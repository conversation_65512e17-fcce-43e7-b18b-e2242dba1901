('D:\\DevelopmentProject\\python\\wxauto\\dist\\办公辅助系统.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON><PERSON>,
 <PERSON>alse,
 ['D:\\DevelopmentProject\\python\\wxauto\\icon.ico'],
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\办公辅助系统.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Python3\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main', 'D:\\DevelopmentProject\\python\\wxauto\\main.py', 'PYSOURCE'),
  ('webview\\lib\\WebBrowserInterop.x64.dll',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\WebBrowserInterop.x64.dll',
   'BINARY'),
  ('webview\\lib\\runtimes\\win-x86\\native\\WebView2Loader.dll',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\runtimes\\win-x86\\native\\WebView2Loader.dll',
   'BINARY'),
  ('webview\\lib\\runtimes\\win-x64\\native\\WebView2Loader.dll',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\runtimes\\win-x64\\native\\WebView2Loader.dll',
   'BINARY'),
  ('webview\\lib\\runtimes\\win-arm64\\native\\WebView2Loader.dll',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\runtimes\\win-arm64\\native\\WebView2Loader.dll',
   'BINARY'),
  ('webview\\lib\\Microsoft.Web.WebView2.Core.dll',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\Microsoft.Web.WebView2.Core.dll',
   'BINARY'),
  ('webview\\lib\\WebBrowserInterop.x86.dll',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\WebBrowserInterop.x86.dll',
   'BINARY'),
  ('webview\\lib\\Microsoft.Web.WebView2.WinForms.dll',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\Microsoft.Web.WebView2.WinForms.dll',
   'BINARY'),
  ('python312.dll', 'D:\\Python3\\python312.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\Python3\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\Python3\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('pythonnet\\runtime\\Python.Runtime.dll',
   'D:\\Python3\\Lib\\site-packages\\pythonnet\\runtime\\Python.Runtime.dll',
   'BINARY'),
  ('clr_loader\\ffi\\dlls\\x86\\ClrLoader.dll',
   'D:\\Python3\\Lib\\site-packages\\clr_loader\\ffi\\dlls\\x86\\ClrLoader.dll',
   'BINARY'),
  ('clr_loader\\ffi\\dlls\\amd64\\ClrLoader.dll',
   'D:\\Python3\\Lib\\site-packages\\clr_loader\\ffi\\dlls\\amd64\\ClrLoader.dll',
   'BINARY'),
  ('unicodedata.pyd', 'D:\\Python3\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python3\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python3\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Python3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'D:\\Python3\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Python3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Python3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python3\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Python3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Python3\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\Python3\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\Python3\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\Python3\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Python3\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\Python3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'D:\\Python3\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'D:\\Python3\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Python3\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\Python3\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python3\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python3\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\Python3\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Python3\\DLLs\\libffi-8.dll', 'BINARY'),
  ('sqlite3.dll', 'D:\\Python3\\DLLs\\sqlite3.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python3\\python3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'D:\\Python3\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\Java\\jre_1.8_291\\bin\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Java\\jre_1.8_291\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('web\\css\\style.css',
   'D:\\DevelopmentProject\\python\\wxauto\\web\\css\\style.css',
   'DATA'),
  ('web\\pages\\home.html',
   'D:\\DevelopmentProject\\python\\wxauto\\web\\pages\\home.html',
   'DATA'),
  ('web\\pages\\index.html',
   'D:\\DevelopmentProject\\python\\wxauto\\web\\pages\\index.html',
   'DATA'),
  ('web\\pages\\talent.html',
   'D:\\DevelopmentProject\\python\\wxauto\\web\\pages\\talent.html',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Python3\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Python3\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('webview\\js\\api.js',
   'D:\\Python3\\Lib\\site-packages\\webview\\js\\api.js',
   'DATA'),
  ('webview\\js\\finish.js',
   'D:\\Python3\\Lib\\site-packages\\webview\\js\\finish.js',
   'DATA'),
  ('webview\\lib\\pywebview-android.jar',
   'D:\\Python3\\Lib\\site-packages\\webview\\lib\\pywebview-android.jar',
   'DATA'),
  ('webview\\js\\lib\\dom_json.js',
   'D:\\Python3\\Lib\\site-packages\\webview\\js\\lib\\dom_json.js',
   'DATA'),
  ('webview\\js\\customize.js',
   'D:\\Python3\\Lib\\site-packages\\webview\\js\\customize.js',
   'DATA'),
  ('webview\\js\\lib\\polyfill.js',
   'D:\\Python3\\Lib\\site-packages\\webview\\js\\lib\\polyfill.js',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Python3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\RECORD',
   'D:\\Python3\\Lib\\site-packages\\numpy-2.3.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.0.dist-info\\LICENSE.txt',
   'D:\\Python3\\Lib\\site-packages\\numpy-2.3.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\DELVEWHEEL',
   'D:\\Python3\\Lib\\site-packages\\numpy-2.3.0.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\METADATA',
   'D:\\Python3\\Lib\\site-packages\\numpy-2.3.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.0.dist-info\\WHEEL',
   'D:\\Python3\\Lib\\site-packages\\numpy-2.3.0.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\entry_points.txt',
   'D:\\Python3\\Lib\\site-packages\\numpy-2.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\INSTALLER',
   'D:\\Python3\\Lib\\site-packages\\numpy-2.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'D:\\DevelopmentProject\\python\\wxauto\\build\\办公辅助系统\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('runw.exe',
   'D:\\Python3\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'D:\\Python3\\python312.dll')
