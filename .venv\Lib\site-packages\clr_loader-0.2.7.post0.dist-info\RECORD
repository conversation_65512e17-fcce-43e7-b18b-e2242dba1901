clr_loader-0.2.7.post0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
clr_loader-0.2.7.post0.dist-info/METADATA,sha256=oD6Ikxtjw_76joOBDg52sY3k_HdSgCLrqy6Qd0rnaRw,1486
clr_loader-0.2.7.post0.dist-info/RECORD,,
clr_loader-0.2.7.post0.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
clr_loader-0.2.7.post0.dist-info/top_level.txt,sha256=LSMza9dbd_kUIS8c3zyusrokszxBeZDnE8JEOR5r484,11
clr_loader/__init__.py,sha256=gky8a-1CBzAGgBiVXdbilFbLJZSNaGNErfWGIyJG8QE,6010
clr_loader/__pycache__/__init__.cpython-312.pyc,,
clr_loader/__pycache__/hostfxr.cpython-312.pyc,,
clr_loader/__pycache__/mono.cpython-312.pyc,,
clr_loader/__pycache__/netfx.cpython-312.pyc,,
clr_loader/__pycache__/types.cpython-312.pyc,,
clr_loader/ffi/__init__.py,sha256=V7HY2mrT0-0iv55FjL08jDbxhpO6fOqWROe_V-XwZFY,2131
clr_loader/ffi/__pycache__/__init__.cpython-312.pyc,,
clr_loader/ffi/__pycache__/hostfxr.cpython-312.pyc,,
clr_loader/ffi/__pycache__/mono.cpython-312.pyc,,
clr_loader/ffi/__pycache__/netfx.cpython-312.pyc,,
clr_loader/ffi/dlls/amd64/ClrLoader.dll,sha256=nrV9U8jgp1-lmr__Du4rBs7yTwE1Fir-YvfA5jsW2e0,8704
clr_loader/ffi/dlls/amd64/ClrLoader.pdb,sha256=AjXnpgBcCL7pSuQvT2dfliR0CQ62MkENJwj5dh1PfQI,5756
clr_loader/ffi/dlls/x86/ClrLoader.dll,sha256=hcmTzTPYC2uj0iA5NmIWGeEglW_X_S-2ZJQM5Jy97Jk,8192
clr_loader/ffi/dlls/x86/ClrLoader.pdb,sha256=DlklaDQMZaArOFmPtn0VaWtSExFrlnhpOtB55oXs86U,5756
clr_loader/ffi/hostfxr.py,sha256=HI7tVtSk_QBh1ryUlFl8ch_odX0ohKHgIwJATPLIVmM,3083
clr_loader/ffi/mono.py,sha256=KR-MApJdxg-aWYZzdxItY7tUsD7kt3jCLRMs4Q6gQ8A,1501
clr_loader/ffi/netfx.py,sha256=Agy7VqlbbgAbOeR8xo21eXRsAscAr-PzPB3-MmsBPoQ,420
clr_loader/hostfxr.py,sha256=o2CqGiXu_02shgpN_MoWW20IdN_hau8dsX9zxqyFEa8,4907
clr_loader/mono.py,sha256=tg6mu_KCbR1IxSVZmoFW6n0CKR2UFRsjnJlK3A3FoTU,6310
clr_loader/netfx.py,sha256=XrVnLcEppTFEmSUCflaSxshNt452EG8ED38yrhfYTe0,1887
clr_loader/types.py,sha256=ZYcBnqwVAiYZovnWRgeVJFrG4eOFrYaqWx1NTxaRu1w,4643
clr_loader/util/__init__.py,sha256=eaBfffDFNkgCs3bCgfCcA-wDWJfTHMMqW_dxTRhdMh0,1028
clr_loader/util/__pycache__/__init__.cpython-312.pyc,,
clr_loader/util/__pycache__/clr_error.cpython-312.pyc,,
clr_loader/util/__pycache__/coreclr_errors.cpython-312.pyc,,
clr_loader/util/__pycache__/find.cpython-312.pyc,,
clr_loader/util/__pycache__/hostfxr_errors.cpython-312.pyc,,
clr_loader/util/__pycache__/runtime_spec.cpython-312.pyc,,
clr_loader/util/clr_error.py,sha256=evOWugwE1FuE5AlPMgvxqnqvu9XykK8CxLXqpMJHKSg,712
clr_loader/util/coreclr_errors.py,sha256=OTMbh_joTtEiHCMlaSGkFnKJaoL98wki7ZsA5_-0Ld4,80037
clr_loader/util/find.py,sha256=CfR4IafQ1Lshx5UCwxw52UTCbPKyAzlOURckQuxHXYI,4989
clr_loader/util/hostfxr_errors.py,sha256=3RmcXNZYm-a6272hTEpbipVxQglilk4pjbDPsbC2I2Y,1937
clr_loader/util/runtime_spec.py,sha256=9XMS7lbdOI_7XJFDC8lF2fohsqvFS-ZWjoRm0I-i-zM,766
