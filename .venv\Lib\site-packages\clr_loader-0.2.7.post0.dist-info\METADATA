Metadata-Version: 2.1
Name: clr_loader
Version: 0.2.7.post0
Summary: Generic pure Python loader for .NET runtimes
Author-email: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
License: MIT
Project-URL: Sources, https://github.com/pythonnet/clr-loader
Project-URL: Documentation, https://pythonnet.github.io/clr-loader/
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Requires-Python: >=3.7
Description-Content-Type: text/markdown
Requires-Dist: cffi >=1.13 ; python_version <= "3.8"
Requires-Dist: cffi >=1.17 ; python_version >= "3.8"

# clr-loader

[![CI](https://github.com/pythonnet/clr-loader/workflows/Python%20Tests/badge.svg)](https://github.com/pythonnet/clr-loader/actions)
[![Pypi](https://img.shields.io/pypi/v/clr-loader.svg)](https://pypi.org/project/clr-loader/)
[![Conda Version](https://img.shields.io/conda/vn/conda-forge/clr_loader.svg)](https://anaconda.org/conda-forge/clr_loader)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

Implements a generic interface for loading one of the CLR (.NET) runtime
implementations and calling simple functions on them.

Documentation is available at https://pythonnet.github.io/clr-loader/.
