2025-08-05 00:06:30 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:06:30 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:693]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:06:31 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:06:32 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:704]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:06:33 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:06:33 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:06:38 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): Ybycy19940805
2025-08-05 00:06:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:716]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:06:49 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:762]  🔍 尝试模糊匹配查找包含'网络查找'的控件...
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:686]  🔄 使用混合模式添加好友: 183000320
2025-08-05 00:07:01 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:693]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:07:02 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:07:03 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:704]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:07:04 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:07:04 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:07:09 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): 183000320
2025-08-05 00:07:11 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:716]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:15:09 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:15:09 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:687]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:15:10 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:15:11 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:698]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:15:12 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:15:12 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:15:16 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): Ybycy19940805
2025-08-05 00:15:18 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:710]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:15:18 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:756]  🔍 尝试模糊匹配查找包含'网络查找'的控件...
2025-08-05 00:15:18 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:775]  模糊匹配失败: (-2147220991, '事件无法调用任何订户', (None, None, None, 0, None))
2025-08-05 00:15:18 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:778]  🔄 未找到'网络查找'按钮，可能直接显示了用户信息，尝试直接查找'添加到通讯录'按钮...
2025-08-05 00:15:18 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:383]  🚀 使用OpenCV超快速检查弹窗...
2025-08-05 00:15:18 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:406]  🔄 回退到uiauto检查...
2025-08-05 00:25:31 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:25:31 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:656]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:25:32 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:25:33 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:667]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:25:34 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:25:34 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:25:39 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): Ybycy19940805
2025-08-05 00:25:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:679]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:25:42 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:716]  ✅ 找到并点击了: 网络查找微信号 (TextControl)
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:383]  🚀 使用OpenCV超快速检查弹窗...
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:214]  ✅ 模板匹配检测到弹窗: templates/user_not_found.png, 匹配度: 1.00
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  ✅ OpenCV检测到'无法找到用户'弹窗
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:260]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.304
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ❌ 匹配度过低: templates/add_friend_button.png, 匹配度: 0.304 < 0.5
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:260]  🔍 模板匹配 templates/confirm_button.png: 最高匹配度 1.000
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:271]  ✅ 模板匹配点击按钮: templates/confirm_button.png, 匹配度: 1.000, 位置: (2297, 698)
2025-08-05 00:25:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:394]  ✅ OpenCV成功关闭弹窗
2025-08-05 00:25:57 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:25:57 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:25:57 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:649]  🔄 使用混合模式添加好友: 183000320
2025-08-05 00:25:57 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:25:58 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:656]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:25:59 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:26:00 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:667]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:26:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:26:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:26:05 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): 183000320
2025-08-05 00:26:07 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:679]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:26:11 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:716]  ✅ 找到并点击了: 网络查找手机/QQ号 (TextControl)
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:383]  🚀 使用OpenCV超快速检查弹窗...
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:214]  ✅ 模板匹配检测到弹窗: templates/user_not_found.png, 匹配度: 1.00
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  ✅ OpenCV检测到'无法找到用户'弹窗
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:260]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.304
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ❌ 匹配度过低: templates/add_friend_button.png, 匹配度: 0.304 < 0.5
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:260]  🔍 模板匹配 templates/confirm_button.png: 最高匹配度 1.000
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:271]  ✅ 模板匹配点击按钮: templates/confirm_button.png, 匹配度: 1.000, 位置: (2297, 698)
2025-08-05 00:26:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:394]  ✅ OpenCV成功关闭弹窗
2025-08-05 00:26:26 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:26:26 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:26:26 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:649]  🔄 使用混合模式添加好友: Baby66ovo
2025-08-05 00:26:26 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:26:26 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:656]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:26:27 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:26:28 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:667]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:26:29 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:26:29 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:26:34 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): Baby66ovo
2025-08-05 00:26:36 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:679]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:26:37 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:716]  ✅ 找到并点击了: 网络查找微信号 (TextControl)
2025-08-05 00:26:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:383]  🚀 使用OpenCV超快速检查弹窗...
2025-08-05 00:26:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:406]  🚫 跳过uiauto检查（太慢），只使用OpenCV
2025-08-05 00:26:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:765]  🎯 步骤3: 查找并点击添加好友按钮...
2025-08-05 00:26:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:770]  🎯 尝试使用OpenCV点击'添加到通讯录'按钮...
2025-08-05 00:26:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:290]  🎯 OpenCV专门查找'添加到通讯录'按钮...
2025-08-05 00:26:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:308]  🔍 加载模板: templates/add_friend_button.png, 尺寸: (36, 129)
2025-08-05 00:26:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:333]  🔍 方法 5: 匹配度 1.000
2025-08-05 00:26:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:333]  🔍 方法 3: 匹配度 1.000
2025-08-05 00:26:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:333]  🔍 方法 1: 匹配度 1.000
2025-08-05 00:26:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:340]  🎯 最佳匹配: 方法 3, 匹配度 1.000
2025-08-05 00:26:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:350]  🎯 准备点击位置: (1959, 454)
2025-08-05 00:26:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:352]  ✅ OpenCV成功点击'添加到通讯录'按钮! 匹配度: 1.000
2025-08-05 00:26:41 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:773]  ✅ OpenCV成功点击了'添加到通讯录'按钮
2025-08-05 00:26:42 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:822]  输入验证消息失败: 'EditControl' object has no attribute 'SetValue'
2025-08-05 00:26:43 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:842]  ✅ 点击了'确定'按钮
2025-08-05 00:26:44 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:850]  🎉 混合模式成功发送好友申请给: Baby66ovo
2025-08-05 00:26:56 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:26:56 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:26:56 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:649]  🔄 使用混合模式添加好友: 84439121790
2025-08-05 00:26:56 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:26:56 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:656]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:26:57 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:26:58 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:667]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:26:59 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:26:59 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:27:04 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): 84439121790
2025-08-05 00:27:06 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:679]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:27:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:725]  🔍 尝试模糊匹配查找包含'网络查找'的控件...
2025-08-05 00:27:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:747]  🔄 未找到'网络查找'按钮，可能直接显示了用户信息，尝试直接查找'添加到通讯录'按钮...
2025-08-05 00:27:15 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:753]  🛑 检测到终止信号，停止混合模式操作
2025-08-05 00:43:10 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:43:10 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:816]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:43:11 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:43:12 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:827]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:43:13 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:43:13 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:380]  🧹 预清理：检查并关闭所有可能的弹窗...
2025-08-05 00:43:13 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  🔍 第1次清理尝试...
2025-08-05 00:43:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:263]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.549
2025-08-05 00:43:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ✅ 模板匹配点击按钮: templates/add_friend_button.png, 匹配度: 0.549, 位置: (985, 401)
2025-08-05 00:43:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:408]  ✅ OpenCV清理了确定按钮 (第1次)
2025-08-05 00:43:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  🔍 第2次清理尝试...
2025-08-05 00:43:14 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:263]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.543
2025-08-05 00:43:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ✅ 模板匹配点击按钮: templates/add_friend_button.png, 匹配度: 0.543, 位置: (987, 401)
2025-08-05 00:43:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:408]  ✅ OpenCV清理了确定按钮 (第2次)
2025-08-05 00:43:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  🔍 第3次清理尝试...
2025-08-05 00:43:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:263]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.543
2025-08-05 00:43:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ✅ 模板匹配点击按钮: templates/add_friend_button.png, 匹配度: 0.543, 位置: (987, 401)
2025-08-05 00:43:15 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:408]  ✅ OpenCV清理了确定按钮 (第3次)
2025-08-05 00:43:16 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:428]  🎉 预清理完成，共清理了 3 个弹窗/按钮
2025-08-05 00:43:16 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:117]  找到搜索框: 搜索
2025-08-05 00:43:21 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:134]  搜索联系人 (标准方法): Ybycy19940805
2025-08-05 00:43:23 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:839]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:885]  🔍 尝试模糊匹配查找包含'网络查找'的控件...
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:907]  🔄 未找到'网络查找'按钮，可能直接显示了用户信息，尝试直接查找'添加到通讯录'按钮...
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:447]  🚀 使用OpenCV超快速检查弹窗...
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:470]  🚫 跳过uiauto检查（太慢），只使用OpenCV
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:925]  🎯 步骤3: 查找并点击添加好友按钮...
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:930]  🎯 尝试使用OpenCV点击'添加到通讯录'按钮...
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:293]  🎯 OpenCV专门查找'添加到通讯录'按钮...
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:311]  🔍 加载模板: templates/add_friend_button.png, 尺寸: (36, 129)
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:336]  🔍 方法 5: 匹配度 0.549
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:336]  🔍 方法 3: 匹配度 0.972
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:336]  🔍 方法 1: 匹配度 0.887
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:343]  🎯 最佳匹配: 方法 3, 匹配度 0.972
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:353]  🎯 准备点击位置: (1642, 252)
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:355]  ✅ OpenCV成功点击'添加到通讯录'按钮! 匹配度: 0.972
2025-08-05 00:43:28 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:933]  ✅ OpenCV成功点击了'添加到通讯录'按钮
2025-08-05 00:43:31 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:980]  📸 混合模式已保存截图: screenshots/add_friend_hybrid_Ybycy19940805_1754325811.png
2025-08-05 00:43:31 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:985]  📝 混合模式准备输入验证消息: 你好我们是牙膏的品牌方
2025-08-05 00:43:33 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:1017]  ✅ 混合模式通过剪贴板输入验证消息: 你好我们是牙膏的品牌方
2025-08-05 00:43:34 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:1028]  📝 混合模式准备输入备注名称: Ybycy19940805
2025-08-05 00:43:48 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:43:48 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:43:48 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:809]  🔄 使用混合模式添加好友: 183000320
2025-08-05 00:43:48 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:43:48 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:816]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:43:49 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:43:50 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:827]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:43:51 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:43:51 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:380]  🧹 预清理：检查并关闭所有可能的弹窗...
2025-08-05 00:43:51 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  🔍 第1次清理尝试...
2025-08-05 00:43:51 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:263]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.549
2025-08-05 00:43:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ✅ 模板匹配点击按钮: templates/add_friend_button.png, 匹配度: 0.549, 位置: (985, 401)
2025-08-05 00:43:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:408]  ✅ OpenCV清理了确定按钮 (第1次)
2025-08-05 00:43:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  🔍 第2次清理尝试...
2025-08-05 00:43:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:263]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.543
2025-08-05 00:43:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ✅ 模板匹配点击按钮: templates/add_friend_button.png, 匹配度: 0.543, 位置: (987, 401)
2025-08-05 00:43:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:408]  ✅ OpenCV清理了确定按钮 (第2次)
2025-08-05 00:43:53 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:391]  🔍 第3次清理尝试...
2025-08-05 00:43:53 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:263]  🔍 模板匹配 templates/add_friend_button.png: 最高匹配度 0.543
2025-08-05 00:43:53 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:274]  ✅ 模板匹配点击按钮: templates/add_friend_button.png, 匹配度: 0.543, 位置: (987, 401)
2025-08-05 00:43:53 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:408]  ✅ OpenCV清理了确定按钮 (第3次)
2025-08-05 00:43:54 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:428]  🎉 预清理完成，共清理了 3 个弹窗/按钮
2025-08-05 00:43:54 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:117]  找到搜索框: 搜索
2025-08-05 00:43:58 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:134]  搜索联系人 (标准方法): 183000320
2025-08-05 00:44:00 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:839]  🎯 步骤2: 使用uiauto完成后续所有操作...
