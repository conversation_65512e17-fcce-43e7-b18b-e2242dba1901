2025-08-05 00:06:30 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:06:30 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:693]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:06:31 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:06:32 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:704]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:06:33 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:06:33 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:06:38 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): Ybycy19940805
2025-08-05 00:06:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:716]  🎯 步骤2: 使用uiauto完成后续所有操作...
2025-08-05 00:06:49 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:762]  🔍 尝试模糊匹配查找包含'网络查找'的控件...
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:70]  找到微信窗口
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:686]  🔄 使用混合模式添加好友: 183000320
2025-08-05 00:07:01 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-05 00:07:01 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:693]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-05 00:07:02 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:07:03 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:704]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-05 00:07:04 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:94]  微信窗口已激活
2025-08-05 00:07:04 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:114]  找到搜索框: 搜索
2025-08-05 00:07:09 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:131]  搜索联系人 (标准方法): 183000320
2025-08-05 00:07:11 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:716]  🎯 步骤2: 使用uiauto完成后续所有操作...
