#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简洁的微信自动化工具 - 基于uiautomation
参考: https://blog.csdn.net/qq_36224726/article/details/142299196
"""

import time
import logging
import uiautomation as auto
from typing import Dict, List


class WeChatAutomation:
    """简洁的微信自动化类 - 只使用uiautomation"""

    def __init__(self):
        """初始化"""
        self.setup_logging()
        self.wechat_window = None
        self.find_wechat_window()

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s",
            handlers=[
                logging.FileHandler("wechat_automation.log", encoding="utf-8"),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger("wechat_automation")

    def find_wechat_window(self) -> bool:
        """查找微信窗口"""
        try:
            # 查找微信主窗口
            self.wechat_window = auto.WindowControl(
                searchDepth=1, 
                ClassName='WeChatMainWndForPC', 
                Name='微信'
            )
            
            if self.wechat_window.Exists(0, 0):
                self.logger.info("✅ 找到微信窗口")
                return True
            else:
                self.logger.error("❌ 未找到微信窗口，请确保微信已打开")
                return False
                
        except Exception as e:
            self.logger.error(f"查找微信窗口失败: {e}")
            return False

    def activate_wechat(self):
        """激活微信窗口"""
        if self.wechat_window:
            try:
                self.wechat_window.SetActive()
                time.sleep(0.5)
                self.logger.info("✅ 微信窗口已激活")
                return True
            except Exception as e:
                self.logger.error(f"激活微信窗口失败: {e}")
                return False
        return False

    def search_user(self, wechat_id: str) -> bool:
        """搜索用户"""
        try:
            self.activate_wechat()
            
            # 点击搜索框
            search_box = self.wechat_window.EditControl(Name='搜索')
            if not search_box.Exists(0, 0):
                self.logger.error("❌ 未找到搜索框")
                return False
            
            # 清空并输入微信号
            search_box.Click()
            time.sleep(0.5)
            search_box.SendKeys('{Ctrl}a')
            time.sleep(0.2)
            search_box.SendKeys(wechat_id)
            time.sleep(2)  # 等待搜索结果
            
            self.logger.info(f"✅ 已搜索用户: {wechat_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"搜索用户失败: {e}")
            return False

    def click_network_search(self) -> bool:
        """点击网络查找"""
        try:
            # 查找网络查找选项
            network_search_patterns = [
                "网络查找微信号",
                "网络查找手机/QQ号", 
                "网络查找",
                "查找微信号"
            ]
            
            for pattern in network_search_patterns:
                try:
                    # 尝试不同的控件类型
                    for control_type in [auto.TextControl, auto.ButtonControl, auto.ListItemControl]:
                        control = control_type(Name=pattern)
                        if control.Exists(0, 0):
                            control.Click()
                            self.logger.info(f"✅ 点击了: {pattern}")
                            time.sleep(2)
                            return True
                except:
                    continue
            
            self.logger.warning("⚠️ 未找到网络查找选项，可能直接显示了用户")
            return True  # 不返回False，继续后续流程
            
        except Exception as e:
            self.logger.error(f"点击网络查找失败: {e}")
            return False

    def add_friend(self, wechat_id: str, verify_msg: str = "", remark: str = "") -> Dict:
        """添加好友 - 核心方法"""
        try:
            self.logger.info(f"🚀 开始添加好友: {wechat_id}")
            
            # 1. 搜索用户
            if not self.search_user(wechat_id):
                return {"success": False, "message": "搜索用户失败"}
            
            # 2. 点击网络查找（如果需要）
            if not self.click_network_search():
                return {"success": False, "message": "网络查找失败"}
            
            # 3. 检查是否出现"无法找到该用户"
            if self.check_user_not_found():
                return {"success": False, "message": "无法找到该用户"}
            
            # 4. 点击添加到通讯录
            if not self.click_add_to_contacts():
                return {"success": False, "message": "未找到添加按钮"}
            
            # 5. 输入验证消息和备注
            if verify_msg:
                self.input_verify_message(verify_msg)
            
            if remark:
                self.input_remark(remark)
            
            # 6. 发送好友申请
            if self.send_friend_request():
                self.logger.info(f"🎉 成功发送好友申请: {wechat_id}")
                return {"success": True, "message": f"成功发送好友申请: {wechat_id}"}
            else:
                return {"success": False, "message": "发送申请失败"}
                
        except Exception as e:
            self.logger.error(f"添加好友失败: {e}")
            return {"success": False, "message": f"添加好友失败: {str(e)}"}

    def check_user_not_found(self) -> bool:
        """检查是否出现'无法找到该用户'弹窗"""
        try:
            not_found_texts = [
                "无法找到该用户",
                "请检查你填写的账号是否正确",
                "用户不存在"
            ]
            
            for text in not_found_texts:
                text_control = auto.TextControl(Name=text)
                if text_control.Exists(0, 0):
                    self.logger.warning(f"⚠️ 发现弹窗: {text}")
                    # 点击确定关闭弹窗
                    self.close_dialog()
                    return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"检查用户不存在弹窗失败: {e}")
            return False

    def close_dialog(self):
        """关闭弹窗"""
        try:
            confirm_buttons = ["确定", "OK", "知道了"]
            for button_name in confirm_buttons:
                button = auto.ButtonControl(Name=button_name)
                if button.Exists(0, 0):
                    button.Click()
                    self.logger.info(f"✅ 关闭弹窗: {button_name}")
                    time.sleep(0.5)
                    return
        except:
            pass

    def click_add_to_contacts(self) -> bool:
        """点击添加到通讯录按钮"""
        try:
            add_button_names = ["添加到通讯录", "添加好友", "添加", "Add to Contacts"]
            
            for button_name in add_button_names:
                button = auto.ButtonControl(Name=button_name)
                if button.Exists(0, 0):
                    button.Click()
                    self.logger.info(f"✅ 点击了: {button_name}")
                    time.sleep(1)
                    return True
            
            self.logger.error("❌ 未找到添加按钮")
            return False
            
        except Exception as e:
            self.logger.error(f"点击添加按钮失败: {e}")
            return False

    def input_verify_message(self, message: str):
        """输入验证消息"""
        try:
            # 查找验证消息输入框
            edit_controls = auto.FindAll(auto.EditControl)
            for edit in edit_controls:
                try:
                    # 通常验证消息框是较大的编辑框
                    rect = edit.BoundingRectangle
                    if rect.height() > 30:  # 高度大于30的可能是验证消息框
                        edit.Click()
                        time.sleep(0.5)
                        edit.SendKeys('{Ctrl}a')
                        time.sleep(0.2)
                        edit.SendKeys(message)
                        self.logger.info(f"✅ 输入验证消息: {message}")
                        return
                except:
                    continue
                    
        except Exception as e:
            self.logger.warning(f"输入验证消息失败: {e}")

    def input_remark(self, remark: str):
        """输入备注"""
        try:
            remark_patterns = ["备注名", "备注", "Remark"]
            for pattern in remark_patterns:
                edit = auto.EditControl(Name=pattern)
                if edit.Exists(0, 0):
                    edit.Click()
                    time.sleep(0.5)
                    edit.SendKeys('{Ctrl}a')
                    time.sleep(0.2)
                    edit.SendKeys(remark)
                    self.logger.info(f"✅ 输入备注: {remark}")
                    return
                    
        except Exception as e:
            self.logger.warning(f"输入备注失败: {e}")

    def send_friend_request(self) -> bool:
        """发送好友申请"""
        try:
            send_buttons = ["确定", "发送", "Send", "OK"]
            
            for button_name in send_buttons:
                button = auto.ButtonControl(Name=button_name)
                if button.Exists(0, 0):
                    button.Click()
                    self.logger.info(f"✅ 点击发送: {button_name}")
                    time.sleep(1)
                    return True
            
            self.logger.error("❌ 未找到发送按钮")
            return False
            
        except Exception as e:
            self.logger.error(f"发送申请失败: {e}")
            return False

    def batch_add_friends(self, friend_list: List[Dict]) -> List[Dict]:
        """批量添加好友"""
        results = []
        
        for friend_data in friend_list:
            wechat_id = friend_data.get('wechat_id', '')
            verify_msg = friend_data.get('verify_msg', '')
            remark = friend_data.get('remark', '')
            
            if not wechat_id:
                continue
            
            result = self.add_friend(wechat_id, verify_msg, remark)
            results.append({
                'wechat_id': wechat_id,
                'result': result
            })
            
            # 添加间隔，避免操作过快
            time.sleep(3)
        
        return results


def main():
    """测试函数"""
    wechat = WeChatAutomation()
    
    if not wechat.wechat_window:
        print("请先打开微信客户端")
        return
    
    # 测试添加单个好友
    result = wechat.add_friend(
        wechat_id="test123",
        verify_msg="你好，我想加你为好友",
        remark="新朋友"
    )
    print(f"添加结果: {result}")


if __name__ == "__main__":
    main()
