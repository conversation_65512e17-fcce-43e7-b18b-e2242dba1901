#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断uiautomation安装和导入问题
"""

import sys
import os

def diagnose_uiautomation():
    """诊断uiautomation问题"""
    print("="*60)
    print("uiautomation诊断工具")
    print("="*60)
    
    # 1. 检查Python版本
    print(f"1. Python版本: {sys.version}")
    print(f"   Python路径: {sys.executable}")
    
    # 2. 检查Python路径
    print(f"\n2. Python模块搜索路径:")
    for i, path in enumerate(sys.path, 1):
        print(f"   {i}. {path}")
    
    # 3. 尝试导入uiautomation
    print(f"\n3. 尝试导入uiautomation:")
    try:
        import uiautomation
        print("   ✅ uiautomation导入成功")
        
        # 检查版本
        version = getattr(uiautomation, '__version__', 'Unknown')
        print(f"   📦 版本: {version}")
        
        # 检查安装位置
        module_file = getattr(uiautomation, '__file__', 'Unknown')
        print(f"   📁 安装位置: {module_file}")
        
        # 检查关键类
        try:
            window = uiautomation.WindowControl
            print("   ✅ WindowControl类可用")
        except AttributeError as e:
            print(f"   ❌ WindowControl类不可用: {e}")
            
    except ImportError as e:
        print(f"   ❌ uiautomation导入失败: {e}")
        print("   💡 解决方案:")
        print("      pip install uiautomation")
        return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False
    
    # 4. 测试基本功能
    print(f"\n4. 测试基本功能:")
    try:
        import uiautomation as auto
        
        # 测试获取桌面
        desktop = auto.GetRootControl()
        print("   ✅ 获取桌面成功")
        
        # 测试查找窗口
        windows = desktop.GetChildren()
        print(f"   ✅ 找到 {len(windows)} 个顶级窗口")
        
        # 测试查找微信窗口
        wechat_window = auto.WindowControl(searchDepth=1, ClassName="WeChatMainWndForPC")
        if wechat_window.Exists(0, 0):
            print("   ✅ 找到微信窗口")
            print(f"      窗口名称: {wechat_window.Name}")
            print(f"      窗口类名: {wechat_window.ClassName}")
        else:
            print("   ⚠️ 未找到微信窗口（请确保微信已打开）")
            
    except Exception as e:
        print(f"   ❌ 基本功能测试失败: {e}")
        return False
    
    # 5. 检查依赖
    print(f"\n5. 检查相关依赖:")
    dependencies = ['win32gui', 'win32api', 'win32con', 'comtypes']
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✅ {dep} 可用")
        except ImportError:
            print(f"   ⚠️ {dep} 不可用（可能影响功能）")
    
    # 6. 测试导入我们的模块
    print(f"\n6. 测试导入自定义模块:")
    try:
        from wechat_uiautomation import WeChatUIAutomation
        print("   ✅ wechat_uiautomation导入成功")
        
        # 尝试创建实例
        try:
            wechat = WeChatUIAutomation()
            print("   ✅ WeChatUIAutomation实例创建成功")
        except Exception as e:
            print(f"   ❌ 实例创建失败: {e}")
            
    except ImportError as e:
        print(f"   ❌ wechat_uiautomation导入失败: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
    
    print(f"\n" + "="*60)
    print("诊断完成")
    print("="*60)
    
    return True

def fix_suggestions():
    """提供修复建议"""
    print("\n💡 常见问题解决方案:")
    print("="*40)
    print("1. 如果uiautomation导入失败:")
    print("   pip uninstall uiautomation")
    print("   pip install uiautomation")
    print("")
    print("2. 如果版本冲突:")
    print("   pip install --upgrade uiautomation")
    print("")
    print("3. 如果权限问题:")
    print("   以管理员身份运行命令提示符")
    print("   pip install --user uiautomation")
    print("")
    print("4. 如果找不到微信窗口:")
    print("   确保微信PC版已打开并登录")
    print("   确保微信窗口可见")
    print("")
    print("5. 如果依赖缺失:")
    print("   pip install pywin32")
    print("   pip install comtypes")

if __name__ == "__main__":
    success = diagnose_uiautomation()
    if not success:
        fix_suggestions()
