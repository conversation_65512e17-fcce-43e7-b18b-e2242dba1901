from typing import Dict, Optional

from .clr_error import ClrErro<PERSON>


def get_coreclr_error(hresult: int) -> Optional[ClrError]:
    name = SymbolicName.get(hresult)
    if not name:
        return None

    return ClrError(
        hresult=hresult,
        name=name,
        message=Message.get(hresult),
        comment=Comment.get(hresult),
    )


Comment: Dict[int, str] = {}
SymbolicName: Dict[int, str] = {}
Message: Dict[int, str] = {}


if __name__ == "__main__":
    from sys import argv, exit
    from xml.etree import ElementTree

    if len(argv) < 2:
        url = "https://raw.githubusercontent.com/dotnet/coreclr/master/src/inc/corerror.xml"
        print(
            f"Please download corerror.xml from {url} and pass as an argument to this script"
        )
        exit(1)

    tree = ElementTree.parse(argv[1])

    print(f"Parsed {argv[1]}, updating {__file__}...")

    marker = "# == Autogenerated from corerror.xml =="

    with open(__file__, "r") as f:
        current = f.read()
        before, _, _ = current.rpartition(marker)

    self = open(__file__, "w")
    self.seek(0)
    self.write(before)
    self.write(marker)
    self.write("\n\n")

    for row in tree.findall(".//HRESULT"):
        try:
            numeric_value = int(row.attrib["NumericValue"], base=16)

            for child in row:
                if child.text:
                    text = child.text.strip(' "')
                    self.write(f"{child.tag}[{hex(numeric_value)}] = {repr(text)}\n")
        except ValueError:
            print("Failed to process numeric value:", numeric_value)

    self.close()

# fmt: off
# flake8: noqa
# == Autogenerated from corerror.xml ==

SymbolicName[0x131106] = 'CLDB_S_TRUNCATION'
Comment[0x131106] = 'STATUS: Data value was truncated.'
SymbolicName[0x131197] = 'META_S_DUPLICATE'
Comment[0x131197] = 'Attempt to define an object that already exists in valid scenerios.'
SymbolicName[0x13130b] = 'CORDBG_S_BAD_START_SEQUENCE_POINT'
Comment[0x13130b] = 'Attempt to SetIP not at a sequence point sequence point.'
SymbolicName[0x13130c] = 'CORDBG_S_BAD_END_SEQUENCE_POINT'
Comment[0x13130c] = 'Attempt to SetIP when not going to a sequence point. If both this and CORDBG_E_BAD_START_SEQUENCE_POINT are true, only CORDBG_E_BAD_START_SEQUENCE_POINT will be reported.'
SymbolicName[0x131316] = 'CORDBG_S_FUNC_EVAL_HAS_NO_RESULT'
Comment[0x131316] = 'Some Func evals will lack a return value,'
SymbolicName[0x131317] = 'CORDBG_S_VALUE_POINTS_TO_VOID'
Comment[0x131317] = "The Debugging API doesn't support dereferencing void pointers."
SymbolicName[0x131319] = 'CORDBG_S_FUNC_EVAL_ABORTED'
Comment[0x131319] = 'The func eval completed, but was aborted.'
SymbolicName[0x131324] = 'CORDBG_S_AT_END_OF_STACK'
Message[0x131324] = 'The stack walk has reached the end of the stack.  There are no more frames to walk.'
Comment[0x131324] = 'The stack walk has reached the end of the stack.  There are no more frames to walk.'
SymbolicName[0x131c13] = 'CORDBG_S_NOT_ALL_BITS_SET'
Comment[0x131c13] = 'Not all bits specified were successfully applied'
SymbolicName[0x80131001] = 'CEE_E_CVTRES_NOT_FOUND'
Message[0x80131001] = 'cvtres.exe not found.'
Comment[0x80131001] = 'cannot find cvtres.exe'
SymbolicName[0x80131013] = 'COR_E_TYPEUNLOADED'
Message[0x80131013] = 'Type has been unloaded.'
Comment[0x80131013] = 'The type had been unloaded.'
SymbolicName[0x80131014] = 'COR_E_APPDOMAINUNLOADED'
Message[0x80131014] = 'Attempted to access an unloaded appdomain.'
Comment[0x80131014] = 'access unloaded appdomain'
SymbolicName[0x80131015] = 'COR_E_CANNOTUNLOADAPPDOMAIN'
Message[0x80131015] = 'Error while unloading appdomain.'
Comment[0x80131015] = 'Error while unloading an appdomain'
SymbolicName[0x80131016] = 'MSEE_E_ASSEMBLYLOADINPROGRESS'
Message[0x80131016] = 'Assembly is still being loaded.'
Comment[0x80131016] = 'Assembly is being currently being loaded'
SymbolicName[0x80131018] = 'COR_E_ASSEMBLYEXPECTED'
Message[0x80131018] = 'The module was expected to contain an assembly manifest.'
Comment[0x80131018] = 'The module was expected to contain an assembly manifest.'
SymbolicName[0x80131019] = 'COR_E_FIXUPSINEXE'
Message[0x80131019] = 'Attempt to load an unverifiable executable with fixups (IAT with more than 2 sections or a TLS section.)'
Comment[0x80131019] = 'Attempt to load an unverifiable exe with fixups (IAT with more than 2 sections or a TLS section)'
SymbolicName[0x8013101b] = 'COR_E_NEWER_RUNTIME'
Message[0x8013101b] = 'This assembly is built by a runtime newer than the currently loaded runtime and cannot be loaded.'
Comment[0x8013101b] = 'The assembly is built by a runtime newer than the currently loaded runtime, and cannot be loaded.'
SymbolicName[0x8013101e] = 'COR_E_MULTIMODULEASSEMBLIESDIALLOWED'
Message[0x8013101e] = 'The module cannot be loaded because only single file assemblies are supported.'
Comment[0x8013101e] = 'The module cannot be loaded because only single file assemblies are supported.'
SymbolicName[0x80131020] = 'HOST_E_DEADLOCK'
Message[0x80131020] = 'Host detected a deadlock on a blocking operation.'
Comment[0x80131020] = 'Host detects deadlock on a blocking operation'
SymbolicName[0x80131022] = 'HOST_E_INVALIDOPERATION'
Message[0x80131022] = 'Invalid operation.'
Comment[0x80131022] = 'The operation is invalid'
SymbolicName[0x80131023] = 'HOST_E_CLRNOTAVAILABLE'
Message[0x80131023] = 'CLR has been disabled due to unrecoverable error.'
Comment[0x80131023] = 'CLR has been disabled due to unrecoverable error'
SymbolicName[0x80131027] = 'HOST_E_EXITPROCESS_THREADABORT'
Message[0x80131027] = 'Process exited due to ThreadAbort escalation.'
Comment[0x80131027] = 'ExitProcess due to ThreadAbort escalation'
SymbolicName[0x80131028] = 'HOST_E_EXITPROCESS_ADUNLOAD'
Message[0x80131028] = 'Process exited due to AD Unload escalation.'
Comment[0x80131028] = 'ExitProcess due to AD Unload escalation'
SymbolicName[0x80131029] = 'HOST_E_EXITPROCESS_TIMEOUT'
Message[0x80131029] = 'Process exited due to Timeout escalation.'
Comment[0x80131029] = 'ExitProcess due to Timeout escalation'
SymbolicName[0x8013102a] = 'HOST_E_EXITPROCESS_OUTOFMEMORY'
Message[0x8013102a] = 'Process exited due to OutOfMemory escalation.'
Comment[0x8013102a] = 'ExitProcess due to OutOfMemory escalation'
SymbolicName[0x80131039] = 'COR_E_MODULE_HASH_CHECK_FAILED'
Message[0x80131039] = "The check of the module's hash failed."
Comment[0x80131039] = "The check of the module's hash failed."
SymbolicName[0x80131040] = 'FUSION_E_REF_DEF_MISMATCH'
Message[0x80131040] = "The located assembly's manifest definition does not match the assembly reference."
Comment[0x80131040] = "The located assembly's manifest definition does not match the assembly reference."
SymbolicName[0x80131041] = 'FUSION_E_INVALID_PRIVATE_ASM_LOCATION'
Message[0x80131041] = 'The private assembly was located outside the appbase directory.'
Comment[0x80131041] = 'The private assembly was located outside the appbase directory.'
SymbolicName[0x80131042] = 'FUSION_E_ASM_MODULE_MISSING'
Message[0x80131042] = 'A module specified in the manifest was not found.'
Comment[0x80131042] = 'A module specified in the manifest was not found.'
SymbolicName[0x80131044] = 'FUSION_E_PRIVATE_ASM_DISALLOWED'
Message[0x80131044] = 'A strongly-named assembly is required.'
Comment[0x80131044] = 'A strongly-named assembly is required.'
SymbolicName[0x80131045] = 'FUSION_E_SIGNATURE_CHECK_FAILED'
Message[0x80131045] = 'Strong name signature could not be verified.  The assembly may have been tampered with, or it was delay signed but not fully signed with the correct private key.'
Comment[0x80131045] = 'The check of the signature failed.'
SymbolicName[0x80131047] = 'FUSION_E_INVALID_NAME'
Message[0x80131047] = 'The given assembly name or codebase was invalid.'
Comment[0x80131047] = 'The given assembly name or codebase was invalid.'
SymbolicName[0x80131048] = 'FUSION_E_CODE_DOWNLOAD_DISABLED'
Message[0x80131048] = 'HTTP download of assemblies has been disabled for this appdomain.'
Comment[0x80131048] = 'HTTP download of assemblies has been disabled for this appdomain.'
SymbolicName[0x80131050] = 'FUSION_E_HOST_GAC_ASM_MISMATCH'
Message[0x80131050] = 'Assembly in host store has a different signature than assembly in GAC.'
Comment[0x80131050] = 'Assembly in host store has a different signature than assembly in GAC'
SymbolicName[0x80131051] = 'FUSION_E_LOADFROM_BLOCKED'
Message[0x80131051] = 'LoadFrom(), LoadFile(), Load(byte[]) and LoadModule() have been disabled by the host.'
Comment[0x80131051] = "Hosted environment doesn't permit loading by location"
SymbolicName[0x80131052] = 'FUSION_E_CACHEFILE_FAILED'
Message[0x80131052] = 'Failed to add file to AppDomain cache.'
Comment[0x80131052] = 'Failed to add file to AppDomain cache'
SymbolicName[0x80131053] = 'FUSION_E_APP_DOMAIN_LOCKED'
Message[0x80131053] = 'The requested assembly version conflicts with what is already bound in the app domain or specified in the manifest.'
Comment[0x80131053] = 'The requested assembly version conflicts with what is already bound in the app domain or specified in the manifest'
SymbolicName[0x80131054] = 'FUSION_E_CONFIGURATION_ERROR'
Message[0x80131054] = "The requested assembly name was neither found in the GAC nor in the manifest or the manifest's specified location is wrong."
Comment[0x80131054] = "The requested assembly name was neither found in the GAC nor in the manifest or the manifest's specified location is wrong"
SymbolicName[0x80131055] = 'FUSION_E_MANIFEST_PARSE_ERROR'
Message[0x80131055] = 'Unexpected error while parsing the specified manifest.'
Comment[0x80131055] = 'Unexpected error while parsing the specified manifest'
SymbolicName[0x80131058] = 'COR_E_LOADING_REFERENCE_ASSEMBLY'
Message[0x80131058] = 'Reference assemblies should not be loaded for execution.  They can only be loaded in the Reflection-only loader context.'
Comment[0x80131058] = 'Reference assemblies should not be loaded for execution.  They can only be loaded in the Reflection-only loader context.'
SymbolicName[0x80131059] = 'COR_E_NI_AND_RUNTIME_VERSION_MISMATCH'
Message[0x80131059] = 'The native image could not be loaded, because it was generated for use by a different version of the runtime.'
Comment[0x80131059] = 'The native image could not be loaded, because it was generated for use by a different version of the runtime.'
SymbolicName[0x80131069] = 'COR_E_LOADING_WINMD_REFERENCE_ASSEMBLY'
Message[0x80131069] = 'Contract Windows Runtime assemblies cannot be loaded for execution.  Make sure your application only contains non-contract Windows Runtime assemblies.'
Comment[0x80131069] = 'Contract Windows Runtime assemblies cannot be loaded for execution.  Make sure your application only contains non-contract Windows Runtime assemblies'
SymbolicName[0x8013106a] = 'COR_E_AMBIGUOUSIMPLEMENTATION'
Message[0x8013106a] = 'Ambiguous implementation found.'
Comment[0x8013106a] = 'Ambiguous implementation found'
SymbolicName[0x80131100] = 'CLDB_E_FILE_BADREAD'
Message[0x80131100] = 'Error occurred during a read.'
Comment[0x80131100] = 'Error occurred during a read.'
SymbolicName[0x80131101] = 'CLDB_E_FILE_BADWRITE'
Message[0x80131101] = 'Error occurred during a write.'
Comment[0x80131101] = 'Error occurred during a write.'
SymbolicName[0x80131107] = 'CLDB_E_FILE_OLDVER'
Message[0x80131107] = 'Old version error.'
Comment[0x80131107] = 'Old version error.'
SymbolicName[0x8013110a] = 'CLDB_E_SMDUPLICATE'
Message[0x8013110a] = 'Create of shared memory failed.  A memory mapping of the same name already exists.'
Comment[0x8013110a] = 'Create of shared memory failed.  A memory mapping of the same name already exists.'
SymbolicName[0x8013110b] = 'CLDB_E_NO_DATA'
Message[0x8013110b] = 'No .CLB data in the memory or stream.'
Comment[0x8013110b] = "There isn't .CLB data in the memory or stream."
SymbolicName[0x8013110d] = 'CLDB_E_INCOMPATIBLE'
Message[0x8013110d] = 'Importing scope is not compatible with the emitting scope.'
Comment[0x8013110d] = 'The importing scope is not comptabile with the emitting scope'
SymbolicName[0x8013110e] = 'CLDB_E_FILE_CORRUPT'
Message[0x8013110e] = 'File is corrupt.'
Comment[0x8013110e] = 'File is corrupt.'
SymbolicName[0x80131110] = 'CLDB_E_BADUPDATEMODE'
Message[0x80131110] = 'Cannot open a incrementally build scope for full update.'
Comment[0x80131110] = 'cannot open a incrementally build scope for full update'
SymbolicName[0x80131124] = 'CLDB_E_INDEX_NOTFOUND'
Message[0x80131124] = 'Index not found.'
Comment[0x80131124] = 'Index %s not found.'
SymbolicName[0x80131130] = 'CLDB_E_RECORD_NOTFOUND'
Message[0x80131130] = 'Record not found on lookup.'
Comment[0x80131130] = "Record wasn't found on lookup."
SymbolicName[0x80131135] = 'CLDB_E_RECORD_OUTOFORDER'
Message[0x80131135] = 'Record is emitted out of order.'
Comment[0x80131135] = 'Record is emitted out of order.'
SymbolicName[0x80131154] = 'CLDB_E_TOO_BIG'
Message[0x80131154] = 'A blob or string was too big.'
Comment[0x80131154] = 'A blob or string was too big.'
SymbolicName[0x8013115f] = 'META_E_INVALID_TOKEN_TYPE'
Message[0x8013115f] = 'A token of the wrong type passed to a metadata function.'
Comment[0x8013115f] = 'A token of the wrong type passed to a metadata function.'
SymbolicName[0x80131165] = 'TLBX_E_LIBNOTREGISTERED'
Message[0x80131165] = 'Typelib export: Type library is not registered.'
Comment[0x80131165] = 'Typelib export: type library is not registered.'
SymbolicName[0x8013118a] = 'META_E_BADMETADATA'
Message[0x8013118a] = 'Merge: Inconsistency in meta data import scope.'
Comment[0x8013118a] = 'Merge: Inconsistency in meta data import scope'
SymbolicName[0x80131192] = 'META_E_BAD_SIGNATURE'
Message[0x80131192] = 'Bad binary signature.'
Comment[0x80131192] = 'Bad binary signature'
SymbolicName[0x80131193] = 'META_E_BAD_INPUT_PARAMETER'
Message[0x80131193] = 'Bad input parameters.'
Comment[0x80131193] = 'Bad input parameters'
SymbolicName[0x80131196] = 'META_E_CANNOTRESOLVETYPEREF'
Message[0x80131196] = 'Cannot resolve typeref.'
Comment[0x80131196] = 'Cannot resolve typeref'
SymbolicName[0x80131198] = 'META_E_STRINGSPACE_FULL'
Message[0x80131198] = 'No logical space left to create more user strings.'
Comment[0x80131198] = 'No logical space left to create more user strings.'
SymbolicName[0x8013119a] = 'META_E_HAS_UNMARKALL'
Message[0x8013119a] = 'Unmark all has been called already.'
Comment[0x8013119a] = 'Unmark all has been called already'
SymbolicName[0x8013119b] = 'META_E_MUST_CALL_UNMARKALL'
Message[0x8013119b] = 'Must call UnmarkAll first before marking.'
Comment[0x8013119b] = 'Must call UnmarkAll first before marking.'
SymbolicName[0x801311c0] = 'META_E_CA_INVALID_TARGET'
Message[0x801311c0] = 'Known custom attribute on invalid target.'
Comment[0x801311c0] = 'Known custom attribute on invalid target.'
SymbolicName[0x801311c1] = 'META_E_CA_INVALID_VALUE'
Message[0x801311c1] = 'Known custom attribute had invalid value.'
Comment[0x801311c1] = 'Known custom attribute had invalid value.'
SymbolicName[0x801311c2] = 'META_E_CA_INVALID_BLOB'
Message[0x801311c2] = 'Known custom attribute blob has bad format.'
Comment[0x801311c2] = 'Known custom attribute blob is bad format.'
SymbolicName[0x801311c3] = 'META_E_CA_REPEATED_ARG'
Message[0x801311c3] = 'Known custom attribute blob has repeated named argument.'
Comment[0x801311c3] = 'Known custom attribute blob has repeated named argument.'
SymbolicName[0x801311c4] = 'META_E_CA_UNKNOWN_ARGUMENT'
Message[0x801311c4] = 'Known custom attribute named argument not recognized.'
Comment[0x801311c4] = 'Known custom attrubte named arg not recognized.'
SymbolicName[0x801311c7] = 'META_E_CA_UNEXPECTED_TYPE'
Message[0x801311c7] = 'Known attribute parser found unexpected type.'
Comment[0x801311c7] = 'Known attribute parser found unexpected type.'
SymbolicName[0x801311c8] = 'META_E_CA_INVALID_ARGTYPE'
Message[0x801311c8] = 'Known attribute parser only handles fields, not properties.'
Comment[0x801311c8] = 'Known attribute parser only handles fields -- no properties.'
SymbolicName[0x801311c9] = 'META_E_CA_INVALID_ARG_FOR_TYPE'
Message[0x801311c9] = 'Known attribute parser found an argument that is invalid for the object it is applied to.'
Comment[0x801311c9] = 'Known attribute parser found an argument that is invalid for the object it is applied to.'
SymbolicName[0x801311ca] = 'META_E_CA_INVALID_UUID'
Message[0x801311ca] = 'The format of the UUID was invalid.'
Comment[0x801311ca] = 'The format of the UUID was invalid.'
SymbolicName[0x801311cb] = 'META_E_CA_INVALID_MARSHALAS_FIELDS'
Message[0x801311cb] = 'The MarshalAs attribute has fields set that are not valid for the specified unmanaged type.'
Comment[0x801311cb] = 'The MarshalAs attribute has fields set that are not valid for the specified unmanaged type.'
SymbolicName[0x801311cc] = 'META_E_CA_NT_FIELDONLY'
Message[0x801311cc] = 'The specified unmanaged type is only valid on fields.'
Comment[0x801311cc] = 'The specified unmanaged type is only valid on fields.'
SymbolicName[0x801311cd] = 'META_E_CA_NEGATIVE_PARAMINDEX'
Message[0x801311cd] = 'The parameter index cannot be negative.'
Comment[0x801311cd] = 'The parameter index cannot be negative.'
SymbolicName[0x801311cf] = 'META_E_CA_NEGATIVE_CONSTSIZE'
Message[0x801311cf] = 'The constant size cannot be negative.'
Comment[0x801311cf] = 'The constant size cannot be negative.'
SymbolicName[0x801311d0] = 'META_E_CA_FIXEDSTR_SIZE_REQUIRED'
Message[0x801311d0] = 'A fixed string requires a size.'
Comment[0x801311d0] = 'A fixed string requires a size.'
SymbolicName[0x801311d1] = 'META_E_CA_CUSTMARSH_TYPE_REQUIRED'
Message[0x801311d1] = 'A custom marshaler requires the custom marshaler type.'
Comment[0x801311d1] = 'A custom marshaler requires the custom marshaler type.'
SymbolicName[0x801311d4] = 'META_E_NOT_IN_ENC_MODE'
Message[0x801311d4] = 'SaveDelta was called without being in EnC mode.'
Comment[0x801311d4] = 'SaveDelta was called without being in EnC mode'
SymbolicName[0x801311e5] = 'META_E_CA_BAD_FRIENDS_ARGS'
Message[0x801311e5] = "InternalsVisibleTo can't have a version, culture, or processor architecture."
Comment[0x801311e5] = "InternalsVisibleTo can't have a version, culture, or processor architecture."
SymbolicName[0x801311e6] = 'META_E_CA_FRIENDS_SN_REQUIRED'
Comment[0x801311e6] = 'Strong-name signed assemblies can only grant friend access to strong name-signed assemblies'
SymbolicName[0x80131203] = 'VLDTR_E_RID_OUTOFRANGE'
Message[0x80131203] = 'Rid is out of range.'
Comment[0x80131203] = 'Rid is out of range.'
SymbolicName[0x80131206] = 'VLDTR_E_STRING_INVALID'
Message[0x80131206] = 'String offset is invalid.'
Comment[0x80131206] = 'String offset is invalid.'
SymbolicName[0x80131207] = 'VLDTR_E_GUID_INVALID'
Message[0x80131207] = 'GUID offset is invalid.'
Comment[0x80131207] = 'GUID offset is invalid.'
SymbolicName[0x80131208] = 'VLDTR_E_BLOB_INVALID'
Message[0x80131208] = 'Blob offset if invalid.'
Comment[0x80131208] = 'Blob offset if invalid.'
SymbolicName[0x80131224] = 'VLDTR_E_MR_BADCALLINGCONV'
Message[0x80131224] = 'MemberRef has invalid calling convention.'
Comment[0x80131224] = 'MemberRef has invalid calling convention.'
SymbolicName[0x80131237] = 'VLDTR_E_SIGNULL'
Message[0x80131237] = 'Signature specified is zero-sized.'
Comment[0x80131237] = 'Signature specified is zero-sized.'
SymbolicName[0x80131239] = 'VLDTR_E_MD_BADCALLINGCONV'
Message[0x80131239] = 'Method signature has invalid calling convention.'
Comment[0x80131239] = 'Method signature has invalid calling convention.'
SymbolicName[0x8013123a] = 'VLDTR_E_MD_THISSTATIC'
Message[0x8013123a] = 'Method is marked static but has HASTHIS/EXPLICITTHIS set on the calling convention.'
Comment[0x8013123a] = 'Method is marked static but has HASTHIS/EXPLICITTHIS set on the calling convention.'
SymbolicName[0x8013123b] = 'VLDTR_E_MD_NOTTHISNOTSTATIC'
Message[0x8013123b] = 'Method is not marked static but is not HASTHIS or EXPLICITTHIS.'
Comment[0x8013123b] = 'Method is not marked static but is not HASTHIS/EXPLICITTHIS.'
SymbolicName[0x8013123c] = 'VLDTR_E_MD_NOARGCNT'
Message[0x8013123c] = 'Method signature is missing the argument count.'
Comment[0x8013123c] = 'Method signature is missing the argument count.'
SymbolicName[0x8013123d] = 'VLDTR_E_SIG_MISSELTYPE'
Message[0x8013123d] = 'Signature missing element type.'
Comment[0x8013123d] = 'Signature missing element type.'
SymbolicName[0x8013123e] = 'VLDTR_E_SIG_MISSTKN'
Message[0x8013123e] = 'Signature missing token.'
Comment[0x8013123e] = 'Signature missing token.'
SymbolicName[0x8013123f] = 'VLDTR_E_SIG_TKNBAD'
Message[0x8013123f] = 'Signature has bad token.'
Comment[0x8013123f] = 'Signature has bad token.'
SymbolicName[0x80131240] = 'VLDTR_E_SIG_MISSFPTR'
Message[0x80131240] = 'Signature is missing function pointer.'
Comment[0x80131240] = 'Signature is missing function pointer.'
SymbolicName[0x80131241] = 'VLDTR_E_SIG_MISSFPTRARGCNT'
Message[0x80131241] = 'Signature has function pointer missing argument count.'
Comment[0x80131241] = 'Signature has function pointer missing argument count.'
SymbolicName[0x80131242] = 'VLDTR_E_SIG_MISSRANK'
Message[0x80131242] = 'Signature is missing rank specification.'
Comment[0x80131242] = 'Signature is missing rank specification.'
SymbolicName[0x80131243] = 'VLDTR_E_SIG_MISSNSIZE'
Message[0x80131243] = 'Signature is missing count of sized dimensions.'
Comment[0x80131243] = 'Signature is missing count of sized dimensions.'
SymbolicName[0x80131244] = 'VLDTR_E_SIG_MISSSIZE'
Message[0x80131244] = 'Signature is missing size of dimension.'
Comment[0x80131244] = 'Signature is missing size of dimension.'
SymbolicName[0x80131245] = 'VLDTR_E_SIG_MISSNLBND'
Message[0x80131245] = 'Signature is missing count of lower bounds.'
Comment[0x80131245] = 'Signature is missing count of lower bounds.'
SymbolicName[0x80131246] = 'VLDTR_E_SIG_MISSLBND'
Message[0x80131246] = 'Signature is missing a lower bound.'
Comment[0x80131246] = 'Signature is missing a lower bound.'
SymbolicName[0x80131247] = 'VLDTR_E_SIG_BADELTYPE'
Message[0x80131247] = 'Signature has bad element type.'
Comment[0x80131247] = 'Signature has bad element type.'
SymbolicName[0x80131256] = 'VLDTR_E_TD_ENCLNOTNESTED'
Message[0x80131256] = 'TypeDef not nested has encloser.'
Comment[0x80131256] = 'TypeDef not nested has encloser.'
SymbolicName[0x80131277] = 'VLDTR_E_FMD_PINVOKENOTSTATIC'
Message[0x80131277] = 'Field or method is PInvoke but is not marked Static.'
Comment[0x80131277] = 'Field/method is PInvoke but is not marked Static.'
SymbolicName[0x801312df] = 'VLDTR_E_SIG_SENTINMETHODDEF'
Message[0x801312df] = 'E_T_SENTINEL in MethodDef signature.'
Comment[0x801312df] = 'E_T_SENTINEL in MethodDef signature'
SymbolicName[0x801312e0] = 'VLDTR_E_SIG_SENTMUSTVARARG'
Message[0x801312e0] = 'E_T_SENTINEL <=> VARARG.'
Comment[0x801312e0] = 'E_T_SENTINEL <=> VARARG'
SymbolicName[0x801312e1] = 'VLDTR_E_SIG_MULTSENTINELS'
Message[0x801312e1] = 'Multiple E_T_SENTINELs.'
Comment[0x801312e1] = 'Multiple E_T_SENTINELs'
SymbolicName[0x801312e3] = 'VLDTR_E_SIG_MISSARG'
Message[0x801312e3] = 'Signature missing argument.'
Comment[0x801312e3] = 'Signature missing argument'
SymbolicName[0x801312e4] = 'VLDTR_E_SIG_BYREFINFIELD'
Message[0x801312e4] = 'Field of ByRef type.'
Comment[0x801312e4] = 'Field of ByRef type'
SymbolicName[0x80131300] = 'CORDBG_E_UNRECOVERABLE_ERROR'
Message[0x80131300] = 'Unrecoverable API error.'
Comment[0x80131300] = 'Unrecoverable API error.'
SymbolicName[0x80131301] = 'CORDBG_E_PROCESS_TERMINATED'
Message[0x80131301] = 'Process was terminated.'
Comment[0x80131301] = 'Process was terminated.'
SymbolicName[0x80131302] = 'CORDBG_E_PROCESS_NOT_SYNCHRONIZED'
Message[0x80131302] = 'Process not synchronized.'
Comment[0x80131302] = 'Process not synchronized.'
SymbolicName[0x80131303] = 'CORDBG_E_CLASS_NOT_LOADED'
Message[0x80131303] = 'A class is not loaded.'
Comment[0x80131303] = 'A class is not loaded.'
SymbolicName[0x80131304] = 'CORDBG_E_IL_VAR_NOT_AVAILABLE'
Message[0x80131304] = 'An IL variable is not available at the current native IP.'
Comment[0x80131304] = 'An IL variable is not available at the'
SymbolicName[0x80131305] = 'CORDBG_E_BAD_REFERENCE_VALUE'
Message[0x80131305] = 'A reference value was found to be bad during dereferencing.'
Comment[0x80131305] = 'A reference value was found to be bad'
SymbolicName[0x80131306] = 'CORDBG_E_FIELD_NOT_AVAILABLE'
Message[0x80131306] = 'A field in a class is not available, because the runtime optimized it away.'
Comment[0x80131306] = 'A field in a class is not available,'
SymbolicName[0x80131307] = 'CORDBG_E_NON_NATIVE_FRAME'
Message[0x80131307] = "'Native-frame-only' operation on non-native frame."
Comment[0x80131307] = 'Native frame only" operation on'
SymbolicName[0x80131309] = 'CORDBG_E_CODE_NOT_AVAILABLE'
Message[0x80131309] = 'The code is currently unavailable.'
Comment[0x80131309] = 'The code is currently unavailable'
SymbolicName[0x8013130a] = 'CORDBG_E_FUNCTION_NOT_IL'
Message[0x8013130a] = 'Attempt to get a ICorDebugFunction for a function that is not IL.'
Comment[0x8013130a] = 'Attempt to get a ICorDebugFunction for'
SymbolicName[0x8013130e] = 'CORDBG_E_CANT_SET_IP_INTO_FINALLY'
Message[0x8013130e] = 'SetIP is not possible because SetIP would move EIP from outside of an exception handling finally clause to a point inside of one.'
Comment[0x8013130e] = "SetIP isn't possible, because SetIP would"
SymbolicName[0x8013130f] = 'CORDBG_E_CANT_SET_IP_OUT_OF_FINALLY'
Message[0x8013130f] = 'SetIP is not possible because it would move EIP from within an exception handling finally clause to a point outside of one.'
Comment[0x8013130f] = "SetIP isn't possible because it would move"
SymbolicName[0x80131310] = 'CORDBG_E_CANT_SET_IP_INTO_CATCH'
Message[0x80131310] = 'SetIP is not possible, because SetIP would move EIP from outside of an exception handling catch clause to a point inside of one.'
Comment[0x80131310] = "SetIP isn't possible, because SetIP would"
SymbolicName[0x80131311] = 'CORDBG_E_SET_IP_NOT_ALLOWED_ON_NONLEAF_FRAME'
Message[0x80131311] = 'SetIP cannot be done on any frame except the leaf frame.'
Comment[0x80131311] = 'Setip cannot be done on any frame except'
SymbolicName[0x80131312] = 'CORDBG_E_SET_IP_IMPOSSIBLE'
Message[0x80131312] = 'SetIP is not allowed.'
Comment[0x80131312] = "SetIP isn't allowed. For example, there is"
SymbolicName[0x80131313] = 'CORDBG_E_FUNC_EVAL_BAD_START_POINT'
Message[0x80131313] = 'Func eval cannot work. Bad starting point.'
Comment[0x80131313] = "Func eval can't work if we're, for example,"
SymbolicName[0x80131314] = 'CORDBG_E_INVALID_OBJECT'
Message[0x80131314] = 'This object value is no longer valid.'
Comment[0x80131314] = 'This object value is no longer valid.'
SymbolicName[0x80131315] = 'CORDBG_E_FUNC_EVAL_NOT_COMPLETE'
Message[0x80131315] = 'CordbEval::GetResult called before func eval has finished.'
Comment[0x80131315] = 'If you call CordbEval::GetResult before the'
SymbolicName[0x8013131a] = 'CORDBG_E_STATIC_VAR_NOT_AVAILABLE'
Message[0x8013131a] = 'A static variable is not available because it has not been initialized yet.'
Comment[0x8013131a] = "A static variable isn't available because"
SymbolicName[0x8013131c] = 'CORDBG_E_CANT_SETIP_INTO_OR_OUT_OF_FILTER'
Message[0x8013131c] = 'SetIP cannot leave or enter a filter.'
Comment[0x8013131c] = "SetIP can't leave or enter a filter"
SymbolicName[0x8013131d] = 'CORDBG_E_CANT_CHANGE_JIT_SETTING_FOR_ZAP_MODULE'
Message[0x8013131d] = 'JIT settings for ZAP modules cannot be changed.'
Comment[0x8013131d] = "You can't change JIT settings for ZAP"
SymbolicName[0x8013131e] = 'CORDBG_E_CANT_SET_IP_OUT_OF_FINALLY_ON_WIN64'
Message[0x8013131e] = 'SetIP is not possible because it would move EIP from within a finally clause to a point outside of one on this platforms.'
Comment[0x8013131e] = "SetIP isn't possible because it would move"
SymbolicName[0x8013131f] = 'CORDBG_E_CANT_SET_IP_OUT_OF_CATCH_ON_WIN64'
Message[0x8013131f] = 'SetIP is not possible because it would move EIP from within a catch clause to a point outside of one on this platforms.'
Comment[0x8013131f] = "SetIP isn't possible because it would move"
SymbolicName[0x80131323] = 'CORDBG_E_CANT_SET_TO_JMC'
Message[0x80131323] = 'Cannot use JMC on this code (likely wrong JIT settings).'
Comment[0x80131323] = "Can't use JMC on this code (likely wrong jit settings)."
SymbolicName[0x80131325] = 'CORDBG_E_NO_CONTEXT_FOR_INTERNAL_FRAME'
Message[0x80131325] = 'Internal frame markers have no associated context.'
Comment[0x80131325] = 'Internal frame markers have no associated context.'
SymbolicName[0x80131326] = 'CORDBG_E_NOT_CHILD_FRAME'
Message[0x80131326] = 'The current frame is not a child frame.'
Comment[0x80131326] = 'The current frame is not a child frame.'
SymbolicName[0x80131327] = 'CORDBG_E_NON_MATCHING_CONTEXT'
Message[0x80131327] = 'The provided CONTEXT does not match the specified thread.'
Comment[0x80131327] = 'The provided CONTEXT does not match the specified thread.\n    The stack pointer in the provided CONTEXT must match the cached stack base and stack limit of the thread.\n'
SymbolicName[0x80131328] = 'CORDBG_E_PAST_END_OF_STACK'
Message[0x80131328] = 'The stackwalker is now past the end of stack.  No information is available.'
Comment[0x80131328] = 'The stackwalker is now past the end of stack.  No information is available.'
SymbolicName[0x80131329] = 'CORDBG_E_FUNC_EVAL_CANNOT_UPDATE_REGISTER_IN_NONLEAF_FRAME'
Message[0x80131329] = 'Func eval cannot update a variable stored in a register on a non-leaf frame.  The most likely cause is that such a variable is passed as a ref/out argument.'
Comment[0x80131329] = 'Func eval cannot update a variable stored in a register on a non-leaf frame.  The most likely cause is that such a variable is passed as a ref/out argument.'
SymbolicName[0x8013132d] = 'CORDBG_E_BAD_THREAD_STATE'
Message[0x8013132d] = 'The state of the thread is invalid.'
Comment[0x8013132d] = 'The state of the thread is invalid.'
SymbolicName[0x8013132e] = 'CORDBG_E_DEBUGGER_ALREADY_ATTACHED'
Message[0x8013132e] = 'This process has already been attached.'
Comment[0x8013132e] = 'This process has already been attached to'
SymbolicName[0x8013132f] = 'CORDBG_E_SUPERFLOUS_CONTINUE'
Message[0x8013132f] = 'Returned from a call to Continue that was not matched with a stopping event.'
Comment[0x8013132f] = 'Returned from a call to Continue that was'
SymbolicName[0x80131330] = 'CORDBG_E_SET_VALUE_NOT_ALLOWED_ON_NONLEAF_FRAME'
Message[0x80131330] = 'Cannot perfrom SetValue on non-leaf frames.'
Comment[0x80131330] = "Can't perfrom SetValue on non-leaf frames."
SymbolicName[0x80131332] = 'CORDBG_E_ENC_MODULE_NOT_ENC_ENABLED'
Message[0x80131332] = 'Tried to do Edit and Continue on a module that was not started in Edit and Continue mode.'
Comment[0x80131332] = "Tried to do EnC on a module that wasn't"
SymbolicName[0x80131333] = 'CORDBG_E_SET_IP_NOT_ALLOWED_ON_EXCEPTION'
Message[0x80131333] = 'SetIP cannot be done on any exception.'
Comment[0x80131333] = 'Setip cannot be done on any exception'
SymbolicName[0x80131334] = 'CORDBG_E_VARIABLE_IS_ACTUALLY_LITERAL'
Message[0x80131334] = "The 'variable' does not exist because it is a literal optimized away by the compiler."
Comment[0x80131334] = "The 'variable' doesn't exist because it is a"
SymbolicName[0x80131335] = 'CORDBG_E_PROCESS_DETACHED'
Message[0x80131335] = 'Process has been detached.'
Comment[0x80131335] = 'Process has been detached from'
SymbolicName[0x80131338] = 'CORDBG_E_ENC_CANT_ADD_FIELD_TO_VALUE_OR_LAYOUT_CLASS'
Message[0x80131338] = 'Adding a field to a value or layout class is prohibited.'
Comment[0x80131338] = 'Adding a field to a value or layout class is prohibitted,'
SymbolicName[0x8013133b] = 'CORDBG_E_FIELD_NOT_STATIC'
Message[0x8013133b] = 'GetStaticFieldValue called on a non-static field.'
Comment[0x8013133b] = 'Returned if someone tries to call GetStaticFieldValue'
SymbolicName[0x8013133c] = 'CORDBG_E_FIELD_NOT_INSTANCE'
Message[0x8013133c] = 'Returned if someone tries to call GetStaticFieldValue on a non-instance field.'
Comment[0x8013133c] = 'Returned if someone tries to call GetStaticFieldValue'
SymbolicName[0x8013133f] = 'CORDBG_E_ENC_JIT_CANT_UPDATE'
Message[0x8013133f] = 'The JIT is unable to update the method.'
Comment[0x8013133f] = 'The JIT is unable to update the method.'
SymbolicName[0x80131341] = 'CORDBG_E_ENC_INTERNAL_ERROR'
Message[0x80131341] = 'Internal Runtime Error while doing Edit-and-Continue.'
Comment[0x80131341] = 'Generic message for "Something user doesn\'t control went wrong" message.'
SymbolicName[0x80131342] = 'CORDBG_E_ENC_HANGING_FIELD'
Message[0x80131342] = 'The field was added via Edit and Continue after the class was loaded.'
Comment[0x80131342] = "The field was added via EnC after the class was loaded, and so instead of the the field being contiguous with the other fields, it's 'hanging' off the instance or type.  This error is used to indicate that either the storage for this field is not yet available and so the field value cannot be read, or the debugger needs to use an EnC specific code path to get the value."
SymbolicName[0x80131343] = 'CORDBG_E_MODULE_NOT_LOADED'
Message[0x80131343] = 'Module not loaded.'
Comment[0x80131343] = "If the module isn't loaded, including if it's been unloaded."
SymbolicName[0x80131345] = 'CORDBG_E_UNABLE_TO_SET_BREAKPOINT'
Message[0x80131345] = 'Cannot set a breakpoint here.'
Comment[0x80131345] = "Can't set a breakpoint here."
SymbolicName[0x80131346] = 'CORDBG_E_DEBUGGING_NOT_POSSIBLE'
Message[0x80131346] = 'Debugging is not possible due to an incompatibility within the CLR implementation.'
Comment[0x80131346] = "Debugging isn't possible due to an incompatibility within the CLR implementation."
SymbolicName[0x80131347] = 'CORDBG_E_KERNEL_DEBUGGER_ENABLED'
Message[0x80131347] = 'A kernel debugger is enabled on the system.  User-mode debugging will trap to the kernel debugger.'
Comment[0x80131347] = "Debugging isn't possible because a kernel debugger is enabled on the system."
SymbolicName[0x80131348] = 'CORDBG_E_KERNEL_DEBUGGER_PRESENT'
Message[0x80131348] = 'A kernel debugger is present on the system.  User-mode debugging will trap to the kernel debugger.'
Comment[0x80131348] = "Debugging isn't possible because a kernel debugger is present on the system."
SymbolicName[0x8013134b] = 'CORDBG_E_INCOMPATIBLE_PROTOCOL'
Message[0x8013134b] = "The debugger's protocol is incompatible with the debuggee."
Comment[0x8013134b] = "The debugger's protocol is incompatible with the debuggee."
SymbolicName[0x8013134c] = 'CORDBG_E_TOO_MANY_PROCESSES'
Message[0x8013134c] = 'The debugger can only handle a finite number of debuggees.'
Comment[0x8013134c] = 'The debugger can only handle a finite number of debuggees.'
SymbolicName[0x8013134d] = 'CORDBG_E_INTEROP_NOT_SUPPORTED'
Message[0x8013134d] = 'Interop debugging is not supported.'
Comment[0x8013134d] = 'Interop debugging is not supported'
SymbolicName[0x8013134e] = 'CORDBG_E_NO_REMAP_BREAKPIONT'
Message[0x8013134e] = 'Cannot call RemapFunction until have received RemapBreakpoint.'
Comment[0x8013134e] = 'Cannot call RemapFunction until have received RemapBreakpoint'
SymbolicName[0x8013134f] = 'CORDBG_E_OBJECT_NEUTERED'
Message[0x8013134f] = 'Object is in a zombie state.'
Comment[0x8013134f] = "Object has been neutered (it's in a zombie state)."
SymbolicName[0x80131350] = 'CORPROF_E_FUNCTION_NOT_COMPILED'
Message[0x80131350] = 'Function not yet compiled.'
Comment[0x80131350] = 'Function not yet compiled.'
SymbolicName[0x80131351] = 'CORPROF_E_DATAINCOMPLETE'
Message[0x80131351] = 'The ID is not fully loaded/defined yet.'
Comment[0x80131351] = 'The ID is not fully loaded/defined yet.'
SymbolicName[0x80131354] = 'CORPROF_E_FUNCTION_NOT_IL'
Message[0x80131354] = 'The Method has no associated IL.'
Comment[0x80131354] = 'The Method has no associated IL'
SymbolicName[0x80131355] = 'CORPROF_E_NOT_MANAGED_THREAD'
Message[0x80131355] = 'The thread has never run managed code before.'
Comment[0x80131355] = 'The thread has never run managed code before'
SymbolicName[0x80131356] = 'CORPROF_E_CALL_ONLY_FROM_INIT'
Message[0x80131356] = 'The function may only be called during profiler initialization.'
Comment[0x80131356] = 'The function may only be called during profiler init'
SymbolicName[0x8013135b] = 'CORPROF_E_NOT_YET_AVAILABLE'
Message[0x8013135b] = 'Requested information is not yet available.'
Comment[0x8013135b] = 'This is a general error used to indicated that the information'
SymbolicName[0x8013135c] = 'CORPROF_E_TYPE_IS_PARAMETERIZED'
Message[0x8013135c] = 'The given type is a generic and cannot be used with this method.'
Comment[0x8013135c] = 'The given type is a generic and cannot be used with this method.'
SymbolicName[0x8013135d] = 'CORPROF_E_FUNCTION_IS_PARAMETERIZED'
Message[0x8013135d] = 'The given function is a generic and cannot be used with this method.'
Comment[0x8013135d] = 'The given function is a generic and cannot be used with this method.'
SymbolicName[0x8013135e] = 'CORPROF_E_STACKSNAPSHOT_INVALID_TGT_THREAD'
Comment[0x8013135e] = 'A profiler tried to walk the stack of an invalid thread'
SymbolicName[0x8013135f] = 'CORPROF_E_STACKSNAPSHOT_UNMANAGED_CTX'
Comment[0x8013135f] = 'A profiler can not walk a thread that is currently executing unmanaged code'
SymbolicName[0x80131360] = 'CORPROF_E_STACKSNAPSHOT_UNSAFE'
Comment[0x80131360] = 'A stackwalk at this point may cause dead locks or data corruption'
SymbolicName[0x80131361] = 'CORPROF_E_STACKSNAPSHOT_ABORTED'
Comment[0x80131361] = 'Stackwalking callback requested the walk to abort'
SymbolicName[0x80131362] = 'CORPROF_E_LITERALS_HAVE_NO_ADDRESS'
Comment[0x80131362] = 'Returned when asked for the address of a static that is a literal.'
SymbolicName[0x80131363] = 'CORPROF_E_UNSUPPORTED_CALL_SEQUENCE'
Comment[0x80131363] = 'A call was made at an unsupported time.  Examples include illegally calling a profiling API method asynchronously, calling a method that might trigger a GC at an unsafe time, and calling a method at a time that could cause locks to be taken out of order.'
SymbolicName[0x80131364] = 'CORPROF_E_ASYNCHRONOUS_UNSAFE'
Comment[0x80131364] = 'A legal asynchronous call was made at an unsafe time (e.g., CLR locks are held)'
SymbolicName[0x80131365] = 'CORPROF_E_CLASSID_IS_ARRAY'
Comment[0x80131365] = 'The specified ClassID cannot be inspected by this function because it is an array'
SymbolicName[0x80131366] = 'CORPROF_E_CLASSID_IS_COMPOSITE'
Comment[0x80131366] = 'The specified ClassID is a non-array composite type (e.g., ref) and cannot be inspected'
SymbolicName[0x80131367] = 'CORPROF_E_PROFILER_DETACHING'
Comment[0x80131367] = "The profiler's call into the CLR is disallowed because the profiler is attempting to detach."
SymbolicName[0x80131368] = 'CORPROF_E_PROFILER_NOT_ATTACHABLE'
Comment[0x80131368] = 'The profiler does not support attaching to a live process.'
SymbolicName[0x80131369] = 'CORPROF_E_UNRECOGNIZED_PIPE_MSG_FORMAT'
Comment[0x80131369] = 'The message sent on the profiling API attach pipe is in an unrecognized format.'
SymbolicName[0x8013136a] = 'CORPROF_E_PROFILER_ALREADY_ACTIVE'
Comment[0x8013136a] = 'The request to attach a profiler was denied because a profiler is already loaded.'
SymbolicName[0x8013136b] = 'CORPROF_E_PROFILEE_INCOMPATIBLE_WITH_TRIGGER'
Comment[0x8013136b] = "Unable to request a profiler attach because the target profilee's runtime is of a version incompatible with the current process calling AttachProfiler()."
SymbolicName[0x8013136c] = 'CORPROF_E_IPC_FAILED'
Comment[0x8013136c] = 'AttachProfiler() encountered an error while communicating on the pipe to the target profilee.  This is often caused by a target profilee that is shutting down or killed while AttachProfiler() is reading or writing the pipe.'
SymbolicName[0x8013136d] = 'CORPROF_E_PROFILEE_PROCESS_NOT_FOUND'
Comment[0x8013136d] = 'AttachProfiler() was unable to find a profilee with the specified process ID.'
SymbolicName[0x8013136e] = 'CORPROF_E_CALLBACK3_REQUIRED'
Comment[0x8013136e] = 'Profiler must implement ICorProfilerCallback3 interface for this call to be supported.'
SymbolicName[0x8013136f] = 'CORPROF_E_UNSUPPORTED_FOR_ATTACHING_PROFILER'
Comment[0x8013136f] = 'This call was attempted by a profiler that attached to the process after startup, but this call is only supported by profilers that are loaded into the process on startup.'
SymbolicName[0x80131370] = 'CORPROF_E_IRREVERSIBLE_INSTRUMENTATION_PRESENT'
Comment[0x80131370] = 'Detach is impossible because the profiler has either instrumented IL or inserted enter/leave hooks. Detach was not attempted; the profiler is still fully attached.'
SymbolicName[0x80131371] = 'CORPROF_E_RUNTIME_UNINITIALIZED'
Comment[0x80131371] = 'The profiler called a function that cannot complete because the CLR is not yet fully initialized.  The profiler may try again once the CLR has fully started.'
SymbolicName[0x80131372] = 'CORPROF_E_IMMUTABLE_FLAGS_SET'
Comment[0x80131372] = 'Detach is impossible because immutable flags were set by the profiler at startup. Detach was not attempted; the profiler is still fully attached.'
SymbolicName[0x80131373] = 'CORPROF_E_PROFILER_NOT_YET_INITIALIZED'
Comment[0x80131373] = 'The profiler called a function that cannot complete because the profiler is not yet fully initialized.'
SymbolicName[0x80131374] = 'CORPROF_E_INCONSISTENT_WITH_FLAGS'
Comment[0x80131374] = 'The profiler called a function that first requires additional flags to be set in the event mask.  This HRESULT may also indicate that the profiler called a function that first requires that some of the flags currently set in the event mask be reset.'
SymbolicName[0x80131375] = 'CORPROF_E_PROFILER_CANCEL_ACTIVATION'
Comment[0x80131375] = 'The profiler has requested that the CLR instance not load the profiler into this process.'
SymbolicName[0x80131376] = 'CORPROF_E_CONCURRENT_GC_NOT_PROFILABLE'
Comment[0x80131376] = 'Concurrent GC mode is enabled, which prevents use of COR_PRF_MONITOR_GC'
SymbolicName[0x80131378] = 'CORPROF_E_DEBUGGING_DISABLED'
Comment[0x80131378] = 'This functionality requires CoreCLR debugging to be enabled.'
SymbolicName[0x80131379] = 'CORPROF_E_TIMEOUT_WAITING_FOR_CONCURRENT_GC'
Comment[0x80131379] = 'Timed out on waiting for concurrent GC to finish during attach.'
SymbolicName[0x8013137a] = 'CORPROF_E_MODULE_IS_DYNAMIC'
Comment[0x8013137a] = 'The specified module was dynamically generated (e.g., via Reflection.Emit API), and is thus not supported by this API method.'
SymbolicName[0x8013137b] = 'CORPROF_E_CALLBACK4_REQUIRED'
Comment[0x8013137b] = 'Profiler must implement ICorProfilerCallback4 interface for this call to be supported.'
SymbolicName[0x8013137c] = 'CORPROF_E_REJIT_NOT_ENABLED'
Comment[0x8013137c] = 'This call is not supported unless ReJIT is first enabled during initialization by setting COR_PRF_ENABLE_REJIT via SetEventMask.'
SymbolicName[0x8013137e] = 'CORPROF_E_FUNCTION_IS_COLLECTIBLE'
Comment[0x8013137e] = 'The specified function is instantiated into a collectible assembly, and is thus not supported by this API method.'
SymbolicName[0x80131380] = 'CORPROF_E_CALLBACK6_REQUIRED'
Comment[0x80131380] = 'Profiler must implement ICorProfilerCallback6 interface for this call to be supported.'
SymbolicName[0x80131382] = 'CORPROF_E_CALLBACK7_REQUIRED'
Comment[0x80131382] = 'Profiler must implement ICorProfilerCallback7 interface for this call to be supported.'
SymbolicName[0x80131383] = 'CORPROF_E_REJIT_INLINING_DISABLED'
Comment[0x80131383] = "The runtime's tracking of inlined methods for ReJIT is not enabled."
SymbolicName[0x80131384] = 'CORDIAGIPC_E_BAD_ENCODING'
Comment[0x80131384] = 'The runtime was unable to decode the Header or Payload.'
SymbolicName[0x80131385] = 'CORDIAGIPC_E_UNKNOWN_COMMAND'
Comment[0x80131385] = 'The specified CommandSet or CommandId is unknown.'
SymbolicName[0x80131386] = 'CORDIAGIPC_E_UNKNOWN_MAGIC'
Comment[0x80131386] = 'The magic version of Diagnostics IPC is unknown.'
SymbolicName[0x80131387] = 'CORDIAGIPC_E_UNKNOWN_ERROR'
Comment[0x80131387] = 'An unknown error occurred in the Diagnpostics IPC Server.'
SymbolicName[0x80131388] = 'CORPROF_E_SUSPENSION_IN_PROGRESS'
Comment[0x80131388] = 'The runtime cannot be suspened since a suspension is already in progress.'
SymbolicName[0x80131401] = 'SECURITY_E_INCOMPATIBLE_SHARE'
Message[0x80131401] = 'Loading this assembly would produce a different grant set from other instances.'
Comment[0x80131401] = 'Loading this assembly would produce a different grant set from other instances'
SymbolicName[0x80131402] = 'SECURITY_E_UNVERIFIABLE'
Message[0x80131402] = 'Unverifiable code failed policy check.'
Comment[0x80131402] = 'Unverifable code failed policy check'
SymbolicName[0x80131403] = 'SECURITY_E_INCOMPATIBLE_EVIDENCE'
Message[0x80131403] = 'Assembly already loaded without additional security evidence.'
Comment[0x80131403] = 'Assembly already loaded without additional security evidence.'
SymbolicName[0x80131416] = 'CORSEC_E_POLICY_EXCEPTION'
Message[0x80131416] = 'PolicyException thrown.'
Comment[0x80131416] = 'PolicyException thrown'
SymbolicName[0x80131417] = 'CORSEC_E_MIN_GRANT_FAIL'
Message[0x80131417] = 'Failed to grant minimum permission requests.'
Comment[0x80131417] = 'Failed to grant minimum permission requests'
SymbolicName[0x80131418] = 'CORSEC_E_NO_EXEC_PERM'
Message[0x80131418] = 'Failed to grant permission to execute.'
Comment[0x80131418] = 'Failed to grant permission to execute'
SymbolicName[0x80131419] = 'CORSEC_E_XMLSYNTAX'
Message[0x80131419] = 'XML Syntax error.'
Comment[0x80131419] = 'XML Syntax error'
SymbolicName[0x8013141a] = 'CORSEC_E_INVALID_STRONGNAME'
Message[0x8013141a] = 'Strong name validation failed.'
Comment[0x8013141a] = 'Strong name validation failed'
SymbolicName[0x8013141b] = 'CORSEC_E_MISSING_STRONGNAME'
Message[0x8013141b] = 'Assembly is not strong named.'
Comment[0x8013141b] = 'Assembly is not strong named'
SymbolicName[0x8013141d] = 'CORSEC_E_INVALID_IMAGE_FORMAT'
Message[0x8013141d] = 'Invalid assembly file format.'
Comment[0x8013141d] = 'Invalid assembly file format'
SymbolicName[0x8013141e] = 'CORSEC_E_INVALID_PUBLICKEY'
Message[0x8013141e] = 'Invalid assembly public key.'
Comment[0x8013141e] = 'Invalid assembly public key'
SymbolicName[0x80131420] = 'CORSEC_E_SIGNATURE_MISMATCH'
Message[0x80131420] = 'Signature size mismatch.'
Comment[0x80131420] = 'Signature size mismatch'
SymbolicName[0x80131430] = 'CORSEC_E_CRYPTO'
Message[0x80131430] = 'Failure during Cryptographic operation.'
Comment[0x80131430] = 'generic CryptographicException'
SymbolicName[0x80131431] = 'CORSEC_E_CRYPTO_UNEX_OPER'
Message[0x80131431] = 'Unexpected Cryptographic operation.'
Comment[0x80131431] = 'generic CryptographicUnexpectedOperationException'
SymbolicName[0x80131442] = 'CORSECATTR_E_BAD_ACTION'
Message[0x80131442] = 'Invalid security action code.'
Comment[0x80131442] = 'Invalid security action code'
SymbolicName[0x80131500] = 'COR_E_EXCEPTION'
Message[0x80131500] = 'General Exception'
Comment[0x80131500] = 'Base class for all exceptions in the runtime'
SymbolicName[0x80131501] = 'COR_E_SYSTEM'
Message[0x80131501] = 'System.Exception'
Comment[0x80131501] = 'The base class for the runtime\'s "less serious" exceptions'
SymbolicName[0x80131502] = 'COR_E_ARGUMENTOUTOFRANGE'
Message[0x80131502] = 'An argument was out of its legal range.'
Comment[0x80131502] = 'An argument was out of its legal range.'
SymbolicName[0x80131503] = 'COR_E_ARRAYTYPEMISMATCH'
Message[0x80131503] = 'Attempted to store an object of the wrong type in an array.'
Comment[0x80131503] = 'Attempted to store an object of the wrong type in an array'
SymbolicName[0x80131504] = 'COR_E_CONTEXTMARSHAL'
Message[0x80131504] = 'Attempted to marshal an object across a context boundary.'
SymbolicName[0x80131505] = 'COR_E_TIMEOUT'
Message[0x80131505] = 'Operation timed out.'
SymbolicName[0x80131506] = 'COR_E_EXECUTIONENGINE'
Message[0x80131506] = 'Internal CLR error.'
Comment[0x80131506] = "An internal error happened in the Common Language Runtime's Execution Engine"
SymbolicName[0x80131507] = 'COR_E_FIELDACCESS'
Message[0x80131507] = 'Access to this field is denied.'
Comment[0x80131507] = 'Access to this field is denied.'
SymbolicName[0x80131508] = 'COR_E_INDEXOUTOFRANGE'
Message[0x80131508] = 'Array subscript out of range.'
Comment[0x80131508] = 'Attempted to access an element within an array by using an index that is'
SymbolicName[0x80131509] = 'COR_E_INVALIDOPERATION'
Message[0x80131509] = 'An operation is not legal in the current state.'
Comment[0x80131509] = 'An operation is not legal in the current state.'
SymbolicName[0x8013150a] = 'COR_E_SECURITY'
Message[0x8013150a] = 'An error relating to security occurred.'
Comment[0x8013150a] = 'An error relating to security occurred.'
SymbolicName[0x8013150c] = 'COR_E_SERIALIZATION'
Message[0x8013150c] = 'An error relating to serialization occurred.'
Comment[0x8013150c] = 'An error relating to serialization has occurred.'
SymbolicName[0x8013150d] = 'COR_E_VERIFICATION'
Message[0x8013150d] = 'A verification failure has occurred.'
Comment[0x8013150d] = 'A verification failure occurred'
SymbolicName[0x80131510] = 'COR_E_METHODACCESS'
Message[0x80131510] = 'Access to this method is denied.'
Comment[0x80131510] = 'Access to this method is denied.'
SymbolicName[0x80131511] = 'COR_E_MISSINGFIELD'
Message[0x80131511] = 'Field does not exist.'
Comment[0x80131511] = 'An attempt was made to dynamically access a field that does not exist.'
SymbolicName[0x80131512] = 'COR_E_MISSINGMEMBER'
Message[0x80131512] = 'Member does not exist.'
Comment[0x80131512] = 'An attempt was made to dynamically invoke or access a field or method'
SymbolicName[0x80131513] = 'COR_E_MISSINGMETHOD'
Message[0x80131513] = 'Method does not exist.'
Comment[0x80131513] = 'An attempt was made to dynamically invoke a method that does not exist'
SymbolicName[0x80131514] = 'COR_E_MULTICASTNOTSUPPORTED'
Message[0x80131514] = 'Attempt to combine delegates that are not multicast.'
Comment[0x80131514] = 'Attempted to combine delegates that are not multicast'
SymbolicName[0x80131515] = 'COR_E_NOTSUPPORTED'
Message[0x80131515] = 'Operation is not supported.'
Comment[0x80131515] = 'The operation is not supported'
SymbolicName[0x80131516] = 'COR_E_OVERFLOW'
Message[0x80131516] = 'Arithmetic, casting or conversion operation overflowed or underflowed.'
Comment[0x80131516] = 'An arithmetic, casting, or conversion operation overflowed or underflowed.'
SymbolicName[0x80131517] = 'COR_E_RANK'
Message[0x80131517] = 'An array has the wrong number of dimensions for a particular operation.'
Comment[0x80131517] = 'An array has the wrong number of dimensions for a particular operation.'
SymbolicName[0x80131518] = 'COR_E_SYNCHRONIZATIONLOCK'
Message[0x80131518] = 'This operation must be called from a synchronized block.'
Comment[0x80131518] = 'Wait(), Notify() or NotifyAll() was called from an unsynchronized ** block of c'
SymbolicName[0x80131519] = 'COR_E_THREADINTERRUPTED'
Message[0x80131519] = 'Thread was interrupted from a waiting state.'
Comment[0x80131519] = 'Indicates that the thread was interrupted from a waiting state'
SymbolicName[0x8013151a] = 'COR_E_MEMBERACCESS'
Message[0x8013151a] = 'Access to this member is denied.'
Comment[0x8013151a] = 'Access to this member is denied.'
SymbolicName[0x80131520] = 'COR_E_THREADSTATE'
Message[0x80131520] = 'Thread is in an invalid state for this operation.'
Comment[0x80131520] = 'Indicate that the Thread class is in an invalid state for the method call'
SymbolicName[0x80131521] = 'COR_E_THREADSTOP'
Message[0x80131521] = 'Thread is stopping.'
Comment[0x80131521] = 'Thrown into a thread to cause it to stop. This exception is typically not caught'
SymbolicName[0x80131522] = 'COR_E_TYPELOAD'
Message[0x80131522] = 'Could not find or load a type.'
Comment[0x80131522] = 'Could not find or load a specific type (class, enum, etc).'
SymbolicName[0x80131523] = 'COR_E_ENTRYPOINTNOTFOUND'
Message[0x80131523] = 'Could not find the specified DllImport entrypoint.'
Comment[0x80131523] = 'Could not find the specified DllImport entry point'
SymbolicName[0x80131524] = 'COR_E_DLLNOTFOUND'
Message[0x80131524] = 'Could not find the specified DllImport Dll.'
Comment[0x80131524] = 'Could not find the specified DllImport DLL.'
SymbolicName[0x80131525] = 'COR_E_THREADSTART'
Comment[0x80131525] = 'Indicate that a user thread fails to start.'
SymbolicName[0x80131527] = 'COR_E_INVALIDCOMOBJECT'
Message[0x80131527] = 'An invalid __ComObject has been used.'
Comment[0x80131527] = 'An invalid __ComObject has been used.'
SymbolicName[0x80131528] = 'COR_E_NOTFINITENUMBER'
Message[0x80131528] = 'Not a Number.'
Comment[0x80131528] = 'Thrown if value (a floating point number) is either the not a number value (NaN) or +- infinity value'
SymbolicName[0x80131529] = 'COR_E_DUPLICATEWAITOBJECT'
Message[0x80131529] = 'An object appears more than once in the wait objects array.'
Comment[0x80131529] = 'An object appears more than once in the wait objects array.'
SymbolicName[0x8013152b] = 'COR_E_SEMAPHOREFULL'
Message[0x8013152b] = 'Reached maximum count for semaphore.'
Comment[0x8013152b] = 'Adding the given count to the semaphore would cause it to exceed its maximum count.'
SymbolicName[0x8013152c] = 'COR_E_WAITHANDLECANNOTBEOPENED'
Message[0x8013152c] = 'No semaphore of the given name exists.'
Comment[0x8013152c] = 'No Semaphore of the given name exists.'
SymbolicName[0x8013152d] = 'COR_E_ABANDONEDMUTEX'
Message[0x8013152d] = 'The wait completed due to an abandoned mutex.'
Comment[0x8013152d] = 'The wait completed due to an abandoned mutex.'
SymbolicName[0x80131530] = 'COR_E_THREADABORTED'
Message[0x80131530] = 'Thread has aborted.'
Comment[0x80131530] = 'Thrown into a thread to cause it to abort. Not catchable.'
SymbolicName[0x80131531] = 'COR_E_INVALIDOLEVARIANTTYPE'
Message[0x80131531] = 'OLE Variant has an invalid type.'
Comment[0x80131531] = 'The type of an OLE variant that was passed into the runtime is invalid.'
SymbolicName[0x80131532] = 'COR_E_MISSINGMANIFESTRESOURCE'
Message[0x80131532] = 'An expected resource in the assembly manifest was missing.'
Comment[0x80131532] = 'An expected resource in the assembly manifest was missing.'
SymbolicName[0x80131533] = 'COR_E_SAFEARRAYTYPEMISMATCH'
Message[0x80131533] = 'A mismatch has occurred between the runtime type of the array and the sub type recorded in the metadata.'
Comment[0x80131533] = 'A mismatch has occurred between the runtime type of the array and the subtype recorded in the metadata'
SymbolicName[0x80131534] = 'COR_E_TYPEINITIALIZATION'
Message[0x80131534] = 'Uncaught exception during type initialization.'
Comment[0x80131534] = "An exception was thrown by a type's initializer (.cctor)."
SymbolicName[0x80131535] = 'COR_E_MARSHALDIRECTIVE'
Message[0x80131535] = 'Invalid marshaling directives.'
Comment[0x80131535] = 'The marshaling directives are invalid.'
SymbolicName[0x80131536] = 'COR_E_MISSINGSATELLITEASSEMBLY'
Message[0x80131536] = 'An expected satellite assembly containing the ultimate fallback resources for a given culture was not found or could not be loaded.'
Comment[0x80131536] = 'An expected satellite assembly containing the ultimate fallback resources'
SymbolicName[0x80131537] = 'COR_E_FORMAT'
Message[0x80131537] = 'The format of one argument does not meet the contract of the method.'
Comment[0x80131537] = 'The format of one argument does not meet the contract of the method.'
SymbolicName[0x80131538] = 'COR_E_SAFEARRAYRANKMISMATCH'
Message[0x80131538] = 'A mismatch has occurred between the runtime rank of the array and the rank recorded in the metadata.'
Comment[0x80131538] = 'A mismatch has occurred between the runtime rank of the array and the rank recorded in the metadata'
SymbolicName[0x80131539] = 'COR_E_PLATFORMNOTSUPPORTED'
Message[0x80131539] = 'Operation is not supported on this platform.'
Comment[0x80131539] = 'The method is not supported on this platform'
SymbolicName[0x8013153a] = 'COR_E_INVALIDPROGRAM'
Message[0x8013153a] = 'Invalid IL or CLR metadata.'
Comment[0x8013153a] = 'A program contained invalid IL or bad metadata.  Usually this is a compiler bug.'
SymbolicName[0x8013153b] = 'COR_E_OPERATIONCANCELED'
Message[0x8013153b] = 'The operation was cancelled.'
Comment[0x8013153b] = 'The operation was cancelled.'
SymbolicName[0x8013153d] = 'COR_E_INSUFFICIENTMEMORY'
Comment[0x8013153d] = 'Not enough memory was available for an operation.'
SymbolicName[0x8013153e] = 'COR_E_RUNTIMEWRAPPED'
Comment[0x8013153e] = 'An object that does not derive from System.Exception has been wrapped in a RuntimeWrappedException.'
SymbolicName[0x80131541] = 'COR_E_DATAMISALIGNED'
Message[0x80131541] = 'A datatype misalignment was detected in a load or store instruction.'
Comment[0x80131541] = 'A datatype misalignment was detected in a load or store instruction.'
SymbolicName[0x80131542] = 'COR_E_CODECONTRACTFAILED'
Message[0x80131542] = 'A managed code contract (ie, precondition, postcondition, invariant, or assert) failed.'
Comment[0x80131542] = 'A managed code contract (ie, precondition, postcondition, invariant, or assert) failed.'
SymbolicName[0x80131543] = 'COR_E_TYPEACCESS'
Message[0x80131543] = 'Access to this type is denied.'
Comment[0x80131543] = 'Access to this type is denied.'
SymbolicName[0x80131544] = 'COR_E_ACCESSING_CCW'
Message[0x80131544] = 'Fail to access a CCW because the corresponding managed object is already collected.'
Comment[0x80131544] = 'Fail to access a CCW because the corresponding managed object is already collected.'
SymbolicName[0x80131577] = 'COR_E_KEYNOTFOUND'
Message[0x80131577] = 'The given key was not present in the dictionary.'
SymbolicName[0x80131578] = 'COR_E_INSUFFICIENTEXECUTIONSTACK'
Message[0x80131578] = 'Insufficient stack to continue executing the program safely. This can happen from having too many functions on the call stack or function on the stack using too much stack space.'
Comment[0x80131578] = 'Insufficient stack to continue executing the program safely. This can happen from having too many functions on the call stack or function on the stack using too much stack space.'
SymbolicName[0x80131600] = 'COR_E_APPLICATION'
Message[0x80131600] = 'Application exception'
Comment[0x80131600] = 'The base class for all "less serious" exceptions.'
SymbolicName[0x80131601] = 'COR_E_INVALIDFILTERCRITERIA'
Message[0x80131601] = 'The given filter criteria does not match the filter content.'
Comment[0x80131601] = 'The given filter criteria does not match the filter contract.'
SymbolicName[0x80131602] = 'COR_E_REFLECTIONTYPELOAD'
Message[0x80131602] = 'Could not find or load a specific class that was requested through Reflection.'
Comment[0x80131602] = 'Could not find or load a specific class that was requested through Reflection'
SymbolicName[0x80131603] = 'COR_E_TARGET'
Message[0x80131603] = 'Attempt to invoke non-static method with a null Object.'
Comment[0x80131603] = '- If you attempt to invoke a non-static method with a null Object - If you atte'
SymbolicName[0x80131604] = 'COR_E_TARGETINVOCATION'
Message[0x80131604] = 'Uncaught exception thrown by method called through Reflection.'
Comment[0x80131604] = 'If the method called throws an exception'
SymbolicName[0x80131605] = 'COR_E_CUSTOMATTRIBUTEFORMAT'
Message[0x80131605] = 'Custom attribute has invalid format.'
Comment[0x80131605] = 'If the binary format of a custom attribute is invalid.'
SymbolicName[0x80131620] = 'COR_E_IO'
Message[0x80131620] = 'Error during managed I/O.'
Comment[0x80131620] = 'Some sort of I/O error.'
SymbolicName[0x80131621] = 'COR_E_FILELOAD'
Message[0x80131621] = 'Could not find or load a specific file.'
SymbolicName[0x80131622] = 'COR_E_OBJECTDISPOSED'
Message[0x80131622] = 'The object has already been disposed.'
Comment[0x80131622] = 'The object has already been disposed.'
SymbolicName[0x80131623] = 'COR_E_FAILFAST'
Message[0x80131623] = 'Runtime operation halted by call to System.Environment.FailFast().'
Comment[0x80131623] = 'Runtime operation halted by call to System.Environment.FailFast().'
SymbolicName[0x80131640] = 'COR_E_HOSTPROTECTION'
Message[0x80131640] = 'The host has forbidden this operation.'
Comment[0x80131640] = 'Attempted to perform an operation that was forbidden by the host.'
SymbolicName[0x80131641] = 'COR_E_ILLEGAL_REENTRANCY'
Message[0x80131641] = 'Attempted to call into managed code when executing inside a low level extensibility point.'
Comment[0x80131641] = 'Attempted to call into managed code when executing inside a low level extensibility point.'
SymbolicName[0x80131700] = 'CLR_E_SHIM_RUNTIMELOAD'
Message[0x80131700] = 'Failed to load the runtime.'
Comment[0x80131700] = 'Failed to load the runtime'
SymbolicName[0x80131704] = 'CLR_E_SHIM_LEGACYRUNTIMEALREADYBOUND'
Message[0x80131704] = 'A runtime has already been bound for legacy activation policy use.'
SymbolicName[0x80131815] = 'VER_E_FIELD_SIG'
Message[0x80131815] = '[field sig]'
SymbolicName[0x801318ce] = 'VER_E_CIRCULAR_VAR_CONSTRAINTS'
Message[0x801318ce] = 'Method parent has circular class type parameter constraints.'
SymbolicName[0x801318cf] = 'VER_E_CIRCULAR_MVAR_CONSTRAINTS'
Message[0x801318cf] = 'Method has circular method type parameter constraints.'
SymbolicName[0x80131920] = 'COR_E_Data'
SymbolicName[0x80131b24] = 'VLDTR_E_SIG_BADVOID'
Message[0x80131b24] = "Illegal 'void' in signature."
Comment[0x80131b24] = 'Illegal "void" in signature'
SymbolicName[0x80131b2d] = 'VLDTR_E_GP_ILLEGAL_VARIANT_MVAR'
Message[0x80131b2d] = 'GenericParam is a method type parameter and must be non-variant.'
Comment[0x80131b2d] = 'GenericParam is a method type parameter and must be non-variant'
SymbolicName[0x80131c00] = 'CORDBG_E_THREAD_NOT_SCHEDULED'
Message[0x80131c00] = 'Thread is not scheduled. Thus we may not have OSThreadId, handle, or context.'
Comment[0x80131c00] = 'Thread is not scheduled. Thus we may not have OSThreadId, handle, or context'
SymbolicName[0x80131c01] = 'CORDBG_E_HANDLE_HAS_BEEN_DISPOSED'
Message[0x80131c01] = 'Handle has been disposed.'
Comment[0x80131c01] = 'Handle has been disposed.'
SymbolicName[0x80131c02] = 'CORDBG_E_NONINTERCEPTABLE_EXCEPTION'
Message[0x80131c02] = 'Cannot intercept this exception.'
Comment[0x80131c02] = 'Cant intercept this exception.'
SymbolicName[0x80131c04] = 'CORDBG_E_INTERCEPT_FRAME_ALREADY_SET'
Message[0x80131c04] = 'The intercept frame for this exception has already been set.'
Comment[0x80131c04] = 'The intercept frame for this exception has already been set.'
SymbolicName[0x80131c05] = 'CORDBG_E_NO_NATIVE_PATCH_AT_ADDR'
Message[0x80131c05] = 'There is no native patch at the given address.'
Comment[0x80131c05] = "there's no native patch at the given address."
SymbolicName[0x80131c06] = 'CORDBG_E_MUST_BE_INTEROP_DEBUGGING'
Message[0x80131c06] = 'This API is only allowed when interop debugging.'
Comment[0x80131c06] = 'This API is only allowed when interop debugging.'
SymbolicName[0x80131c07] = 'CORDBG_E_NATIVE_PATCH_ALREADY_AT_ADDR'
Message[0x80131c07] = 'There is already a native patch at the address.'
Comment[0x80131c07] = "There's already a native patch at the address"
SymbolicName[0x80131c08] = 'CORDBG_E_TIMEOUT'
Message[0x80131c08] = 'A wait timed out, likely an indication of deadlock.'
Comment[0x80131c08] = 'a wait timed out .. likely an indication of deadlock.'
SymbolicName[0x80131c09] = 'CORDBG_E_CANT_CALL_ON_THIS_THREAD'
Message[0x80131c09] = 'Cannot use the API on this thread.'
Comment[0x80131c09] = "Can't use the API on this thread."
SymbolicName[0x80131c0a] = 'CORDBG_E_ENC_INFOLESS_METHOD'
Message[0x80131c0a] = "Method was not JIT'd in EnC mode."
Comment[0x80131c0a] = 'Method was not JITed in EnC mode'
SymbolicName[0x80131c0c] = 'CORDBG_E_ENC_IN_FUNCLET'
Message[0x80131c0c] = 'Method is in a callable handler/filter. Cannot increase stack.'
Comment[0x80131c0c] = 'Method is in a callable handler/filter. Cant grow stack'
SymbolicName[0x80131c0e] = 'CORDBG_E_ENC_EDIT_NOT_SUPPORTED'
Message[0x80131c0e] = 'Attempt to perform unsupported edit.'
Comment[0x80131c0e] = 'Attempt to perform unsupported edit'
SymbolicName[0x80131c10] = 'CORDBG_E_NOTREADY'
Message[0x80131c10] = 'The LS is not in a good spot to perform the requested operation.'
Comment[0x80131c10] = 'The LS is not in a good spot to perform the requested operation.'
SymbolicName[0x80131c11] = 'CORDBG_E_CANNOT_RESOLVE_ASSEMBLY'
Message[0x80131c11] = 'We failed to resolve assembly given an AssemblyRef token. Assembly may be not loaded yet or not a valid token.'
Comment[0x80131c11] = 'We failed to resolve assembly given an AssemblyRef token. Assembly may be not loaded yet or not a valid token.'
SymbolicName[0x80131c12] = 'CORDBG_E_MUST_BE_IN_LOAD_MODULE'
Message[0x80131c12] = 'Must be in context of LoadModule callback to perform requested operation.'
Comment[0x80131c12] = 'Must be in context of LoadModule callback to perform requested operation'
SymbolicName[0x80131c13] = 'CORDBG_E_CANNOT_BE_ON_ATTACH'
Message[0x80131c13] = 'Requested operation cannot be performed during an attach operation.'
Comment[0x80131c13] = 'Requested operation cannot be performed during an attach operation'
SymbolicName[0x80131c14] = 'CORDBG_E_NGEN_NOT_SUPPORTED'
Message[0x80131c14] = 'NGEN must be supported to perform the requested operation.'
Comment[0x80131c14] = 'NGEN must be supported to perform the requested operation'
SymbolicName[0x80131c15] = 'CORDBG_E_ILLEGAL_SHUTDOWN_ORDER'
Message[0x80131c15] = 'Trying to shutdown out of order.'
Comment[0x80131c15] = 'Trying to shutdown out of order.'
SymbolicName[0x80131c16] = 'CORDBG_E_CANNOT_DEBUG_FIBER_PROCESS'
Message[0x80131c16] = 'Debugging fiber mode managed process is not supported.'
Comment[0x80131c16] = "For Whidbey, we don't support debugging fiber mode managed process"
SymbolicName[0x80131c17] = 'CORDBG_E_MUST_BE_IN_CREATE_PROCESS'
Message[0x80131c17] = 'Must be in context of CreateProcess callback to perform requested operation.'
Comment[0x80131c17] = 'Must be in context of CreateProcess callback to perform requested operation'
SymbolicName[0x80131c18] = 'CORDBG_E_DETACH_FAILED_OUTSTANDING_EVALS'
Message[0x80131c18] = 'All outstanding func-evals have not completed, detaching is not allowed at this time.'
Comment[0x80131c18] = 'All outstanding func-evals have not completed, detaching is not allowed at this time.'
SymbolicName[0x80131c19] = 'CORDBG_E_DETACH_FAILED_OUTSTANDING_STEPPERS'
Message[0x80131c19] = 'All outstanding steppers have not been closed, detaching is not allowed at this time.'
Comment[0x80131c19] = 'All outstanding steppers have not been closed, detaching is not allowed at this time.'
SymbolicName[0x80131c20] = 'CORDBG_E_CANT_INTEROP_STEP_OUT'
Message[0x80131c20] = 'Cannot have an ICorDebugStepper do a native step-out.'
Comment[0x80131c20] = "Can't have an ICorDebugStepper do a native step-out."
SymbolicName[0x80131c21] = 'CORDBG_E_DETACH_FAILED_OUTSTANDING_BREAKPOINTS'
Message[0x80131c21] = 'All outstanding breakpoints have not been closed, detaching is not allowed at this time.'
Comment[0x80131c21] = 'All outstanding breakpoints have not been closed, detaching is not allowed at this time.'
SymbolicName[0x80131c22] = 'CORDBG_E_ILLEGAL_IN_STACK_OVERFLOW'
Message[0x80131c22] = 'The operation is illegal because of a stack overflow.'
Comment[0x80131c22] = 'the operation is illegal because of a stackoverflow.'
SymbolicName[0x80131c23] = 'CORDBG_E_ILLEGAL_AT_GC_UNSAFE_POINT'
Message[0x80131c23] = 'The operation failed because it is a GC unsafe point.'
Comment[0x80131c23] = "The operation failed because it's a GC unsafe point."
SymbolicName[0x80131c24] = 'CORDBG_E_ILLEGAL_IN_PROLOG'
Message[0x80131c24] = 'The operation failed because the thread is in the prolog.'
Comment[0x80131c24] = 'The operation failed because the thread is in the prolog'
SymbolicName[0x80131c25] = 'CORDBG_E_ILLEGAL_IN_NATIVE_CODE'
Message[0x80131c25] = 'The operation failed because the thread is in native code.'
Comment[0x80131c25] = 'The operation failed because the thread is in native code'
SymbolicName[0x80131c26] = 'CORDBG_E_ILLEGAL_IN_OPTIMIZED_CODE'
Message[0x80131c26] = 'The operation failed because the thread is in optimized code.'
Comment[0x80131c26] = 'The operation failed because the thread is in optimized code.'
SymbolicName[0x80131c28] = 'CORDBG_E_APPDOMAIN_MISMATCH'
Message[0x80131c28] = 'A supplied object or type belongs to the wrong AppDomain.'
Comment[0x80131c28] = 'A supplied object or type belongs to the wrong AppDomain'
SymbolicName[0x80131c29] = 'CORDBG_E_CONTEXT_UNVAILABLE'
Message[0x80131c29] = "The thread's context is not available."
Comment[0x80131c29] = "The thread's context is not available."
SymbolicName[0x80131c30] = 'CORDBG_E_UNCOMPATIBLE_PLATFORMS'
Message[0x80131c30] = 'The operation failed because debuggee and debugger are on incompatible platforms.'
Comment[0x80131c30] = 'The operation failed because debuggee and debugger are on incompatible platform'
SymbolicName[0x80131c31] = 'CORDBG_E_DEBUGGING_DISABLED'
Message[0x80131c31] = 'The operation failed because the debugging has been disabled'
Comment[0x80131c31] = 'The operation failed because the debugging has been disabled'
SymbolicName[0x80131c32] = 'CORDBG_E_DETACH_FAILED_ON_ENC'
Message[0x80131c32] = 'Detach is illegal after an Edit and Continue on a module.'
Comment[0x80131c32] = 'Detach is illegal after a module has been EnCed.'
SymbolicName[0x80131c33] = 'CORDBG_E_CURRENT_EXCEPTION_IS_OUTSIDE_CURRENT_EXECUTION_SCOPE'
Message[0x80131c33] = 'Cannot intercept the current exception at the specified frame.'
Comment[0x80131c33] = 'Interception of the current exception is not legal'
SymbolicName[0x80131c34] = 'CORDBG_E_HELPER_MAY_DEADLOCK'
Message[0x80131c34] = 'The debugger helper thread cannot obtain the locks it needs to perform this operation.'
Comment[0x80131c34] = 'Helper thread can not safely run code. The opereration may work at a later time.'
SymbolicName[0x80131c35] = 'CORDBG_E_MISSING_METADATA'
Message[0x80131c35] = 'The operation failed because the debugger could not get the metadata.'
Comment[0x80131c35] = 'The operation failed because the debugger could not get the metadata.'
SymbolicName[0x80131c36] = 'CORDBG_E_TARGET_INCONSISTENT'
Message[0x80131c36] = 'The debuggee is in a corrupt state.'
Comment[0x80131c36] = 'The debuggee is in a corrupt state.'
SymbolicName[0x80131c37] = 'CORDBG_E_DETACH_FAILED_OUTSTANDING_TARGET_RESOURCES'
Message[0x80131c37] = 'Detach failed because there are outstanding resources in the target.'
Comment[0x80131c37] = 'The debugger is holding resource in the target (such as GC handles, function evaluations, etc).\n  These resources must be released through the appropriate ICorDebug API before detach can succeed.'
SymbolicName[0x80131c38] = 'CORDBG_E_TARGET_READONLY'
Message[0x80131c38] = 'The debuggee is read-only.'
Comment[0x80131c38] = 'The provided ICorDebugDataTarget does not implement ICorDebugMutableDataTarget.'
SymbolicName[0x80131c39] = 'CORDBG_E_MISMATCHED_CORWKS_AND_DACWKS_DLLS'
Message[0x80131c39] = 'The version of clr.dll in the target does not match the one mscordacwks.dll was built for.'
Comment[0x80131c39] = 'A clr/mscordacwks mismatch will cause DAC to fail to initialize in ClrDataAccess::Initialize'
SymbolicName[0x80131c3a] = 'CORDBG_E_MODULE_LOADED_FROM_DISK'
Message[0x80131c3a] = 'Symbols are not supplied for modules loaded from disk.'
Comment[0x80131c3a] = 'Symbols are not supplied for modules loaded from disk'
SymbolicName[0x80131c3b] = 'CORDBG_E_SYMBOLS_NOT_AVAILABLE'
Message[0x80131c3b] = 'The application did not supply symbols when it loaded or created this module, or they are not yet available.'
Comment[0x80131c3b] = 'The application did not supply symbols when it loaded or created this module, or they are not yet available'
SymbolicName[0x80131c3c] = 'CORDBG_E_DEBUG_COMPONENT_MISSING'
Message[0x80131c3c] = 'A debug component is not installed.'
Comment[0x80131c3c] = 'A debug component is not installed'
SymbolicName[0x80131c43] = 'CORDBG_E_LIBRARY_PROVIDER_ERROR'
Message[0x80131c43] = 'The ICLRDebuggingLibraryProvider callback returned an error or did not provide a valid handle.'
Comment[0x80131c43] = 'The ICLRDebuggingLibraryProvider callback returned an error or did not provide a valid handle'
SymbolicName[0x80131c44] = 'CORDBG_E_NOT_CLR'
Message[0x80131c44] = 'The module at the base address indicated was not recognized as a CLR'
Comment[0x80131c44] = 'The module at the base address indicated was not recognized as a CLR'
SymbolicName[0x80131c45] = 'CORDBG_E_MISSING_DATA_TARGET_INTERFACE'
Message[0x80131c45] = 'The provided data target does not implement the required interfaces for this version of the runtime'
Comment[0x80131c45] = 'The provided data target does not implement the required interfaces for this version of the runtime'
SymbolicName[0x80131c46] = 'CORDBG_E_UNSUPPORTED_DEBUGGING_MODEL'
Message[0x80131c46] = 'This debugging model is unsupported by the specified runtime'
Comment[0x80131c46] = 'This debugging model is unsupported by the specified runtime'
SymbolicName[0x80131c47] = 'CORDBG_E_UNSUPPORTED_FORWARD_COMPAT'
Message[0x80131c47] = 'The debugger is not designed to support the version of the CLR the debuggee is using.'
Comment[0x80131c47] = 'The debugger is not designed to support the version of the CLR the debuggee is using.'
SymbolicName[0x80131c48] = 'CORDBG_E_UNSUPPORTED_VERSION_STRUCT'
Message[0x80131c48] = 'The version struct has an unrecognized value for wStructVersion'
Comment[0x80131c48] = 'The version struct has an unrecognized value for wStructVersion'
SymbolicName[0x80131c49] = 'CORDBG_E_READVIRTUAL_FAILURE'
Message[0x80131c49] = 'A call into a ReadVirtual implementation returned failure'
Comment[0x80131c49] = 'A call into a ReadVirtual implementation returned failure'
SymbolicName[0x80131c4a] = 'CORDBG_E_VALUE_POINTS_TO_FUNCTION'
Message[0x80131c4a] = "The Debugging API doesn't support dereferencing function pointers."
Comment[0x80131c4a] = "The Debugging API doesn't support dereferencing function pointers."
SymbolicName[0x80131c4b] = 'CORDBG_E_CORRUPT_OBJECT'
Message[0x80131c4b] = 'The address provided does not point to a valid managed object.'
Comment[0x80131c4b] = 'The address provided does not point to a valid managed object.'
SymbolicName[0x80131c4c] = 'CORDBG_E_GC_STRUCTURES_INVALID'
Message[0x80131c4c] = 'The GC heap structures are not in a valid state for traversal.'
Comment[0x80131c4c] = 'The GC heap structures are not in a valid state for traversal.'
SymbolicName[0x80131c4d] = 'CORDBG_E_INVALID_OPCODE'
Message[0x80131c4d] = 'The specified IL offset or opcode is not supported for this operation.'
Comment[0x80131c4d] = 'The specified IL offset or opcode is not supported for this operation.'
SymbolicName[0x80131c4e] = 'CORDBG_E_UNSUPPORTED'
Message[0x80131c4e] = 'The specified action is unsupported by this version of the runtime.'
Comment[0x80131c4e] = 'The specified action is unsupported by this version of the runtime.'
SymbolicName[0x80131c4f] = 'CORDBG_E_MISSING_DEBUGGER_EXPORTS'
Message[0x80131c4f] = 'The debuggee memory space does not have the expected debugging export table.'
Comment[0x80131c4f] = 'The debuggee memory space does not have the expected debugging export table.'
SymbolicName[0x80131c61] = 'CORDBG_E_DATA_TARGET_ERROR'
Message[0x80131c61] = 'Failure when calling a data target method.'
Comment[0x80131c61] = 'Failure when calling a data target method.'
SymbolicName[0x80131c64] = 'CORDBG_E_NO_IMAGE_AVAILABLE'
Message[0x80131c64] = "Couldn't find a native image."
Comment[0x80131c64] = "Couldn't find a native image."
SymbolicName[0x80131c68] = 'CORDBG_E_UNSUPPORTED_DELEGATE'
Message[0x80131c68] = 'The delegate contains a delegate currently not supported by the API.'
Comment[0x80131c68] = 'The delegate contains a delegate currently not supported by the API.'
SymbolicName[0x80131d02] = 'PEFMT_E_64BIT'
Message[0x80131d02] = 'File is PE32+.'
Comment[0x80131d02] = 'File is PE32+'
SymbolicName[0x80131d0b] = 'PEFMT_E_32BIT'
Message[0x80131d0b] = 'File is PE32'
Comment[0x80131d0b] = 'File is PE32'
SymbolicName[0x80131f06] = 'NGEN_E_SYS_ASM_NI_MISSING'
Message[0x80131f06] = 'NGen cannot proceed because Mscorlib.dll does not have a native image'
Comment[0x80131f06] = 'Compiling any assembly other than mscorlib in the absence of mscorlib.ni.dll is not allowed.'
SymbolicName[0x80131fff] = 'CLDB_E_INTERNALERROR'
SymbolicName[0x80132000] = 'CLR_E_BIND_ASSEMBLY_VERSION_TOO_LOW'
Message[0x80132000] = 'The bound assembly has a version that is lower than that of the request.'
Comment[0x80132000] = 'For AppX binder, indicates that bound assembly has a version lower than that requested, and we will refuse version rollback.'
SymbolicName[0x80132001] = 'CLR_E_BIND_ASSEMBLY_PUBLIC_KEY_MISMATCH'
Message[0x80132001] = 'The assembly version has a public key token that does not match that of the request.'
Comment[0x80132001] = "For AppX binder, indicates that bound assembly's public key token doesn't match the key in the assembly name."
SymbolicName[0x80132002] = 'CLR_E_BIND_IMAGE_UNAVAILABLE'
Message[0x80132002] = 'The requested image was not found or is unavailable.'
Comment[0x80132002] = 'Occurs if a request for a native image is made on an ICLRPrivAssembly interface when one is not available.'
SymbolicName[0x80132003] = 'CLR_E_BIND_UNRECOGNIZED_IDENTITY_FORMAT'
Message[0x80132003] = 'The provided identity format is not recognized.'
Comment[0x80132003] = 'If a binder is provided an identity format that it cannot parse, it returns this error.'
SymbolicName[0x80132004] = 'CLR_E_BIND_ASSEMBLY_NOT_FOUND'
Message[0x80132004] = 'A binding for the specified assembly name was not found.'
Comment[0x80132004] = 'Returned by binders that bind based on assembly identity.'
SymbolicName[0x80132005] = 'CLR_E_BIND_TYPE_NOT_FOUND'
Message[0x80132005] = 'A binding for the specified type name was not found.'
Comment[0x80132005] = 'Returned by binders that bind based on type identity.'
SymbolicName[0x80132006] = 'CLR_E_BIND_SYS_ASM_NI_MISSING'
Message[0x80132006] = 'Could not use native image because Mscorlib.dll is missing a native image'
Comment[0x80132006] = 'Returned when loading an assembly that only has a native image and no IL and cannot hardbind to mscorlib.ni.dll.'
SymbolicName[0x80132007] = 'CLR_E_BIND_NI_SECURITY_FAILURE'
Message[0x80132007] = 'Native image was generated in a different trust level than present at runtime'
Comment[0x80132007] = 'Returned when an assembly is NGened in full trust and then used in partial trust or vice versa.'
SymbolicName[0x80132008] = 'CLR_E_BIND_NI_DEP_IDENTITY_MISMATCH'
Message[0x80132008] = 'Native image identity mismatch with respect to its dependencies'
Comment[0x80132008] = "Returned when an assembly's identities have changed so the native image is no longer valid."
SymbolicName[0x80132009] = 'CLR_E_GC_OOM'
Message[0x80132009] = 'Failfast due to an OOM during a GC'
Comment[0x80132009] = 'During a GC when we try to allocate memory for GC datastructures we could not.'
SymbolicName[0x8013200a] = 'CLR_E_GC_BAD_AFFINITY_CONFIG'
Message[0x8013200a] = "GCHeapAffinitizeMask or GCHeapAffinitizeRanges didn't specify any CPUs the current process is affinitized to."
Comment[0x8013200a] = "During a GC initialization, the affinity mask specified via GCHeapAffinitizeMask or GCHeapAffinitizeRanges didn't contain any CPUs the current process is affinitized to."
SymbolicName[0x8013200b] = 'CLR_E_GC_BAD_AFFINITY_CONFIG_FORMAT'
Message[0x8013200b] = 'GCHeapAffinitizeRanges configuration string has invalid format.'
Comment[0x8013200b] = "During a GC initialization, the GCHeapAffinitizeRanges config couldn't be parsed due to its invalid format."
SymbolicName[0x8013200c] = 'CLR_E_CROSSGEN_NO_IBC_DATA_FOUND'
Message[0x8013200c] = 'Cannot compile using the PartialNgen flag because no IBC data was found.'
Comment[0x8013200c] = 'During crossgen compilation, the PartialNgen flag was set, but no IBC data was found in the resources section of the input image, and no IBC file was externally provided.'
