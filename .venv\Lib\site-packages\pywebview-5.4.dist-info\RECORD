pywebview-5.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pywebview-5.4.dist-info/LICENSE,sha256=SpiN01mIMs02U94g3DPLZ30PtTq1VRyHnKMSgK5lNnU,1518
pywebview-5.4.dist-info/METADATA,sha256=4THdmnbVnzYZY3kE5CiGXddiCsQx-xsLw02cB6_DyGA,4522
pywebview-5.4.dist-info/RECORD,,
pywebview-5.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pywebview-5.4.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
pywebview-5.4.dist-info/entry_points.txt,sha256=9LC45PCvcE-KEf9GnEOgRom_deLsCxohUWOsXf40KH8,64
pywebview-5.4.dist-info/top_level.txt,sha256=d5SpaV1yBkWk3tar-VrEOX-bBM9jXPlVdVNpN5FQrxA,8
webview/__init__.py,sha256=k7uM4qSF-Ejq0QiHxMhAv9IckQ7M3qp779QTFQcB6FQ,14289
webview/__pycache__/__init__.cpython-312.pyc,,
webview/__pycache__/_version.cpython-312.pyc,,
webview/__pycache__/errors.cpython-312.pyc,,
webview/__pycache__/event.cpython-312.pyc,,
webview/__pycache__/guilib.cpython-312.pyc,,
webview/__pycache__/http.cpython-312.pyc,,
webview/__pycache__/localization.cpython-312.pyc,,
webview/__pycache__/menu.cpython-312.pyc,,
webview/__pycache__/screen.cpython-312.pyc,,
webview/__pycache__/util.cpython-312.pyc,,
webview/__pycache__/window.cpython-312.pyc,,
webview/__pyinstaller/__init__.py,sha256=7ItSg8oUeprOOpmoFTrfbz9XmXOzbrWgZnNMPMjC-P0,70
webview/__pyinstaller/__pycache__/__init__.cpython-312.pyc,,
webview/__pyinstaller/__pycache__/hook-webview.cpython-312.pyc,,
webview/__pyinstaller/hook-webview.py,sha256=Mu3vq7lmogG-QFL_68C6dlrx3zR8nQHDlf8dUnulMFk,294
webview/_version.py,sha256=FhYsvQx9kUSDeiCmla4K_qKqJtwXRZPTG0FeQqP2Ce4,406
webview/dom/__init__.py,sha256=UV5pmB6oui7-oy1HOp-QYYVFAUdcGMM9hjUBrKmeb7U,1162
webview/dom/__pycache__/__init__.cpython-312.pyc,,
webview/dom/__pycache__/classlist.cpython-312.pyc,,
webview/dom/__pycache__/dom.cpython-312.pyc,,
webview/dom/__pycache__/element.cpython-312.pyc,,
webview/dom/__pycache__/event.cpython-312.pyc,,
webview/dom/__pycache__/propsdict.cpython-312.pyc,,
webview/dom/classlist.py,sha256=xOd9aeGY1NxEz4iUILza1jQ8iD9WdUpQuK6uHzyjiNc,1377
webview/dom/dom.py,sha256=yrWWVDtnluYPr2cLWaRHpKKQxUFzxtb91bC2xJwhe_0,2485
webview/dom/element.py,sha256=rOEJ0KyNOMVQ3MF6dP709Wp0TNPRFd9nZrL6tt0o9Q8,14983
webview/dom/event.py,sha256=2R1Wo8wKBKryWhC10fZM6_KDrqySouvZjjKllJcGFC8,1128
webview/dom/propsdict.py,sha256=FWuAV44JA-E25uYKMf-WEehfa0ZpNX5Ax-9-XM3w_m0,5983
webview/errors.py,sha256=9mqzRMan1XdRhEeG_mqrxnl5Qf5eLco49Pzi9Dr_2S8,93
webview/event.py,sha256=yJGdlcUi0Lkomyn9vu5Fkd1TgYu9J3PSf6ilNvPzs_k,2461
webview/guilib.py,sha256=8lxy16ZoBXmad50N3GBZHQaL3I-jVb5U_sFBllkrG88,3828
webview/http.py,sha256=fvSdZGgWFTdzLPLWnPG_I-Q4_X_F3r3tAklG0u713PA,7974
webview/js/api.js,sha256=qjTQFNhSstc626h8OvFoLx0GjVI84NpJPlNC5AAQj-A,8599
webview/js/customize.js,sha256=IsWIQHBcUKaAk-foiKcDkUIquqjwvofeb0VtIjEZrEA,3692
webview/js/finish.js,sha256=7WAFonhq8k0a_jHZ7ntpoZw2cOdwick9YHJWY2GNoMs,359
webview/js/lib/dom_json.js,sha256=_IXeRfpwLvIoU7rN9TTr83Nll0XTzS4V79oELFVCMFI,26096
webview/js/lib/polyfill.js,sha256=GMNbqbTCllImn9xAEkkHdyymqlRUmj9uwgKAcp_b-8k,26187
webview/lib/Microsoft.Web.WebView2.Core.dll,sha256=y4yFL8xO9V1jC2TRcdwRU4uyUlgEHtIs8xc1mCouCeM,603712
webview/lib/Microsoft.Web.WebView2.WinForms.dll,sha256=5iBWvuKKsJQHEUS0c3EAmm-8Fi-a6hhHGfLobtUV9_g,38976
webview/lib/WebBrowserInterop.x64.dll,sha256=9qZGRw8OEFgiSlLo4uIXUB3KRpObML_Jpd1NzdQ_CI0,7168
webview/lib/WebBrowserInterop.x86.dll,sha256=TlJyl_vqfFoEGfOmrzB8JoF4e-sL8O8_6oSaf9haCNw,7168
webview/lib/pywebview-android.jar,sha256=O3XEz3FOfoCS9Hdu_SKeFHgyPiITAHwEHag0uR4yAA4,9071
webview/lib/runtimes/win-arm64/native/WebView2Loader.dll,sha256=J36EzZR072MC_zvJFwbMrJvHCc0ibeTb19rShXGyvAU,139840
webview/lib/runtimes/win-x64/native/WebView2Loader.dll,sha256=JxtX4-wDxDahXYDK_rn9FhikN5MjPYsFyURvjeClG-Q,165960
webview/lib/runtimes/win-x86/native/WebView2Loader.dll,sha256=fzYv2YzCQ61iDy0KSmwiPfgPlgxOGaLZly9K95OFZc8,116312
webview/localization.py,sha256=yT9WqzuhL75ecOiY8Mga7zG21vSXgt0hnRlU1ZsjiL4,760
webview/menu.py,sha256=0oa_QFIdyTNUDDyj6yhGIf_opM1dy0x4eDc1JRaBsBc,733
webview/platforms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
webview/platforms/__pycache__/__init__.cpython-312.pyc,,
webview/platforms/__pycache__/android.cpython-312.pyc,,
webview/platforms/__pycache__/cef.cpython-312.pyc,,
webview/platforms/__pycache__/cocoa.cpython-312.pyc,,
webview/platforms/__pycache__/edgechromium.cpython-312.pyc,,
webview/platforms/__pycache__/gtk.cpython-312.pyc,,
webview/platforms/__pycache__/mshtml.cpython-312.pyc,,
webview/platforms/__pycache__/qt.cpython-312.pyc,,
webview/platforms/__pycache__/winforms.cpython-312.pyc,,
webview/platforms/android.py,sha256=_Zz5q-xLU5NiioZhaC59fBqIxFZW1ONLskG0Z8pq5Uo,12617
webview/platforms/cef.py,sha256=iyzIotD96Px3_y4fq8BH0g4k-XNMwzW4AX9g1Z7QhXQ,10836
webview/platforms/cocoa.py,sha256=_eeIBEj9M3Nz9m5v-_6ca46pqiYoEjtHhHh5e3H2oBM,53518
webview/platforms/edgechromium.py,sha256=1R6QTqok8vn2ILKjrBlS8MLaO789G9xRYXWUnYzJNOg,11706
webview/platforms/gtk.py,sha256=q-uCVZyN9Gg_3hkr3zL_qoLPMjsT9ZjjU0UptkvsAk0,30976
webview/platforms/mshtml.py,sha256=z7Z1Pypg_BmRLe0V9uh99kJ-FFRVAju08RgCZJn034o,8827
webview/platforms/qt.py,sha256=DU0KpLHN4-6GgB3tYFcowDsJjYT0Qs7GQcR-fV1cUq8,36744
webview/platforms/winforms.py,sha256=opeqH3yI8IT_Mh6eGyd4LWc2KYiz7UJY2hti3b93ZkU,31782
webview/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
webview/screen.py,sha256=TYy4y7n3V4hZuhbmoWP4qPY5EmRI5aVi9hSHZf903l0,405
webview/util.py,sha256=UuXzkbWPYsKiVgEYxXWM4TIqvSzrF1TznwpcmlowU5Y,16305
webview/window.py,sha256=rSaeUZsG9TAVHzY1E1txG0yIkFVWc8wS2nuKIVtR4gM,18653
