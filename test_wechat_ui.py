#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基于uiautomation的微信自动化功能
"""

from wechat_uiautomation import WeChatUIAutomation

def test_wechat_ui():
    """测试微信UI自动化功能"""
    print("="*60)
    print("测试基于uiautomation的微信自动化功能")
    print("="*60)
    
    try:
        # 创建实例
        wechat = WeChatUIAutomation()
        print("✅ WeChatUIAutomation实例创建成功")
        
        # 查找微信窗口
        if wechat.find_wechat_window():
            print("✅ 成功找到微信窗口")
            
            # 激活微信窗口
            wechat.activate_wechat()
            print("✅ 微信窗口已激活")
            
            print("\n" + "="*60)
            print("功能测试菜单")
            print("="*60)
            print("1. 测试搜索联系人")
            print("2. 测试发送消息")
            print("3. 测试添加好友")
            print("4. 获取联系人列表")
            print("5. 退出测试")
            print("="*60)
            
            while True:
                try:
                    choice = input("\n请选择测试功能 (1-5): ").strip()
                    
                    if choice == "1":
                        contact_name = input("请输入要搜索的联系人名称: ").strip()
                        if contact_name:
                            result = wechat.search_contact(contact_name)
                            if result:
                                print(f"✅ 搜索联系人 '{contact_name}' 成功")
                            else:
                                print(f"❌ 搜索联系人 '{contact_name}' 失败")
                        else:
                            print("❌ 联系人名称不能为空")
                    
                    elif choice == "2":
                        contact_name = input("请输入联系人名称: ").strip()
                        message = input("请输入消息内容: ").strip()
                        if contact_name and message:
                            print(f"正在向 {contact_name} 发送消息: {message}")
                            result = wechat.send_message(contact_name, message)
                            if result:
                                print("✅ 消息发送成功")
                            else:
                                print("❌ 消息发送失败")
                        else:
                            print("❌ 联系人名称和消息内容不能为空")
                    
                    elif choice == "3":
                        search_keyword = input("请输入搜索关键词: ").strip()
                        verify_msg = input("请输入验证消息 (可选): ").strip()
                        if search_keyword:
                            print(f"正在添加好友: {search_keyword}")
                            result = wechat.add_friend(search_keyword, verify_msg)
                            if result:
                                print("✅ 好友申请发送成功")
                            else:
                                print("❌ 好友申请发送失败")
                        else:
                            print("❌ 搜索关键词不能为空")
                    
                    elif choice == "4":
                        print("正在获取联系人列表...")
                        contacts = wechat.get_contact_list()
                        if contacts:
                            print(f"✅ 获取到 {len(contacts)} 个联系人")
                            print("前10个联系人:")
                            for i, contact in enumerate(contacts[:10], 1):
                                print(f"  {i}. {contact}")
                            if len(contacts) > 10:
                                print(f"  ... 还有 {len(contacts) - 10} 个联系人")
                        else:
                            print("❌ 获取联系人列表失败")
                    
                    elif choice == "5":
                        print("退出测试")
                        break
                    
                    else:
                        print("❌ 无效选择，请输入 1-5")
                        
                except KeyboardInterrupt:
                    print("\n用户中断测试")
                    break
                except Exception as e:
                    print(f"❌ 测试过程中出错: {e}")
            
        else:
            print("❌ 未找到微信窗口")
            print("请确保:")
            print("1. 微信PC版已打开并登录")
            print("2. 微信窗口可见")
            print("3. 微信版本兼容")
        
        print("\n" + "="*60)
        print("测试完成！")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def quick_test():
    """快速测试基本功能"""
    print("快速测试uiautomation基本功能...")
    
    try:
        wechat = WeChatUIAutomation()
        
        if wechat.find_wechat_window():
            print("✅ 微信窗口检测成功")
            wechat.activate_wechat()
            print("✅ 微信窗口激活成功")
            print("🎯 uiautomation可以正常使用！")
            print("💡 运行 python test_wechat_ui.py 进行完整测试")
        else:
            print("❌ 请先打开微信")
            
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_test()
    else:
        test_wechat_ui()
