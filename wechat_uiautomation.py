#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于uiautomation的微信自动化替代方案
完全免费，功能强大
"""

import time
import logging
import os
from datetime import datetime
from typing import List, Dict, Optional
import cv2
import numpy as np
import pyautogui

try:
    import uiautomation as auto
    UIAUTOMATION_AVAILABLE = True
except ImportError:
    UIAUTOMATION_AVAILABLE = False
    print("警告: uiautomation库未安装，某些功能可能不可用")

try:
    import wxauto
    WXAUTO_AVAILABLE = True
except ImportError:
    WXAUTO_AVAILABLE = False
    print("警告: wxauto库未安装，某些功能可能不可用")


class WeChatUIAutomation:
    """基于uiautomation的微信自动化类"""

    def __init__(self):
        """初始化"""
        self.wechat_window = None
        self.uiautomation_available = UIAUTOMATION_AVAILABLE
        self.setup_logging()

        if not self.uiautomation_available:
            raise ImportError("uiautomation库未安装，请先安装: pip install uiautomation")

        try:
            # 查找微信窗口
            if not self.find_wechat_window():
                self.logger.warning("未找到微信窗口，请确保微信已打开")
        except Exception as e:
            self.logger.error(f"初始化微信自动化失败: {e}")
            raise

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(name)s] [%(levelname)s] [%(filename)s:%(lineno)d]  %(message)s",
            handlers=[
                logging.FileHandler("wechat_ui_automation.log", encoding="utf-8"),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger("wechat_uiautomation")

    def find_wechat_window(self):
        """查找微信窗口"""
        try:
            # 尝试通过窗口名称查找
            self.wechat_window = auto.WindowControl(searchDepth=1, Name="微信")
            if self.wechat_window.Exists(0, 0):
                self.logger.info("找到微信窗口")
                return True

            # 尝试其他可能的窗口名称
            self.wechat_window = auto.WindowControl(
                searchDepth=1, ClassName="WeChatMainWndForPC"
            )
            if self.wechat_window.Exists(0, 0):
                self.logger.info("找到微信窗口（通过类名）")
                return True

            self.logger.error("未找到微信窗口，请确保微信已打开")
            return False

        except Exception as e:
            self.logger.error(f"查找微信窗口失败: {e}")
            return False

    def activate_wechat(self):
        """激活微信窗口"""
        if self.wechat_window:
            try:
                self.wechat_window.SetActive()
                time.sleep(0.5)
                self.logger.info("微信窗口已激活")
                return True
            except Exception as e:
                self.logger.error(f"激活微信窗口失败: {e}")
                return False
        return False

    def search_contact(self, contact_name: str) -> bool:
        """搜索联系人"""
        try:
            self.activate_wechat()

            # 预清理：在搜索前先清理所有可能的弹窗
            self.pre_clear_dialogs()

            # 查找搜索框
            search_box = None
            search_patterns = ["搜索", "Search"]
            
            for pattern in search_patterns:
                try:
                    search_box = self.wechat_window.EditControl(Name=pattern)
                    if search_box.Exists(0, 0):
                        self.logger.info(f"找到搜索框: {pattern}")
                        break
                except:
                    continue

            if not search_box:
                self.logger.error("未找到搜索框")
                return False

            # 清空搜索框并输入联系人名称
            search_box.Click()
            time.sleep(0.5)
            search_box.SendKeys("{Ctrl}a")
            time.sleep(0.2)
            search_box.SendKeys(contact_name)
            time.sleep(2)

            self.logger.info(f"搜索联系人 (标准方法): {contact_name}")
            return True

        except Exception as e:
            self.logger.error(f"搜索联系人失败: {e}")
            return False

    def send_message(self, contact_name: str, message: str) -> bool:
        """发送消息"""
        try:
            # 搜索联系人
            if not self.search_contact(contact_name):
                return False

            time.sleep(2)

            # 查找消息输入框
            message_box = self._find_message_input_box()
            if not message_box:
                self.logger.error("未找到消息输入框")
                return False

            # 发送消息
            message_box.Click()
            time.sleep(0.5)
            message_box.SendKeys(message)
            time.sleep(0.5)
            message_box.SendKeys("{Enter}")

            self.logger.info(f"消息已发送给 {contact_name}: {message}")
            return True

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    def _find_message_input_box(self):
        """查找消息输入框的辅助方法"""
        try:
            # 获取所有编辑框
            edit_controls = []
            for control in self.wechat_window.GetChildren():
                if control.ControlType == auto.ControlType.EditControl:
                    edit_controls.append(control)

            if edit_controls:
                # 通常消息输入框是最后一个或者位置最靠下的
                return edit_controls[-1]

            return None
        except Exception:
            return None

    def opencv_check_user_not_found(self):
        """使用OpenCV模板匹配检测'无法找到用户'弹窗 - 在微信窗口内操作"""
        try:
            # 获取微信窗口的位置和大小
            if not self.wechat_window:
                return False, None

            wechat_rect = self.wechat_window.BoundingRectangle
            wechat_x = wechat_rect.left
            wechat_y = wechat_rect.top
            wechat_width = wechat_rect.width()
            wechat_height = wechat_rect.height()

            # 只截取微信窗口区域
            screenshot = pyautogui.screenshot(region=(wechat_x, wechat_y, wechat_width, wechat_height))
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
            
            # 模板文件路径
            template_files = [
                "templates/user_not_found.png",
                "templates/user_not_found_text.png", 
                "templates/dialog_background.png"
            ]
            
            # 方法1：模板匹配（如果有模板图片）
            for template_file in template_files:
                if os.path.exists(template_file):
                    try:
                        template = cv2.imread(template_file, cv2.IMREAD_GRAYSCALE)
                        if template is not None:
                            # 模板匹配
                            result = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                            
                            # 如果匹配度高于阈值
                            if max_val > 0.7:  # 70%匹配度
                                h, w = template.shape
                                x, y = max_loc
                                # 返回相对于微信窗口的坐标
                                self.logger.info(f"✅ 模板匹配检测到弹窗: {template_file}, 匹配度: {max_val:.2f}")
                                self.logger.info(f"🎯 弹窗位置: 微信窗口内({x}, {y}), 大小: {w}x{h}")
                                return True, (x, y, w, h)
                    except Exception as e:
                        self.logger.warning(f"模板匹配失败 {template_file}: {e}")
                        continue
            
            return False, None
            
        except Exception as e:
            self.logger.warning(f"OpenCV检测失败: {e}")
            return False, None

    def opencv_click_confirm_button(self, dialog_region=None):
        """使用OpenCV模板匹配点击确定按钮 - 在微信窗口内操作"""
        try:
            # 获取微信窗口的位置和大小
            if not self.wechat_window:
                self.logger.error("❌ 微信窗口未找到")
                return False

            wechat_rect = self.wechat_window.BoundingRectangle
            wechat_x = wechat_rect.left
            wechat_y = wechat_rect.top
            wechat_width = wechat_rect.width()
            wechat_height = wechat_rect.height()

            self.logger.info(f"🎯 微信窗口位置: ({wechat_x}, {wechat_y}), 大小: {wechat_width}x{wechat_height}")

            # 只截取微信窗口区域
            screenshot = pyautogui.screenshot(region=(wechat_x, wechat_y, wechat_width, wechat_height))
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # 如果有弹窗区域，只在该区域内查找（相对于微信窗口）
            if dialog_region:
                x, y, w, h = dialog_region
                # 调整坐标为相对于微信窗口的坐标
                rel_x = max(0, x - wechat_x)
                rel_y = max(0, y - wechat_y)
                rel_w = min(w, wechat_width - rel_x)
                rel_h = min(h, wechat_height - rel_y)

                roi = screenshot_cv[rel_y:rel_y+rel_h, rel_x:rel_x+rel_w]
                roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                offset_x, offset_y = rel_x, rel_y
            else:
                roi = screenshot_cv
                roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                offset_x, offset_y = 0, 0
            
            # 方法1：模板匹配（如果有模板图片）
            template_files = [
                "templates/add_friend_button.png",  # 优先匹配添加好友按钮
                "templates/confirm_button.png"
            ]

            for template_file in template_files:
                if os.path.exists(template_file):
                    try:
                        template = cv2.imread(template_file, cv2.IMREAD_GRAYSCALE)
                        if template is not None:
                            # 模板匹配
                            result = cv2.matchTemplate(roi_gray, template, cv2.TM_CCOEFF_NORMED)
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                            # 记录匹配度（用于调试）
                            self.logger.info(f"🔍 模板匹配 {template_file}: 最高匹配度 {max_val:.3f}")

                            # 降低阈值，提高匹配成功率
                            if max_val > 0.5:  # 50%匹配度（降低阈值）
                                h, w = template.shape
                                x, y = max_loc
                                # 计算在微信窗口内的相对坐标
                                rel_click_x = offset_x + x + w // 2
                                rel_click_y = offset_y + y + h // 2

                                # 转换为屏幕绝对坐标
                                abs_click_x = wechat_x + rel_click_x
                                abs_click_y = wechat_y + rel_click_y

                                # 确保点击位置在微信窗口内
                                if (wechat_x <= abs_click_x <= wechat_x + wechat_width and
                                    wechat_y <= abs_click_y <= wechat_y + wechat_height):
                                    pyautogui.click(abs_click_x, abs_click_y)
                                    self.logger.info(f"✅ 模板匹配点击按钮: {template_file}, 匹配度: {max_val:.3f}")
                                    self.logger.info(f"🎯 点击位置: 微信窗口内({rel_click_x}, {rel_click_y}) -> 屏幕({abs_click_x}, {abs_click_y})")
                                    return True
                                else:
                                    self.logger.warning(f"❌ 点击位置超出微信窗口范围: ({abs_click_x}, {abs_click_y})")
                                    return False
                            else:
                                self.logger.info(f"❌ 匹配度过低: {template_file}, 匹配度: {max_val:.3f} < 0.5")
                    except Exception as e:
                        self.logger.warning(f"模板匹配失败 {template_file}: {e}")
                        continue
                else:
                    self.logger.warning(f"❌ 模板文件不存在: {template_file}")
            
            return False

        except Exception as e:
            self.logger.warning(f"OpenCV点击按钮失败: {e}")
            return False

    def opencv_click_add_friend_button(self):
        """专门用于点击'添加到通讯录'按钮的OpenCV方法 - 在微信窗口内操作"""
        try:
            self.logger.info("🎯 OpenCV专门查找'添加到通讯录'按钮...")

            # 获取微信窗口的位置和大小
            if not self.wechat_window:
                self.logger.error("❌ 微信窗口未找到")
                return False

            wechat_rect = self.wechat_window.BoundingRectangle
            wechat_x = wechat_rect.left
            wechat_y = wechat_rect.top
            wechat_width = wechat_rect.width()
            wechat_height = wechat_rect.height()

            # 只截取微信窗口区域
            screenshot = pyautogui.screenshot(region=(wechat_x, wechat_y, wechat_width, wechat_height))
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)

            # 专门的添加好友按钮模板
            template_files = [
                "templates/add_friend_button.png"
            ]

            for template_file in template_files:
                if os.path.exists(template_file):
                    try:
                        template = cv2.imread(template_file, cv2.IMREAD_GRAYSCALE)
                        if template is not None:
                            self.logger.info(f"🔍 加载模板: {template_file}, 尺寸: {template.shape}")

                            # 多种匹配方法
                            methods = [
                                cv2.TM_CCOEFF_NORMED,
                                cv2.TM_CCORR_NORMED,
                                cv2.TM_SQDIFF_NORMED
                            ]

                            best_match = 0
                            best_location = None
                            best_method = None

                            for method in methods:
                                result = cv2.matchTemplate(gray, template, method)
                                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                                # 对于TM_SQDIFF_NORMED，值越小越好
                                if method == cv2.TM_SQDIFF_NORMED:
                                    match_val = 1 - min_val
                                    location = min_loc
                                else:
                                    match_val = max_val
                                    location = max_loc

                                self.logger.info(f"🔍 方法 {method}: 匹配度 {match_val:.3f}")

                                if match_val > best_match:
                                    best_match = match_val
                                    best_location = location
                                    best_method = method

                            self.logger.info(f"🎯 最佳匹配: 方法 {best_method}, 匹配度 {best_match:.3f}")

                            # 降低阈值到30%
                            if best_match > 0.3:
                                h, w = template.shape
                                x, y = best_location
                                # 计算在微信窗口内的相对坐标
                                rel_click_x = x + w // 2
                                rel_click_y = y + h // 2

                                # 转换为屏幕绝对坐标
                                abs_click_x = wechat_x + rel_click_x
                                abs_click_y = wechat_y + rel_click_y

                                # 确保点击位置在微信窗口内
                                if (wechat_x <= abs_click_x <= wechat_x + wechat_width and
                                    wechat_y <= abs_click_y <= wechat_y + wechat_height):
                                    pyautogui.click(abs_click_x, abs_click_y)
                                    self.logger.info(f"✅ OpenCV成功点击'添加到通讯录'按钮! 匹配度: {best_match:.3f}")
                                    self.logger.info(f"🎯 点击位置: 微信窗口内({rel_click_x}, {rel_click_y}) -> 屏幕({abs_click_x}, {abs_click_y})")
                                    return True
                                else:
                                    self.logger.warning(f"❌ 点击位置超出微信窗口范围: ({abs_click_x}, {abs_click_y})")
                                    return False
                            else:
                                self.logger.warning(f"❌ 匹配度过低: {best_match:.3f} < 0.3")

                                # 保存当前屏幕截图用于调试
                                debug_path = "debug_screenshot.png"
                                cv2.imwrite(debug_path, screenshot_cv)
                                self.logger.info(f"💾 已保存调试截图: {debug_path}")

                    except Exception as e:
                        self.logger.error(f"模板匹配失败 {template_file}: {e}")
                        continue
                else:
                    self.logger.error(f"❌ 模板文件不存在: {template_file}")

            return False

        except Exception as e:
            self.logger.error(f"OpenCV点击'添加到通讯录'按钮失败: {e}")
            return False

    def pre_clear_dialogs(self):
        """在开始操作前预先清理所有可能的弹窗和确定按钮"""
        try:
            self.logger.info("🧹 预清理：检查并关闭所有可能的弹窗...")

            # 检查全局终止标志
            from apis import PROCESSING_CANCELLED
            if PROCESSING_CANCELLED:
                return False

            cleared_count = 0
            max_attempts = 3  # 最多尝试3次清理

            for attempt in range(max_attempts):
                self.logger.info(f"🔍 第{attempt + 1}次清理尝试...")

                # 方法1：使用OpenCV检测并关闭弹窗
                try:
                    found, dialog_region = self.opencv_check_user_not_found()
                    if found:
                        if self.opencv_click_confirm_button(dialog_region):
                            self.logger.info(f"✅ OpenCV清理了弹窗 (第{attempt + 1}次)")
                            cleared_count += 1
                            time.sleep(0.5)
                            continue
                except:
                    pass

                # 方法2：尝试点击任何确定按钮
                try:
                    if self.opencv_click_confirm_button():
                        self.logger.info(f"✅ OpenCV清理了确定按钮 (第{attempt + 1}次)")
                        cleared_count += 1
                        time.sleep(0.5)
                        continue
                except:
                    pass

                # 方法3：按ESC键清理
                try:
                    import pyautogui
                    pyautogui.press('esc')
                    self.logger.info(f"✅ ESC键清理 (第{attempt + 1}次)")
                    time.sleep(0.3)
                except:
                    pass

                # 如果这次没有找到任何弹窗，说明清理完成
                break

            if cleared_count > 0:
                self.logger.info(f"🎉 预清理完成，共清理了 {cleared_count} 个弹窗/按钮")
            else:
                self.logger.info("✅ 预清理完成，没有发现需要清理的弹窗")

            return True

        except Exception as e:
            self.logger.warning(f"预清理失败: {e}")
            return False

    def check_user_not_found_dialog(self):
        """超快速检查是否出现'无法找到该用户'弹窗 - 使用OpenCV优先"""
        try:
            # 检查全局终止标志
            from apis import PROCESSING_CANCELLED
            if PROCESSING_CANCELLED:
                self.logger.warning("🛑 检测到终止信号，停止检查弹窗")
                return False

            self.logger.info("🚀 使用OpenCV超快速检查弹窗...")

            # 方法1：使用OpenCV快速检测（毫秒级）
            try:
                # 检查是否有opencv方法
                if hasattr(self, 'opencv_check_user_not_found'):
                    found, dialog_region = self.opencv_check_user_not_found()
                    if found:
                        self.logger.info("✅ OpenCV检测到'无法找到用户'弹窗")
                        # 使用OpenCV快速点击确定按钮
                        if self.opencv_click_confirm_button(dialog_region):
                            self.logger.info("✅ OpenCV成功关闭弹窗")
                            return True
                        else:
                            # OpenCV点击失败，回退到uiauto
                            self.close_user_not_found_dialog()
                            return True
                else:
                    self.logger.warning("OpenCV方法不可用，直接使用uiauto")
            except Exception as e:
                self.logger.warning(f"OpenCV检测失败，回退到uiauto: {e}")

            # 不再使用uiauto回退，太慢了！
            self.logger.info("🚫 跳过uiauto检查（太慢），只使用OpenCV")
            return False

        except Exception as e:
            self.logger.warning(f"检查'无法找到用户'弹窗失败: {e}")
            return False

    def close_user_not_found_dialog(self):
        """快速关闭'无法找到用户'弹窗 - 只使用OpenCV和键盘"""
        try:
            # 检查全局终止标志
            from apis import PROCESSING_CANCELLED
            if PROCESSING_CANCELLED:
                self.logger.warning("🛑 检测到终止信号，停止关闭弹窗")
                return False

            self.logger.info("⚡ 超快速关闭弹窗（OpenCV + ESC键）...")

            # 方法1：尝试使用OpenCV点击确定按钮
            try:
                if self.opencv_click_confirm_button():
                    self.logger.info("✅ OpenCV成功关闭弹窗")
                    return True
            except:
                pass

            # 方法2：直接按ESC键关闭（最快）
            try:
                if not PROCESSING_CANCELLED:
                    import pyautogui
                    pyautogui.press('esc')
                    self.logger.info("✅ 使用ESC键关闭弹窗")
                    time.sleep(0.3)  # 很短的等待时间
                    return True
            except:
                pass

            self.logger.warning("❌ 无法关闭弹窗")
            return False

        except Exception as e:
            self.logger.warning(f"关闭弹窗失败: {e}")
            return False

    def add_friend(
        self, search_keyword: str, verify_msg: str = "", remark_name: str = "", timeout: int = 30
    ) -> dict:
        """添加好友"""
        start_time = time.time()

        try:
            self.logger.info(f"🔍 开始添加好友: {search_keyword} (超时: {timeout}秒)")
            self.activate_wechat()

            # 检查超时
            if time.time() - start_time > timeout:
                self.logger.warning(f"⏰ 操作超时: {search_keyword}")
                return {"success": False, "message": "操作超时", "timeout": True}

            # 搜索用户
            if not self.search_contact(search_keyword):
                return {"success": False, "message": "搜索联系人失败"}

            time.sleep(3)  # 等待搜索结果加载

            # 再次检查超时
            if time.time() - start_time > timeout:
                self.logger.warning(f"⏰ 操作超时: {search_keyword}")
                return {"success": False, "message": "操作超时", "timeout": True}

            # 检查是否出现"无法找到该用户"弹窗
            if self.check_user_not_found_dialog():
                return {"success": False, "message": "无法找到该用户", "user_not_found": True}

            # 点击"网络查找微信号"按钮
            network_search_clicked = False
            search_patterns = [
                "网络查找微信号",
                "网络查找手机/QQ号",
                "网络查找",
                "查找微信号",
                "搜索微信号"
            ]

            control_types = ["ButtonControl", "TextControl", "PaneControl", "ListItemControl"]

            for pattern in search_patterns:
                if network_search_clicked:
                    break

                if time.time() - start_time > timeout:
                    return {"success": False, "message": "操作超时", "timeout": True}

                for control_type in control_types:
                    try:
                        if control_type == "ButtonControl":
                            control = self.wechat_window.ButtonControl(Name=pattern)
                        elif control_type == "TextControl":
                            control = self.wechat_window.TextControl(Name=pattern)
                        elif control_type == "PaneControl":
                            control = self.wechat_window.PaneControl(Name=pattern)
                        elif control_type == "ListItemControl":
                            control = self.wechat_window.ListItemControl(Name=pattern)
                        else:
                            continue

                        if control.Exists(0, 0):
                            control.Click()
                            self.logger.info(f"✅ 找到并点击了: {pattern} ({control_type})")
                            network_search_clicked = True
                            time.sleep(3)
                            break
                    except Exception as e:
                        continue

            # 方法2：如果还没找到，尝试模糊匹配
            if not network_search_clicked:
                try:
                    all_controls = self.wechat_window.GetChildren()
                    for control in all_controls:
                        if (
                            hasattr(control, "Name")
                            and control.Name
                            and "网络查找" in control.Name
                        ):
                            try:
                                control.Click()
                                self.logger.info(f"✅ 模糊匹配点击了: {control.Name}")
                                network_search_clicked = True
                                time.sleep(3)
                                break
                            except Exception as e:
                                self.logger.warning(f"点击控件失败: {e}")
                                continue
                except Exception as e:
                    self.logger.warning(f"模糊匹配失败: {e}")

            if not network_search_clicked:
                self.logger.error("❌ 未找到'网络查找微信号'选项")
                return {"success": False, "message": "未找到'网络查找微信号'选项"}

            # 检查超时
            if time.time() - start_time > timeout:
                return {"success": False, "message": "操作超时", "timeout": True}

            # 检查是否出现"无法找到该用户"弹窗
            if self.check_user_not_found_dialog():
                return {"success": False, "message": "无法找到该用户", "user_not_found": True}

            # 查找并点击添加好友按钮
            add_button_found = False

            # 方法1：优先使用OpenCV（超快速）
            self.logger.info("🎯 使用OpenCV查找'添加到通讯录'按钮...")
            try:
                if self.opencv_click_add_friend_button():
                    self.logger.info("✅ OpenCV成功点击了'添加到通讯录'按钮")
                    add_button_found = True
                    time.sleep(1)
                else:
                    self.logger.info("🔄 OpenCV未找到按钮，回退到uiauto...")
            except Exception as e:
                self.logger.warning(f"OpenCV点击失败，回退到uiauto: {e}")

            # 方法2：如果OpenCV失败，使用uiauto（但要快速）
            if not add_button_found:
                add_button_names = ["添加到通讯录", "添加好友", "添加", "Add to Contacts"]

                for button_name in add_button_names:
                    try:
                        if time.time() - start_time > timeout:
                            return {"success": False, "message": "操作超时", "timeout": True}

                        self.logger.info(f"🔍 正在查找按钮: {button_name}")
                        add_button = self.wechat_window.ButtonControl(Name=button_name)
                        if add_button.Exists(0, 0):
                            add_button.Click()
                            self.logger.info(f"✅ uiauto找到并点击了'{button_name}'按钮")
                            add_button_found = True
                            time.sleep(1)  # 减少等待时间
                            break
                        else:
                            self.logger.info(f"❌ 未找到按钮: {button_name}")
                    except Exception as e:
                        self.logger.warning(f"查找按钮失败 {button_name}: {e}")
                        continue

            if not add_button_found:
                return {"success": False, "message": "未找到添加好友按钮，可能已经是好友"}

            # 等待添加好友页面加载
            time.sleep(2)

            # 截图保存当前状态
            try:
                import pyautogui
                screenshot = pyautogui.screenshot()
                screenshot_path = f"screenshots/add_friend_{search_keyword}_{int(time.time())}.png"
                os.makedirs("screenshots", exist_ok=True)
                screenshot.save(screenshot_path)
                self.logger.info(f"📸 已保存截图: {screenshot_path}")
            except Exception as e:
                self.logger.warning(f"截图失败: {e}")

            # 输入验证消息（增强版）
            if verify_msg:
                self.logger.info(f"📝 准备输入验证消息: {verify_msg}")
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    # 方法1：查找验证消息输入框（多种方式）
                    message_input_found = False

                    # 尝试通过占位符文本查找
                    placeholders = ["验证消息", "申请理由", "请输入验证信息", "说点什么..."]
                    for placeholder in placeholders:
                        try:
                            edit_control = self.wechat_window.EditControl(Name=placeholder)
                            if edit_control.Exists(0, 0):
                                edit_control.Click()
                                time.sleep(0.5)
                                edit_control.SendKeys("{Ctrl}a")  # 全选
                                time.sleep(0.2)
                                edit_control.SendKeys(verify_msg)
                                self.logger.info(f"✅ 通过占位符'{placeholder}'输入验证消息: {verify_msg}")
                                message_input_found = True
                                break
                        except:
                            continue

                    # 方法2：查找所有编辑框，选择最合适的
                    if not message_input_found:
                        try:
                            all_edits = []
                            for control in self.wechat_window.GetChildren():
                                if control.ControlType == auto.ControlType.EditControl:
                                    all_edits.append(control)

                            # 通常验证消息框是较大的编辑框
                            for edit_control in all_edits:
                                try:
                                    rect = edit_control.BoundingRectangle
                                    # 如果编辑框高度大于30像素，可能是验证消息框
                                    if rect.height() > 30:
                                        edit_control.Click()
                                        time.sleep(0.5)
                                        edit_control.SendKeys("{Ctrl}a")
                                        time.sleep(0.2)
                                        edit_control.SendKeys(verify_msg)
                                        self.logger.info(f"✅ 通过大编辑框输入验证消息: {verify_msg}")
                                        message_input_found = True
                                        break
                                except:
                                    continue
                        except:
                            pass

                    # 方法3：使用剪贴板输入（最后的方法）
                    if not message_input_found:
                        try:
                            import pyperclip
                            pyperclip.copy(verify_msg)
                            # 按Ctrl+V粘贴
                            import pyautogui
                            pyautogui.hotkey('ctrl', 'v')
                            self.logger.info(f"✅ 通过剪贴板输入验证消息: {verify_msg}")
                            message_input_found = True
                        except Exception as e:
                            self.logger.warning(f"剪贴板输入失败: {e}")

                    if not message_input_found:
                        self.logger.warning("❌ 未找到验证消息输入框")

                    time.sleep(1)

                except Exception as e:
                    self.logger.warning(f"输入验证消息失败: {e}")

            # 输入备注名称（如果提供）
            if remark_name:
                self.logger.info(f"📝 准备输入备注名称: {remark_name}")
                try:
                    # 查找备注输入框
                    remark_patterns = ["备注名", "备注", "Remark"]
                    for pattern in remark_patterns:
                        try:
                            remark_control = self.wechat_window.EditControl(Name=pattern)
                            if remark_control.Exists(0, 0):
                                remark_control.Click()
                                time.sleep(0.5)
                                remark_control.SendKeys("{Ctrl}a")
                                time.sleep(0.2)
                                remark_control.SendKeys(remark_name)
                                self.logger.info(f"✅ 输入备注名称: {remark_name}")
                                break
                        except:
                            continue
                except Exception as e:
                    self.logger.warning(f"输入备注名称失败: {e}")

            # 点击确定按钮发送申请
            confirm_button_names = ["确定", "发送", "Send", "OK"]
            confirm_clicked = False

            for button_name in confirm_button_names:
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    confirm_button = self.wechat_window.ButtonControl(Name=button_name)
                    if confirm_button.Exists(0, 0):
                        confirm_button.Click()
                        self.logger.info(f"✅ 点击了'{button_name}'按钮")
                        confirm_clicked = True
                        time.sleep(2)
                        break
                except:
                    continue

            if confirm_clicked:
                self.logger.info(f"🎉 成功发送好友申请给: {search_keyword}")
                return {"success": True, "message": f"已成功发送好友申请给: {search_keyword}"}
            else:
                return {"success": False, "message": "未找到确定按钮"}

        except Exception as e:
            self.logger.error(f"添加好友失败: {e}")
            return {"success": False, "message": f"添加好友失败: {str(e)}"}

    def add_friend_hybrid(self, search_keyword: str, verify_msg: str = "", remark_name: str = "", timeout: int = 30):
        """
        混合模式添加好友：
        1. 使用wxauto快速定位搜索框并输入微信号
        2. 使用uiauto完成后续所有操作（点击网络查找按钮、添加好友等）
        """
        start_time = time.time()

        try:
            self.logger.info(f"🔄 使用混合模式添加好友: {search_keyword}")

            # 第一步：使用wxauto快速输入微信号
            try:
                import wxauto
                wx = wxauto.WeChat()

                self.logger.info("📱 步骤1: 使用wxauto快速输入微信号...")
                # 激活微信窗口
                self.activate_wechat()
                time.sleep(1)

                # 使用wxauto的搜索功能快速输入
                wx.Search(search_keyword)
                self.logger.info(f"✅ wxauto已输入微信号: {search_keyword}")
                time.sleep(2)

            except Exception as e:
                self.logger.warning(f"wxauto输入失败，回退到uiauto输入: {e}")
                # 回退到uiauto输入方法
                if not self.search_contact(search_keyword):
                    return {"success": False, "message": "搜索联系人失败"}
                time.sleep(2)

            # 检查超时
            if time.time() - start_time > timeout:
                return {"success": False, "message": "操作超时", "timeout": True}

            # 第二步：使用uiauto完成后续所有操作
            # 直接调用原有的add_friend方法中的uiauto逻辑
            self.logger.info("🎯 步骤2: 使用uiauto完成后续所有操作...")

            # 使用原有的网络查找逻辑
            network_search_clicked = False

            # 方法1：精确匹配查找
            search_patterns = [
                "网络查找微信号",
                "网络查找手机/QQ号",
                "网络查找",
                "查找微信号"
            ]

            control_types = ["ButtonControl", "TextControl", "PaneControl", "ListItemControl"]

            for pattern in search_patterns:
                if network_search_clicked:
                    break

                if time.time() - start_time > timeout:
                    return {"success": False, "message": "操作超时", "timeout": True}

                for control_type in control_types:
                    try:
                        if control_type == "ButtonControl":
                            control = self.wechat_window.ButtonControl(Name=pattern)
                        elif control_type == "TextControl":
                            control = self.wechat_window.TextControl(Name=pattern)
                        elif control_type == "PaneControl":
                            control = self.wechat_window.PaneControl(Name=pattern)
                        elif control_type == "ListItemControl":
                            control = self.wechat_window.ListItemControl(Name=pattern)
                        else:
                            continue

                        if control.Exists(0, 0):
                            control.Click()
                            self.logger.info(f"✅ 找到并点击了: {pattern} ({control_type})")
                            network_search_clicked = True
                            time.sleep(3)
                            break
                    except Exception as e:
                        continue

            # 方法2：模糊匹配（包含"网络查找"的任何控件）
            if not network_search_clicked:
                self.logger.info("🔍 尝试模糊匹配查找包含'网络查找'的控件...")
                try:
                    all_controls = self.wechat_window.GetChildren()
                    for control in all_controls:
                        if (
                            hasattr(control, "Name")
                            and control.Name
                            and "网络查找" in control.Name
                        ):
                            try:
                                control.Click()
                                self.logger.info(f"✅ 模糊匹配点击了: {control.Name}")
                                network_search_clicked = True
                                time.sleep(3)
                                break
                            except Exception as e:
                                self.logger.warning(f"点击控件失败: {e}")
                                continue
                except Exception as e:
                    self.logger.warning(f"模糊匹配失败: {e}")

            if not network_search_clicked:
                self.logger.info("🔄 未找到'网络查找'按钮，可能直接显示了用户信息，尝试直接查找'添加到通讯录'按钮...")
                # 不返回错误，继续查找添加按钮

            # 检查终止标志
            from apis import PROCESSING_CANCELLED
            if PROCESSING_CANCELLED:
                self.logger.warning("🛑 检测到终止信号，停止混合模式操作")
                return {"success": False, "message": "操作已终止", "cancelled": True}

            # 检查超时
            if time.time() - start_time > timeout:
                return {"success": False, "message": "操作超时", "timeout": True}

            # 检查是否出现"无法找到该用户"弹窗
            if self.check_user_not_found_dialog():
                return {"success": False, "message": "无法找到该用户", "user_not_found": True}

            # 第三步：查找并点击添加好友按钮
            self.logger.info("🎯 步骤3: 查找并点击添加好友按钮...")

            add_button_found = False

            # 方法1：优先使用OpenCV专门方法
            self.logger.info("🎯 尝试使用OpenCV点击'添加到通讯录'按钮...")
            try:
                if self.opencv_click_add_friend_button():
                    self.logger.info("✅ OpenCV成功点击了'添加到通讯录'按钮")
                    add_button_found = True
                    time.sleep(1)
                else:
                    self.logger.info("🔄 OpenCV未找到按钮，回退到uiauto...")
            except Exception as e:
                self.logger.warning(f"OpenCV点击失败，回退到uiauto: {e}")

            # 方法2：如果OpenCV失败，使用uiauto
            if not add_button_found:
                add_button_names = ["添加到通讯录", "添加好友", "添加", "Add to Contacts"]

                for button_name in add_button_names:
                    try:
                        # 检查终止标志
                        from apis import PROCESSING_CANCELLED
                        if PROCESSING_CANCELLED:
                            self.logger.warning("🛑 检测到终止信号，停止查找添加按钮")
                            return {"success": False, "message": "操作已终止", "cancelled": True}

                        if time.time() - start_time > timeout:
                            return {"success": False, "message": "操作超时", "timeout": True}

                        add_button = self.wechat_window.ButtonControl(Name=button_name)
                        if add_button.Exists(0, 0):
                            add_button.Click()
                            self.logger.info(f"✅ uiauto找到并点击了'{button_name}'按钮")
                            add_button_found = True
                            time.sleep(1)  # 减少等待时间
                            break
                    except:
                        continue

            if not add_button_found:
                return {"success": False, "message": "未找到添加好友按钮，可能已经是好友"}

            # 第四步：输入验证消息和备注（增强版）
            # 等待页面加载
            time.sleep(2)

            # 截图保存当前状态
            try:
                import pyautogui
                screenshot = pyautogui.screenshot()
                screenshot_path = f"screenshots/add_friend_hybrid_{search_keyword}_{int(time.time())}.png"
                os.makedirs("screenshots", exist_ok=True)
                screenshot.save(screenshot_path)
                self.logger.info(f"📸 混合模式已保存截图: {screenshot_path}")
            except Exception as e:
                self.logger.warning(f"截图失败: {e}")

            if verify_msg:
                self.logger.info(f"📝 混合模式准备输入验证消息: {verify_msg}")
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    # 使用增强的验证消息输入方法
                    message_input_found = False

                    # 方法1：通过占位符查找
                    placeholders = ["验证消息", "申请理由", "请输入验证信息", "说点什么..."]
                    for placeholder in placeholders:
                        try:
                            edit_control = self.wechat_window.EditControl(Name=placeholder)
                            if edit_control.Exists(0, 0):
                                edit_control.Click()
                                time.sleep(0.5)
                                edit_control.SendKeys("{Ctrl}a")
                                time.sleep(0.2)
                                edit_control.SendKeys(verify_msg)
                                self.logger.info(f"✅ 混合模式通过占位符输入验证消息: {verify_msg}")
                                message_input_found = True
                                break
                        except:
                            continue

                    # 方法2：剪贴板输入
                    if not message_input_found:
                        try:
                            import pyperclip
                            pyperclip.copy(verify_msg)
                            import pyautogui
                            pyautogui.hotkey('ctrl', 'v')
                            self.logger.info(f"✅ 混合模式通过剪贴板输入验证消息: {verify_msg}")
                            message_input_found = True
                        except Exception as e:
                            self.logger.warning(f"剪贴板输入失败: {e}")

                    time.sleep(1)
                except Exception as e:
                    self.logger.warning(f"混合模式输入验证消息失败: {e}")

            # 输入备注名称
            if remark_name:
                self.logger.info(f"📝 混合模式准备输入备注名称: {remark_name}")
                try:
                    remark_patterns = ["备注名", "备注", "Remark"]
                    for pattern in remark_patterns:
                        try:
                            remark_control = self.wechat_window.EditControl(Name=pattern)
                            if remark_control.Exists(0, 0):
                                remark_control.Click()
                                time.sleep(0.5)
                                remark_control.SendKeys("{Ctrl}a")
                                time.sleep(0.2)
                                remark_control.SendKeys(remark_name)
                                self.logger.info(f"✅ 混合模式输入备注名称: {remark_name}")
                                break
                        except:
                            continue
                except Exception as e:
                    self.logger.warning(f"混合模式输入备注名称失败: {e}")

            # 第五步：点击确定按钮发送申请
            confirm_button_names = ["确定", "发送", "Send", "OK"]
            confirm_clicked = False

            for button_name in confirm_button_names:
                try:
                    # 检查终止标志
                    from apis import PROCESSING_CANCELLED
                    if PROCESSING_CANCELLED:
                        self.logger.warning("🛑 检测到终止信号，停止点击确定按钮")
                        return {"success": False, "message": "操作已终止", "cancelled": True}

                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    confirm_button = self.wechat_window.ButtonControl(Name=button_name)
                    if confirm_button.Exists(0, 0):
                        confirm_button.Click()
                        self.logger.info(f"✅ 点击了'{button_name}'按钮")
                        confirm_clicked = True
                        time.sleep(1)  # 减少等待时间
                        break
                except:
                    continue

            if confirm_clicked:
                self.logger.info(f"🎉 混合模式成功发送好友申请给: {search_keyword}")
                return {"success": True, "message": f"混合模式成功发送好友申请给: {search_keyword}"}
            else:
                return {"success": False, "message": "未找到确定按钮"}

        except Exception as e:
            self.logger.error(f"混合模式添加好友失败: {e}")
            return {"success": False, "message": f"混合模式添加好友失败: {str(e)}"}

    def get_contact_list(self) -> List[str]:
        """获取联系人列表"""
        results = []
        try:
            self.activate_wechat()

            # 等待一段时间，避免操作过快
            time.sleep(3)

        except Exception as e:
            self.logger.error(f"获取联系人列表失败: {e}")

        return results


def main():
    """主函数示例"""
    wechat = WeChatUIAutomation()

    if not wechat.find_wechat_window():
        print("请先打开微信客户端")
        return

    print("微信UI自动化工具")
    print("1. 发送消息")
    print("2. 添加好友")
    print("3. 获取联系人列表")

    choice = input("请选择功能 (1-3): ")

    if choice == "1":
        contact = input("请输入联系人名称: ")
        message = input("请输入消息内容: ")
        wechat.send_message(contact, message)

    elif choice == "2":
        keyword = input("请输入搜索关键词: ")
        verify_msg = input("请输入验证消息: ")
        wechat.add_friend(keyword, verify_msg)

    elif choice == "3":
        contacts = wechat.get_contact_list()
        print(f"联系人列表: {contacts[:10]}...")  # 只显示前10个


if __name__ == "__main__":
    main()
