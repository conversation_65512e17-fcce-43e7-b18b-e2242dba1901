#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于uiautomation的微信自动化替代方案
完全免费，功能强大
"""

import time
import logging
import os
from datetime import datetime
from typing import List, Dict, Optional

try:
    import uiautomation as auto

    UIAUTOMATION_AVAILABLE = True
except ImportError as e:
    # 检查是否在虚拟环境中
    import sys

    if ".venv" in sys.executable or "venv" in sys.executable:
        print(f"⚠️ 虚拟环境中uiautomation不可用: {e}")
        print("💡 建议：退出虚拟环境或运行: pip install uiautomation")
    else:
        print(f"⚠️ uiautomation导入失败: {e}")
        print("请安装uiautomation: pip install uiautomation")
    UIAUTOMATION_AVAILABLE = False
    auto = None


class WeChatUIAutomation:
    """基于uiautomation的微信自动化类"""

    def __init__(self):
        """初始化"""
        self.wechat_window = None
        self.uiautomation_available = UIAUTOMATION_AVAILABLE
        self.setup_logging()

        if not self.uiautomation_available:
            self.logger.error("uiautomation不可用，请安装: pip install uiautomation")

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s",
            handlers=[
                logging.FileHandler("wechat_ui_automation.log", encoding="utf-8"),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def find_wechat_window(self) -> bool:
        """查找微信窗口"""
        if not self.uiautomation_available:
            self.logger.error("uiautomation不可用")
            return False

        try:
            # 查找微信主窗口
            self.wechat_window = auto.WindowControl(
                searchDepth=1, Name="微信", ClassName="WeChatMainWndForPC"
            )
            if self.wechat_window.Exists(0, 0):
                self.logger.info("找到微信窗口")
                return True

            # 尝试其他可能的窗口名称
            self.wechat_window = auto.WindowControl(
                searchDepth=1, ClassName="WeChatMainWndForPC"
            )
            if self.wechat_window.Exists(0, 0):
                self.logger.info("找到微信窗口（通过类名）")
                return True

            self.logger.error("未找到微信窗口，请确保微信已打开")
            return False

        except Exception as e:
            self.logger.error(f"查找微信窗口失败: {e}")
            return False

    def activate_wechat(self):
        """激活微信窗口"""
        if self.wechat_window:
            try:
                self.wechat_window.SetActive()
                time.sleep(0.5)
                self.logger.info("微信窗口已激活")
            except Exception as e:
                self.logger.warning(f"激活微信窗口失败: {e}")
                # 尝试点击窗口来激活
                try:
                    rect = self.wechat_window.BoundingRectangle
                    center_x = rect.left + rect.width() // 2
                    center_y = rect.top + rect.height() // 2
                    self.wechat_window.Click(center_x, center_y)
                    time.sleep(0.5)
                    self.logger.info("通过点击激活微信窗口")
                except Exception as e2:
                    self.logger.error(f"点击激活也失败: {e2}")

    def search_contact(self, contact_name: str) -> bool:
        """搜索联系人"""
        try:
            self.activate_wechat()

            # 查找搜索框的多种方式
            search_box = None
            search_methods = [
                lambda: self.wechat_window.EditControl(Name="搜索"),
                lambda: self.wechat_window.EditControl(AutomationId="SearchEditBox"),
                lambda: self.wechat_window.EditControl(ClassName="EditControl"),
                # 通过遍历所有编辑框找到搜索框
                lambda: next(
                    (
                        edit
                        for edit in self.wechat_window.GetChildren()
                        if edit.ControlType == auto.ControlType.EditControl
                    ),
                    None,
                ),
            ]

            for method in search_methods:
                try:
                    search_box = method()
                    if search_box and search_box.Exists(0, 0):
                        self.logger.info(f"找到搜索框: {search_box.Name}")
                        break
                except Exception as e:
                    continue

            if search_box and search_box.Exists(0, 0):
                # 尝试多种输入方法
                input_success = False

                # 方法1：标准方法
                try:
                    search_box.SetFocus()
                    time.sleep(0.3)
                    search_box.Click()
                    time.sleep(0.5)
                    search_box.SendKeys("{Ctrl}a")
                    time.sleep(0.2)
                    search_box.SendKeys(contact_name)
                    time.sleep(2)
                    self.logger.info(f"搜索联系人 (标准方法): {contact_name}")
                    input_success = True
                except Exception as e:
                    self.logger.warning(f"标准输入方法失败: {e}")

                # 方法2：备用方法
                if not input_success:
                    try:
                        search_box.Click()
                        time.sleep(0.5)
                        auto.SendKeys("{Ctrl}a")
                        time.sleep(0.3)
                        auto.SendKeys(contact_name)
                        time.sleep(2)
                        self.logger.info(f"搜索联系人 (备用方法): {contact_name}")
                        input_success = True
                    except Exception as e:
                        self.logger.warning(f"备用输入方法失败: {e}")

                # 方法3：强制清空后输入
                if not input_success:
                    try:
                        search_box.Click()
                        time.sleep(0.5)
                        # 多次删除确保清空
                        for _ in range(10):
                            auto.SendKeys("{Backspace}")
                            time.sleep(0.1)
                        auto.SendKeys(contact_name)
                        time.sleep(2)
                        self.logger.info(f"搜索联系人 (强制清空方法): {contact_name}")
                        input_success = True
                    except Exception as e:
                        self.logger.warning(f"强制清空方法失败: {e}")

                if input_success:
                    return True
                else:
                    self.logger.error("所有搜索框输入方法都失败，尝试快捷键")
                    # 尝试使用全局快捷键
                    try:
                        auto.SendKeys("{Ctrl}f")  # 微信搜索快捷键
                        time.sleep(0.5)
                        auto.SendKeys(contact_name)
                        time.sleep(2)
                        self.logger.info(f"使用快捷键搜索联系人: {contact_name}")
                        return True
                    except Exception as e2:
                        self.logger.error(f"快捷键搜索也失败: {e2}")
                        return False
            else:
                self.logger.error("未找到搜索框")
                # 尝试使用快捷键作为备选方案
                try:
                    auto.SendKeys("{Ctrl}f")
                    time.sleep(0.5)
                    auto.SendKeys(contact_name)
                    time.sleep(1)
                    self.logger.info(f"使用快捷键搜索联系人: {contact_name}")
                    return True
                except Exception as e:
                    self.logger.error(f"快捷键搜索失败: {e}")
                    return False

        except Exception as e:
            self.logger.error(f"搜索联系人失败: {e}")
            return False

    def send_message(self, contact_name: str, message: str) -> bool:
        """发送消息"""
        try:
            # 搜索并打开联系人
            if not self.search_contact(contact_name):
                return False

            # 按回车打开聊天窗口
            auto.SendKeys("{Enter}")
            time.sleep(2)  # 等待聊天窗口打开

            # 查找消息输入框的多种方式
            msg_edit = None
            input_methods = [
                # 尝试通过不同的属性查找输入框
                lambda: self.wechat_window.EditControl(ClassName="EditControl"),
                lambda: self.wechat_window.EditControl(Name=""),
                # 查找所有编辑框，通常消息输入框是最后一个或最大的
                lambda: self._find_message_input_box(),
            ]

            for method in input_methods:
                try:
                    msg_edit = method()
                    if msg_edit and msg_edit.Exists(0, 0):
                        self.logger.info("找到消息输入框")
                        break
                except Exception:
                    continue

            if msg_edit and msg_edit.Exists(0, 0):
                try:
                    # 点击消息输入框
                    msg_edit.Click()
                    time.sleep(0.5)

                    # 输入消息
                    msg_edit.SendKeys(message)
                    time.sleep(0.5)

                    # 发送消息
                    auto.SendKeys("{Enter}")
                    self.logger.info(f"向 {contact_name} 发送消息: {message}")
                    return True
                except Exception as e:
                    self.logger.error(f"操作消息输入框失败: {e}")
                    # 尝试直接输入
                    try:
                        auto.SendKeys(message)
                        time.sleep(0.5)
                        auto.SendKeys("{Enter}")
                        self.logger.info(
                            f"使用直接输入向 {contact_name} 发送消息: {message}"
                        )
                        return True
                    except Exception as e2:
                        self.logger.error(f"直接输入也失败: {e2}")
                        return False
            else:
                self.logger.error("未找到消息输入框，尝试直接输入")
                try:
                    # 直接输入消息作为备选方案
                    auto.SendKeys(message)
                    time.sleep(0.5)
                    auto.SendKeys("{Enter}")
                    self.logger.info(
                        f"使用直接输入向 {contact_name} 发送消息: {message}"
                    )
                    return True
                except Exception as e:
                    self.logger.error(f"直接输入失败: {e}")
                    return False

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    def _find_message_input_box(self):
        """查找消息输入框的辅助方法"""
        try:
            # 获取所有编辑框
            edit_controls = []
            for control in self.wechat_window.GetChildren():
                if control.ControlType == auto.ControlType.EditControl:
                    edit_controls.append(control)

            if edit_controls:
                # 通常消息输入框是最后一个或者位置最靠下的
                return edit_controls[-1]

            return None
        except Exception:
            return None

    def add_friend(
        self, search_keyword: str, verify_msg: str = "", remark_name: str = "", timeout: int = 30
    ) -> dict:
        """添加好友"""
        start_time = time.time()

        try:
            self.logger.info(f"🔍 开始添加好友: {search_keyword} (超时: {timeout}秒)")
            self.activate_wechat()

            # 检查超时
            if time.time() - start_time > timeout:
                self.logger.warning(f"⏰ 操作超时: {search_keyword}")
                return {"success": False, "message": "操作超时", "timeout": True}

            # 搜索用户
            if not self.search_contact(search_keyword):
                return {"success": False, "message": "搜索联系人失败"}

            time.sleep(3)  # 等待搜索结果加载

            # 再次检查超时
            if time.time() - start_time > timeout:
                self.logger.warning(f"⏰ 操作超时: {search_keyword}")
                return {"success": False, "message": "操作超时", "timeout": True}

            # 检查是否出现"无法找到该用户"弹窗
            if self.check_user_not_found_dialog():
                self.logger.warning(f"❌ 无法找到用户: {search_keyword}")
                return {"success": False, "message": "无法找到该用户", "user_not_found": True}

            # 关键步骤：点击"网络查找微信号"
            self.logger.info("查找'网络查找微信号'选项...")

            # 先调试：列出所有可能的控件
            self.logger.info("调试：列出搜索结果区域的控件...")
            try:
                all_controls = self.wechat_window.GetChildren()
                for i, control in enumerate(all_controls[:30]):  # 显示前30个控件
                    if hasattr(control, "Name") and control.Name:
                        control_type = (
                            control.ControlType
                            if hasattr(control, "ControlType")
                            else "Unknown"
                        )
                        self.logger.info(
                            f"  控件{i+1}: '{control.Name}' ({control_type})"
                        )
                        if "网络" in control.Name or "查找" in control.Name:
                            self.logger.info(f"    ⭐ 可能的目标控件: {control.Name}")
            except Exception as e:
                self.logger.warning(f"调试信息获取失败: {e}")

            network_search_clicked = False

            # 方法1：尝试查找各种控件类型
            search_patterns = ["网络查找微信号", "网络查找", "查找微信号"]
            control_types = [
                "ButtonControl",
                "TextControl",
                "PaneControl",
                "ListItemControl",
            ]

            for pattern in search_patterns:
                if network_search_clicked:
                    break

                for control_type in control_types:
                    try:
                        if control_type == "ButtonControl":
                            control = self.wechat_window.ButtonControl(Name=pattern)
                        elif control_type == "TextControl":
                            control = self.wechat_window.TextControl(Name=pattern)
                        elif control_type == "PaneControl":
                            control = self.wechat_window.PaneControl(Name=pattern)
                        elif control_type == "ListItemControl":
                            control = self.wechat_window.ListItemControl(Name=pattern)
                        else:
                            continue

                        if control.Exists(0, 0):
                            control.Click()
                            self.logger.info(
                                f"✅ 找到并点击了: {pattern} ({control_type})"
                            )
                            network_search_clicked = True
                            time.sleep(3)
                            break
                    except Exception as e:
                        continue

            # 方法2：如果还没找到，尝试模糊匹配
            if not network_search_clicked:
                try:
                    all_controls = self.wechat_window.GetChildren()
                    for control in all_controls:
                        if (
                            hasattr(control, "Name")
                            and control.Name
                            and (
                                "网络查找" in control.Name
                                or "查找微信号" in control.Name
                            )
                        ):
                            try:
                                control.Click()
                                self.logger.info(f"✅ 模糊匹配点击了: {control.Name}")
                                network_search_clicked = True
                                time.sleep(3)
                                break
                            except Exception as e:
                                self.logger.warning(f"点击控件失败: {e}")
                                continue
                except Exception as e:
                    self.logger.warning(f"模糊匹配失败: {e}")

            if not network_search_clicked:
                self.logger.error("❌ 未找到'网络查找微信号'选项")
                self.logger.info("💡 请确保搜索的是微信号而不是昵称")
                return {"success": False, "message": "未找到'网络查找微信号'选项"}

            self.logger.info("✅ 成功点击网络查找，等待用户卡片加载...")

            # 检查超时
            if time.time() - start_time > timeout:
                self.logger.warning(f"⏰ 操作超时: {search_keyword}")
                return {"success": False, "message": "操作超时", "timeout": True}

            # 调试：列出当前页面的所有控件
            self.logger.info("调试：列出当前页面的控件...")
            try:
                all_controls = self.wechat_window.GetChildren()
                button_count = 0
                for i, control in enumerate(all_controls[:20]):  # 只显示前20个
                    if hasattr(control, "Name") and control.Name:
                        control_type = (
                            control.ControlType
                            if hasattr(control, "ControlType")
                            else "Unknown"
                        )
                        self.logger.info(
                            f"  控件{i+1}: {control.Name} ({control_type})"
                        )
                        if "Button" in str(control_type):
                            button_count += 1
                self.logger.info(f"找到 {button_count} 个按钮控件")
            except Exception as e:
                self.logger.warning(f"调试信息获取失败: {e}")

            # 现在查找"添加到通讯录"绿色按钮（在用户卡片中）
            add_button_found = False

            self.logger.info("查找绿色'添加到通讯录'按钮...")

            # 智能等待和查找"添加到通讯录"按钮
            max_wait_time = 15  # 最多等待15秒
            wait_interval = 2  # 每2秒检查一次

            for wait_count in range(max_wait_time // wait_interval):
                self.logger.info(
                    f"等待并查找'添加到通讯录'按钮... ({wait_count + 1}/{max_wait_time // wait_interval})"
                )
                time.sleep(wait_interval)

                # 方法1：在整个桌面范围内查找
                try:
                    desktop = auto.GetRootControl()
                    add_button = desktop.ButtonControl(Name="添加到通讯录")
                    if add_button.Exists(0, 0):
                        self.logger.info("✅ 在桌面范围内找到'添加到通讯录'按钮！")
                        add_button.Click()
                        add_button_found = True
                        time.sleep(3)
                        break
                except Exception:
                    pass

                # 方法2：在微信窗口内查找
                try:
                    wechat_add_button = self.wechat_window.ButtonControl(
                        Name="添加到通讯录"
                    )
                    if wechat_add_button.Exists(0, 0):
                        self.logger.info("✅ 在微信窗口内找到'添加到通讯录'按钮！")
                        wechat_add_button.Click()
                        add_button_found = True
                        time.sleep(3)
                        break
                except Exception:
                    pass

                # 方法3：查找所有窗口中的按钮
                try:
                    all_windows = desktop.GetChildren()
                    for window in all_windows:
                        try:
                            add_btn = window.ButtonControl(Name="添加到通讯录")
                            if add_btn.Exists(0, 0):
                                self.logger.info(
                                    f"✅ 在窗口 {window.Name} 中找到'添加到通讯录'按钮！"
                                )
                                add_btn.Click()
                                add_button_found = True
                                time.sleep(3)
                                break
                        except:
                            continue
                    if add_button_found:
                        break
                except Exception:
                    pass

            # 如果还是没找到，进行最后的调试
            if not add_button_found:
                self.logger.info("最后调试：列出所有可能的控件...")
                try:
                    desktop = auto.GetRootControl()

                    def find_add_controls(parent, depth=0):
                        if depth > 2:
                            return []
                        found = []
                        try:
                            children = parent.GetChildren()
                            for child in children:
                                if (
                                    hasattr(child, "Name")
                                    and child.Name
                                    and "添加" in child.Name
                                ):
                                    found.append((child.Name, child))
                                    self.logger.info(
                                        f"找到包含'添加'的控件: {child.Name}"
                                    )
                                found.extend(find_add_controls(child, depth + 1))
                        except:
                            pass
                        return found

                    all_add_controls = find_add_controls(desktop)

                    # 尝试点击包含"通讯录"的控件
                    for name, control in all_add_controls:
                        if "通讯录" in name:
                            try:
                                control.Click()
                                self.logger.info(f"✅ 尝试点击: {name}")
                                add_button_found = True
                                time.sleep(3)
                                break
                            except:
                                continue

                except Exception as e:
                    self.logger.warning(f"最终调试失败: {e}")

            # 尝试多种方式查找"添加到通讯录"按钮
            add_button_names = [
                "添加到通讯录",
                "添加好友",
                "Add to Contacts",
                "Add Friend",
            ]

            # 方法1：标准按钮查找
            for button_name in add_button_names:
                try:
                    add_button = self.wechat_window.ButtonControl(Name=button_name)
                    if add_button.Exists(0, 0):
                        self.logger.info(f"✅ 找到绿色按钮: {button_name}")
                        add_button.Click()
                        add_button_found = True
                        time.sleep(3)  # 等待对话框弹出
                        break
                except Exception:
                    continue

            # 方法2：递归查找所有子控件中的"添加到通讯录"按钮
            if not add_button_found:
                try:
                    self.logger.info("递归查找'添加到通讯录'按钮...")

                    def find_add_button_recursive(parent_control, depth=0):
                        if depth > 3:  # 限制递归深度
                            return None

                        try:
                            children = parent_control.GetChildren()
                            for child in children:
                                if hasattr(child, "Name") and child.Name:
                                    if child.Name == "添加到通讯录":
                                        return child
                                    # 递归查找子控件
                                    result = find_add_button_recursive(child, depth + 1)
                                    if result:
                                        return result
                        except:
                            pass
                        return None

                    add_button = find_add_button_recursive(self.wechat_window)
                    if add_button:
                        add_button.Click()
                        self.logger.info("✅ 递归找到并点击了'添加到通讯录'按钮")
                        add_button_found = True
                        time.sleep(3)

                except Exception as e:
                    self.logger.warning(f"递归查找失败: {e}")

            # 方法3：使用坐标点击（如果知道按钮大概位置）
            if not add_button_found:
                try:
                    self.logger.info("尝试通过模糊匹配查找...")
                    all_controls = self.wechat_window.GetChildren()
                    for control in all_controls:
                        if (
                            hasattr(control, "Name")
                            and control.Name
                            and ("添加" in control.Name and "通讯录" in control.Name)
                        ):
                            try:
                                control.Click()
                                self.logger.info(f"✅ 模糊匹配找到: {control.Name}")
                                add_button_found = True
                                time.sleep(3)
                                break
                            except Exception as e:
                                continue
                except Exception as e:
                    self.logger.warning(f"模糊匹配失败: {e}")

            if not add_button_found:
                # 尝试查找包含"添加"文字的按钮
                try:
                    all_buttons = self.wechat_window.GetChildren()
                    for control in all_buttons:
                        if (
                            hasattr(control, "Name")
                            and control.Name
                            and ("添加" in control.Name or "Add" in control.Name)
                        ):
                            self.logger.info(f"找到可能的添加按钮: {control.Name}")
                            control.Click()
                            add_button_found = True
                            time.sleep(1)
                            break
                except Exception as e:
                    self.logger.warning(f"查找添加按钮失败: {e}")

            if add_button_found:
                self.logger.info("✅ 成功点击'添加到通讯录'按钮，等待对话框...")

                # 等待"申请添加好友"对话框出现
                time.sleep(3)

                # 简化的输入方法：使用键盘输入
                if verify_msg or remark_name:
                    try:
                        self.logger.info("使用键盘输入验证消息和备注名...")

                        # 等待对话框完全加载
                        time.sleep(2)

                        if verify_msg:
                            # 按Tab键定位到第一个输入框（验证消息）
                            auto.SendKeys("{Tab}")
                            time.sleep(0.5)
                            # 清空并输入验证消息
                            auto.SendKeys("{Ctrl}a")
                            time.sleep(0.3)
                            auto.SendKeys(verify_msg)
                            time.sleep(0.5)
                            self.logger.info(f"✅ 输入验证消息: {verify_msg}")

                        if remark_name:
                            # 按Tab键定位到第二个输入框（备注名）
                            auto.SendKeys("{Tab}")
                            time.sleep(0.5)
                            # 清空并输入备注名
                            auto.SendKeys("{Ctrl}a")
                            time.sleep(0.3)
                            auto.SendKeys(remark_name)
                            time.sleep(0.5)
                            self.logger.info(f"✅ 输入备注名: {remark_name}")

                    except Exception as e:
                        self.logger.warning(f"键盘输入失败: {e}")

                        # 备用方法：尝试直接查找编辑框
                        try:
                            self.logger.info("尝试备用方法查找输入框...")

                            # 查找当前活动窗口的编辑框
                            active_window = auto.GetForegroundControl()
                            if active_window:
                                # 查找编辑框
                                edit_controls = []
                                try:

                                    def find_edits(parent, depth=0):
                                        if depth > 3:
                                            return
                                        try:
                                            children = parent.GetChildren()
                                            for child in children:
                                                if hasattr(child, "ControlType") and (
                                                    "Edit" in str(child.ControlType)
                                                    or "Document"
                                                    in str(child.ControlType)
                                                ):
                                                    edit_controls.append(child)
                                                find_edits(child, depth + 1)
                                        except:
                                            pass

                                    find_edits(active_window)
                                    self.logger.info(
                                        f"找到 {len(edit_controls)} 个输入框"
                                    )

                                    # 填写验证消息（第一个输入框）
                                    if verify_msg and len(edit_controls) > 0:
                                        try:
                                            edit_controls[0].Click()
                                            time.sleep(0.5)
                                            edit_controls[0].SendKeys("{Ctrl}a")
                                            time.sleep(0.3)
                                            edit_controls[0].SendKeys(verify_msg)
                                            self.logger.info(
                                                f"✅ 备用方法输入验证消息: {verify_msg}"
                                            )
                                        except:
                                            pass

                                    # 填写备注名（第二个输入框）
                                    if remark_name and len(edit_controls) > 1:
                                        try:
                                            edit_controls[1].Click()
                                            time.sleep(0.5)
                                            edit_controls[1].SendKeys("{Ctrl}a")
                                            time.sleep(0.3)
                                            edit_controls[1].SendKeys(remark_name)
                                            self.logger.info(
                                                f"✅ 备用方法输入备注名: {remark_name}"
                                            )
                                        except:
                                            pass

                                except Exception as e:
                                    self.logger.warning(f"备用方法也失败: {e}")

                        except Exception as e:
                            self.logger.warning(f"备用方法查找失败: {e}")

                # 查找并点击确定按钮
                send_button_found = False
                send_button_names = ["确定", "发送", "Send", "OK", "确认"]

                self.logger.info("查找确定按钮...")

                for button_name in send_button_names:
                    try:
                        send_button = self.wechat_window.ButtonControl(Name=button_name)
                        if send_button.Exists(0, 0):
                            send_button.Click()
                            send_button_found = True
                            self.logger.info(f"✅ 点击了按钮: {button_name}")
                            time.sleep(1)
                            break
                    except Exception as e:
                        self.logger.debug(f"查找按钮 {button_name} 失败: {e}")
                        continue

                if send_button_found:
                    # 截图保存
                    try:
                        self.take_screenshot(search_keyword, "添加好友")
                    except Exception as e:
                        self.logger.warning(f"截图保存失败: {e}")

                    self.logger.info(f"🎉 已成功发送好友申请给: {search_keyword}")
                    if remark_name:
                        self.logger.info(f"📝 备注名: {remark_name}")
                    return {"success": True, "message": f"已成功发送好友申请给: {search_keyword}"}
                else:
                    self.logger.warning("❌ 未找到确定按钮")
                    return {"success": False, "message": "未找到确定按钮"}
            else:
                self.logger.warning(
                    f"未找到添加好友按钮，可能已经是好友: {search_keyword}"
                )
                return {"success": False, "message": f"未找到添加好友按钮，可能已经是好友: {search_keyword}"}

        except Exception as e:
            self.logger.error(f"添加好友失败: {e}")
            return {"success": False, "message": f"添加好友失败: {str(e)}"}

    def check_user_not_found_dialog(self):
        """检查是否出现'无法找到该用户'弹窗"""
        try:
            self.logger.info("检查是否出现'无法找到该用户'弹窗...")

            # 检查可能的弹窗文本
            not_found_texts = [
                "无法找到该用户",
                "请检查你填写的账号是否正确",
                "无法找到",
                "用户不存在",
                "账号不存在"
            ]

            # 方法1：在整个桌面范围内查找
            desktop = auto.GetRootControl()

            for text in not_found_texts:
                try:
                    # 查找包含该文本的控件
                    text_control = desktop.TextControl(Name=text)
                    if text_control.Exists(0, 0):
                        self.logger.info(f"✅ 发现'无法找到用户'弹窗: {text}")
                        self.close_user_not_found_dialog()
                        return True
                except:
                    continue

            # 方法2：查找包含关键词的文本控件
            try:
                all_windows = desktop.GetChildren()
                for window in all_windows:
                    try:
                        text_controls = window.GetChildren()
                        for control in text_controls:
                            if hasattr(control, 'Name') and control.Name:
                                for keyword in ["无法找到", "用户不存在", "账号不存在"]:
                                    if keyword in control.Name:
                                        self.logger.info(f"✅ 发现'无法找到用户'相关文本: {control.Name}")
                                        self.close_user_not_found_dialog()
                                        return True
                    except:
                        continue
            except:
                pass

            return False

        except Exception as e:
            self.logger.warning(f"检查'无法找到用户'弹窗失败: {e}")
            return False

    def close_user_not_found_dialog(self):
        """关闭'无法找到用户'弹窗"""
        try:
            self.logger.info("尝试关闭'无法找到用户'弹窗...")

            # 查找确定按钮
            confirm_button_names = ["确定", "OK", "知道了", "关闭"]
            desktop = auto.GetRootControl()

            for button_name in confirm_button_names:
                try:
                    # 在整个桌面范围内查找确定按钮
                    confirm_button = desktop.ButtonControl(Name=button_name)
                    if confirm_button.Exists(0, 0):
                        confirm_button.Click()
                        self.logger.info(f"✅ 点击了确定按钮: {button_name}")
                        time.sleep(1)
                        return True
                except:
                    continue

            # 如果没找到确定按钮，尝试按ESC键关闭
            try:
                auto.SendKeys("{Esc}")
                self.logger.info("✅ 使用ESC键关闭弹窗")
                time.sleep(1)
                return True
            except:
                pass

            self.logger.warning("❌ 无法关闭'无法找到用户'弹窗")
            return False

        except Exception as e:
            self.logger.warning(f"关闭'无法找到用户'弹窗失败: {e}")
            return False

    def add_friend_hybrid(self, search_keyword: str, verify_msg: str = "", remark_name: str = "", timeout: int = 30):
        """
        混合模式添加好友：
        1. 使用wxauto快速定位搜索框并输入微信号
        2. 使用uiauto完成后续所有操作（点击网络查找按钮、添加好友等）
        """
        start_time = time.time()

        try:
            self.logger.info(f"🔄 使用混合模式添加好友: {search_keyword}")

            # 第一步：使用wxauto快速输入微信号
            try:
                import wxauto
                wx = wxauto.WeChat()

                self.logger.info("📱 步骤1: 使用wxauto快速输入微信号...")
                # 激活微信窗口
                self.activate_wechat()
                time.sleep(1)

                # 使用wxauto的搜索功能快速输入
                wx.Search(search_keyword)
                self.logger.info(f"✅ wxauto已输入微信号: {search_keyword}")
                time.sleep(2)

            except Exception as e:
                self.logger.warning(f"wxauto输入失败，回退到uiauto输入: {e}")
                # 回退到uiauto输入方法
                if not self.search_contact(search_keyword):
                    return {"success": False, "message": "搜索联系人失败"}
                time.sleep(2)

            # 检查超时
            if time.time() - start_time > timeout:
                return {"success": False, "message": "操作超时", "timeout": True}

            # 第二步：使用uiauto完成后续所有操作
            # 直接调用原有的add_friend方法中的uiauto逻辑
            self.logger.info("🎯 步骤2: 使用uiauto完成后续所有操作...")

            # 使用原有的网络查找逻辑
            network_search_clicked = False

            # 方法1：精确匹配查找
            search_patterns = [
                "网络查找微信号",
                "网络查找手机/QQ号",
                "网络查找",
                "查找微信号"
            ]

            control_types = ["ButtonControl", "TextControl", "PaneControl", "ListItemControl"]

            for pattern in search_patterns:
                if network_search_clicked:
                    break

                if time.time() - start_time > timeout:
                    return {"success": False, "message": "操作超时", "timeout": True}

                for control_type in control_types:
                    try:
                        if control_type == "ButtonControl":
                            control = self.wechat_window.ButtonControl(Name=pattern)
                        elif control_type == "TextControl":
                            control = self.wechat_window.TextControl(Name=pattern)
                        elif control_type == "PaneControl":
                            control = self.wechat_window.PaneControl(Name=pattern)
                        elif control_type == "ListItemControl":
                            control = self.wechat_window.ListItemControl(Name=pattern)
                        else:
                            continue

                        if control.Exists(0, 0):
                            control.Click()
                            self.logger.info(f"✅ 找到并点击了: {pattern} ({control_type})")
                            network_search_clicked = True
                            time.sleep(3)
                            break
                    except Exception as e:
                        continue

            # 方法2：模糊匹配（包含"网络查找"的任何控件）
            if not network_search_clicked:
                self.logger.info("🔍 尝试模糊匹配查找包含'网络查找'的控件...")
                try:
                    all_controls = self.wechat_window.GetChildren()
                    for control in all_controls:
                        if (
                            hasattr(control, "Name")
                            and control.Name
                            and "网络查找" in control.Name
                        ):
                            try:
                                control.Click()
                                self.logger.info(f"✅ 模糊匹配点击了: {control.Name}")
                                network_search_clicked = True
                                time.sleep(3)
                                break
                            except Exception as e:
                                self.logger.warning(f"点击控件失败: {e}")
                                continue
                except Exception as e:
                    self.logger.warning(f"模糊匹配失败: {e}")

            if not network_search_clicked:
                return {"success": False, "message": "未找到'网络查找'按钮"}

            # 检查超时
            if time.time() - start_time > timeout:
                return {"success": False, "message": "操作超时", "timeout": True}

            # 检查是否出现"无法找到该用户"弹窗
            if self.check_user_not_found_dialog():
                return {"success": False, "message": "无法找到该用户", "user_not_found": True}

            # 第三步：查找并点击添加好友按钮
            self.logger.info("🎯 步骤3: 查找并点击添加好友按钮...")

            add_button_found = False
            add_button_names = ["添加到通讯录", "添加好友", "添加", "Add to Contacts"]

            for button_name in add_button_names:
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    add_button = self.wechat_window.ButtonControl(Name=button_name)
                    if add_button.Exists(0, 0):
                        add_button.Click()
                        self.logger.info(f"✅ 找到并点击了'{button_name}'按钮")
                        add_button_found = True
                        time.sleep(2)
                        break
                except:
                    continue

            if not add_button_found:
                return {"success": False, "message": "未找到添加好友按钮，可能已经是好友"}

            # 第四步：输入验证消息（如果有）
            if verify_msg:
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    # 查找验证消息输入框
                    edit_control = self.wechat_window.EditControl()
                    if edit_control.Exists(0, 0):
                        edit_control.SetValue(verify_msg)
                        self.logger.info(f"✅ 输入验证消息: {verify_msg}")
                        time.sleep(1)
                except Exception as e:
                    self.logger.warning(f"输入验证消息失败: {e}")

            # 第五步：点击确定按钮发送申请
            confirm_button_names = ["确定", "发送", "Send", "OK"]
            confirm_clicked = False

            for button_name in confirm_button_names:
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    confirm_button = self.wechat_window.ButtonControl(Name=button_name)
                    if confirm_button.Exists(0, 0):
                        confirm_button.Click()
                        self.logger.info(f"✅ 点击了'{button_name}'按钮")
                        confirm_clicked = True
                        time.sleep(2)
                        break
                except:
                    continue

            if confirm_clicked:
                self.logger.info(f"🎉 混合模式成功发送好友申请给: {search_keyword}")
                return {"success": True, "message": f"混合模式成功发送好友申请给: {search_keyword}"}
            else:
                return {"success": False, "message": "未找到确定按钮"}

        except Exception as e:
            self.logger.error(f"混合模式添加好友失败: {e}")
            return {"success": False, "message": f"混合模式添加好友失败: {str(e)}"}

            # 检查超时
            if time.time() - start_time > timeout:
                return {"success": False, "message": "操作超时", "timeout": True}

            # 检查是否出现"无法找到该用户"弹窗
            if self.check_user_not_found_dialog():
                return {"success": False, "message": "无法找到该用户", "user_not_found": True}

            # 第三步：使用uiauto进行后续所有操作（添加好友）
            self.logger.info("🎯 步骤3: 使用uiauto进行添加好友操作...")

            # 查找并点击添加好友按钮
            add_button_found = False
            add_button_names = ["添加到通讯录", "添加好友", "添加", "Add to Contacts"]

            for button_name in add_button_names:
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    add_button = desktop.ButtonControl(Name=button_name)
                    if add_button.Exists(0, 0):
                        add_button.Click()
                        self.logger.info(f"✅ 找到并点击了'{button_name}'按钮")
                        add_button_found = True
                        time.sleep(2)
                        break
                except:
                    continue

            if not add_button_found:
                return {"success": False, "message": "未找到添加好友按钮，可能已经是好友"}

            # 第四步：输入验证消息（如果有）
            if verify_msg:
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    # 查找验证消息输入框
                    edit_control = desktop.EditControl()
                    if edit_control.Exists(0, 0):
                        edit_control.SetValue(verify_msg)
                        self.logger.info(f"✅ 输入验证消息: {verify_msg}")
                        time.sleep(1)
                except Exception as e:
                    self.logger.warning(f"输入验证消息失败: {e}")

            # 第五步：点击确定按钮发送申请
            confirm_button_names = ["确定", "发送", "Send", "OK"]
            confirm_clicked = False

            for button_name in confirm_button_names:
                try:
                    if time.time() - start_time > timeout:
                        return {"success": False, "message": "操作超时", "timeout": True}

                    confirm_button = desktop.ButtonControl(Name=button_name)
                    if confirm_button.Exists(0, 0):
                        confirm_button.Click()
                        self.logger.info(f"✅ 点击了'{button_name}'按钮")
                        confirm_clicked = True
                        time.sleep(2)
                        break
                except:
                    continue

            if confirm_clicked:
                self.logger.info(f"🎉 混合模式成功发送好友申请给: {search_keyword}")
                return {"success": True, "message": f"混合模式成功发送好友申请给: {search_keyword}"}
            else:
                return {"success": False, "message": "未找到确定按钮"}

        except Exception as e:
            self.logger.error(f"混合模式添加好友失败: {e}")
            return {"success": False, "message": f"混合模式添加好友失败: {str(e)}"}

    def add_friend_manual_guide(
        self, search_keyword: str, verify_msg: str = ""
    ) -> bool:
        """手动指导添加好友"""
        try:
            self.logger.info("=" * 50)
            self.logger.info("手动添加好友指导模式")
            self.logger.info("=" * 50)

            # 搜索用户
            if not self.search_contact(search_keyword):
                return False

            self.logger.info("✅ 搜索完成，请按照以下步骤手动操作：")
            self.logger.info("1. 在搜索结果中找到正确的用户")
            self.logger.info("2. 点击用户头像或名称")
            self.logger.info("3. 在用户详情页面点击'添加到通讯录'按钮")
            self.logger.info("4. 输入验证消息（如果需要）")
            if verify_msg:
                self.logger.info(f"   建议验证消息: {verify_msg}")
            self.logger.info("5. 点击'发送'按钮")
            self.logger.info("=" * 50)

            input("请完成上述操作后按回车键继续...")
            self.logger.info("✅ 手动添加好友操作完成")
            return True

        except Exception as e:
            self.logger.error(f"手动指导添加好友失败: {e}")
            return False

    def take_screenshot(self, wechat_id: str, action: str) -> str:
        """截图保存，格式：当前日期_微信号_操作"""
        try:
            # 导入PIL用于截图
            try:
                from PIL import ImageGrab
            except ImportError:
                self.logger.warning("PIL库未安装，无法截图。请运行: pip install Pillow")
                return ""

            # 创建screenshots目录
            screenshot_dir = "screenshots"
            if not os.path.exists(screenshot_dir):
                os.makedirs(screenshot_dir)

            # 生成文件名：当前日期_微信号_操作
            current_date = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{current_date}_{wechat_id}_{action}.png"
            filepath = os.path.join(screenshot_dir, filename)

            # 截取整个屏幕
            screenshot = ImageGrab.grab()
            screenshot.save(filepath)

            self.logger.info(f"📸 截图已保存: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return ""

    def get_contact_list(self) -> List[str]:
        """获取联系人列表"""
        try:
            self.activate_wechat()

            # 点击通讯录
            contacts_button = self.wechat_window.ButtonControl(Name="通讯录")
            if contacts_button.Exists(0, 0):
                contacts_button.Click()
                time.sleep(1)

                # 获取联系人列表
                contact_list = []
                list_items = self.wechat_window.ListControl().GetChildren()

                for item in list_items:
                    if hasattr(item, "Name") and item.Name:
                        contact_list.append(item.Name)

                self.logger.info(f"获取到 {len(contact_list)} 个联系人")
                return contact_list

            return []

        except Exception as e:
            self.logger.error(f"获取联系人列表失败: {e}")
            return []

    def batch_add_friends(self, friends_data: List[Dict]) -> Dict:
        """批量添加好友"""
        results = {"success": 0, "failed": 0, "total": len(friends_data)}

        for i, friend in enumerate(friends_data, 1):
            search_key = friend.get("search_key", "")
            verify_msg = friend.get("verify_msg", "")

            self.logger.info(f"[{i}/{len(friends_data)}] 添加好友: {search_key}")

            if self.add_friend(search_key, verify_msg):
                results["success"] += 1
            else:
                results["failed"] += 1

            # 等待一段时间，避免操作过快
            time.sleep(3)

        return results


def main():
    """主函数示例"""
    wechat = WeChatUIAutomation()

    if not wechat.find_wechat_window():
        print("请先打开微信客户端")
        return

    print("微信UI自动化工具")
    print("1. 发送消息")
    print("2. 添加好友")
    print("3. 获取联系人列表")

    choice = input("请选择功能 (1-3): ")

    if choice == "1":
        contact = input("请输入联系人名称: ")
        message = input("请输入消息内容: ")
        wechat.send_message(contact, message)

    elif choice == "2":
        keyword = input("请输入搜索关键词: ")
        verify_msg = input("请输入验证消息: ")
        wechat.add_friend(keyword, verify_msg)

    elif choice == "3":
        contacts = wechat.get_contact_list()
        print(f"联系人列表: {contacts[:10]}...")  # 只显示前10个


if __name__ == "__main__":
    main()
