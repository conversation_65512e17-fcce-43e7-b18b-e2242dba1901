#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于uiautomation的微信自动化替代方案
完全免费，功能强大
"""

import time
import logging
from typing import List, Dict, Optional

try:
    import uiautomation as auto
    UIAUTOMATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ uiautomation导入失败: {e}")
    print("请安装uiautomation: pip install uiautomation")
    UIAUTOMATION_AVAILABLE = False
    auto = None

class WeChatUIAutomation:
    """基于uiautomation的微信自动化类"""
    
    def __init__(self):
        """初始化"""
        self.wechat_window = None
        self.uiautomation_available = UIAUTOMATION_AVAILABLE
        self.setup_logging()

        if not self.uiautomation_available:
            self.logger.error("uiautomation不可用，请安装: pip install uiautomation")
            raise ImportError("uiautomation not available")
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler('wechat_ui_automation.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def find_wechat_window(self) -> bool:
        """查找微信窗口"""
        if not self.uiautomation_available:
            self.logger.error("uiautomation不可用")
            return False

        try:
            # 查找微信主窗口
            self.wechat_window = auto.WindowControl(searchDepth=1, Name="微信", ClassName="WeChatMainWndForPC")
            if self.wechat_window.Exists(0, 0):
                self.logger.info("找到微信窗口")
                return True

            # 尝试其他可能的窗口名称
            self.wechat_window = auto.WindowControl(searchDepth=1, ClassName="WeChatMainWndForPC")
            if self.wechat_window.Exists(0, 0):
                self.logger.info("找到微信窗口（通过类名）")
                return True

            self.logger.error("未找到微信窗口，请确保微信已打开")
            return False

        except Exception as e:
            self.logger.error(f"查找微信窗口失败: {e}")
            return False
    
    def activate_wechat(self):
        """激活微信窗口"""
        if self.wechat_window:
            try:
                self.wechat_window.SetActive()
                time.sleep(0.5)
                self.logger.info("微信窗口已激活")
            except Exception as e:
                self.logger.warning(f"激活微信窗口失败: {e}")
                # 尝试点击窗口来激活
                try:
                    rect = self.wechat_window.BoundingRectangle
                    center_x = rect.left + rect.width() // 2
                    center_y = rect.top + rect.height() // 2
                    self.wechat_window.Click(center_x, center_y)
                    time.sleep(0.5)
                    self.logger.info("通过点击激活微信窗口")
                except Exception as e2:
                    self.logger.error(f"点击激活也失败: {e2}")
    
    def search_contact(self, contact_name: str) -> bool:
        """搜索联系人"""
        try:
            self.activate_wechat()

            # 查找搜索框的多种方式
            search_box = None
            search_methods = [
                lambda: self.wechat_window.EditControl(Name="搜索"),
                lambda: self.wechat_window.EditControl(AutomationId="SearchEditBox"),
                lambda: self.wechat_window.EditControl(ClassName="EditControl"),
                # 通过遍历所有编辑框找到搜索框
                lambda: next((edit for edit in self.wechat_window.GetChildren()
                            if edit.ControlType == auto.ControlType.EditControl), None)
            ]

            for method in search_methods:
                try:
                    search_box = method()
                    if search_box and search_box.Exists(0, 0):
                        self.logger.info(f"找到搜索框: {search_box.Name}")
                        break
                except Exception as e:
                    continue

            if search_box and search_box.Exists(0, 0):
                try:
                    # 点击搜索框
                    search_box.Click()
                    time.sleep(0.5)

                    # 清空搜索框并输入联系人名称
                    search_box.SendKeys("{Ctrl}a")
                    time.sleep(0.2)
                    search_box.SendKeys(contact_name)
                    time.sleep(1)

                    self.logger.info(f"搜索联系人: {contact_name}")
                    return True
                except Exception as e:
                    self.logger.error(f"操作搜索框失败: {e}")
                    # 尝试使用全局快捷键
                    try:
                        auto.SendKeys("{Ctrl}f")  # 微信搜索快捷键
                        time.sleep(0.5)
                        auto.SendKeys(contact_name)
                        time.sleep(1)
                        self.logger.info(f"使用快捷键搜索联系人: {contact_name}")
                        return True
                    except Exception as e2:
                        self.logger.error(f"快捷键搜索也失败: {e2}")
                        return False
            else:
                self.logger.error("未找到搜索框")
                # 尝试使用快捷键作为备选方案
                try:
                    auto.SendKeys("{Ctrl}f")
                    time.sleep(0.5)
                    auto.SendKeys(contact_name)
                    time.sleep(1)
                    self.logger.info(f"使用快捷键搜索联系人: {contact_name}")
                    return True
                except Exception as e:
                    self.logger.error(f"快捷键搜索失败: {e}")
                    return False

        except Exception as e:
            self.logger.error(f"搜索联系人失败: {e}")
            return False
    
    def send_message(self, contact_name: str, message: str) -> bool:
        """发送消息"""
        try:
            # 搜索并打开联系人
            if not self.search_contact(contact_name):
                return False

            # 按回车打开聊天窗口
            auto.SendKeys("{Enter}")
            time.sleep(2)  # 等待聊天窗口打开

            # 查找消息输入框的多种方式
            msg_edit = None
            input_methods = [
                # 尝试通过不同的属性查找输入框
                lambda: self.wechat_window.EditControl(ClassName="EditControl"),
                lambda: self.wechat_window.EditControl(Name=""),
                # 查找所有编辑框，通常消息输入框是最后一个或最大的
                lambda: self._find_message_input_box()
            ]

            for method in input_methods:
                try:
                    msg_edit = method()
                    if msg_edit and msg_edit.Exists(0, 0):
                        self.logger.info("找到消息输入框")
                        break
                except Exception:
                    continue

            if msg_edit and msg_edit.Exists(0, 0):
                try:
                    # 点击消息输入框
                    msg_edit.Click()
                    time.sleep(0.5)

                    # 输入消息
                    msg_edit.SendKeys(message)
                    time.sleep(0.5)

                    # 发送消息
                    auto.SendKeys("{Enter}")
                    self.logger.info(f"向 {contact_name} 发送消息: {message}")
                    return True
                except Exception as e:
                    self.logger.error(f"操作消息输入框失败: {e}")
                    # 尝试直接输入
                    try:
                        auto.SendKeys(message)
                        time.sleep(0.5)
                        auto.SendKeys("{Enter}")
                        self.logger.info(f"使用直接输入向 {contact_name} 发送消息: {message}")
                        return True
                    except Exception as e2:
                        self.logger.error(f"直接输入也失败: {e2}")
                        return False
            else:
                self.logger.error("未找到消息输入框，尝试直接输入")
                try:
                    # 直接输入消息作为备选方案
                    auto.SendKeys(message)
                    time.sleep(0.5)
                    auto.SendKeys("{Enter}")
                    self.logger.info(f"使用直接输入向 {contact_name} 发送消息: {message}")
                    return True
                except Exception as e:
                    self.logger.error(f"直接输入失败: {e}")
                    return False

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    def _find_message_input_box(self):
        """查找消息输入框的辅助方法"""
        try:
            # 获取所有编辑框
            edit_controls = []
            for control in self.wechat_window.GetChildren():
                if control.ControlType == auto.ControlType.EditControl:
                    edit_controls.append(control)

            if edit_controls:
                # 通常消息输入框是最后一个或者位置最靠下的
                return edit_controls[-1]

            return None
        except Exception:
            return None
    
    def add_friend(self, search_keyword: str, verify_msg: str = "") -> bool:
        """添加好友"""
        try:
            self.activate_wechat()
            
            # 搜索用户
            if not self.search_contact(search_keyword):
                return False
            
            time.sleep(1)
            
            # 查找"添加到通讯录"按钮
            add_buttons = self.wechat_window.ButtonControl(Name="添加到通讯录")
            if add_buttons.Exists(0, 0):
                add_buttons.Click()
                time.sleep(1)
                
                # 如果有验证消息输入框，输入验证消息
                if verify_msg:
                    verify_edit = self.wechat_window.EditControl()
                    if verify_edit.Exists(0, 0):
                        verify_edit.SendKeys(verify_msg)
                        time.sleep(0.5)
                
                # 点击发送按钮
                send_button = self.wechat_window.ButtonControl(Name="发送")
                if send_button.Exists(0, 0):
                    send_button.Click()
                    self.logger.info(f"已发送好友申请给: {search_keyword}")
                    return True
                    
            self.logger.warning(f"未找到添加好友按钮，可能已经是好友: {search_keyword}")
            return False
            
        except Exception as e:
            self.logger.error(f"添加好友失败: {e}")
            return False
    
    def get_contact_list(self) -> List[str]:
        """获取联系人列表"""
        try:
            self.activate_wechat()
            
            # 点击通讯录
            contacts_button = self.wechat_window.ButtonControl(Name="通讯录")
            if contacts_button.Exists(0, 0):
                contacts_button.Click()
                time.sleep(1)
                
                # 获取联系人列表
                contact_list = []
                list_items = self.wechat_window.ListControl().GetChildren()
                
                for item in list_items:
                    if hasattr(item, 'Name') and item.Name:
                        contact_list.append(item.Name)
                
                self.logger.info(f"获取到 {len(contact_list)} 个联系人")
                return contact_list
            
            return []
            
        except Exception as e:
            self.logger.error(f"获取联系人列表失败: {e}")
            return []
    
    def batch_add_friends(self, friends_data: List[Dict]) -> Dict:
        """批量添加好友"""
        results = {"success": 0, "failed": 0, "total": len(friends_data)}
        
        for i, friend in enumerate(friends_data, 1):
            search_key = friend.get("search_key", "")
            verify_msg = friend.get("verify_msg", "")
            
            self.logger.info(f"[{i}/{len(friends_data)}] 添加好友: {search_key}")
            
            if self.add_friend(search_key, verify_msg):
                results["success"] += 1
            else:
                results["failed"] += 1
            
            # 等待一段时间，避免操作过快
            time.sleep(3)
        
        return results

def main():
    """主函数示例"""
    wechat = WeChatUIAutomation()
    
    if not wechat.find_wechat_window():
        print("请先打开微信客户端")
        return
    
    print("微信UI自动化工具")
    print("1. 发送消息")
    print("2. 添加好友")
    print("3. 获取联系人列表")
    
    choice = input("请选择功能 (1-3): ")
    
    if choice == "1":
        contact = input("请输入联系人名称: ")
        message = input("请输入消息内容: ")
        wechat.send_message(contact, message)
        
    elif choice == "2":
        keyword = input("请输入搜索关键词: ")
        verify_msg = input("请输入验证消息: ")
        wechat.add_friend(keyword, verify_msg)
        
    elif choice == "3":
        contacts = wechat.get_contact_list()
        print(f"联系人列表: {contacts[:10]}...")  # 只显示前10个

if __name__ == "__main__":
    main()
