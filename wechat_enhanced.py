#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版微信自动化工具
集成了wxauto和uiautomation的优势
"""

import os
import sys
import time
import json
import random
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import threading

# 导入所需库
try:
    import wxauto
    from wxauto import WeChat
    WXAUTO_AVAILABLE = True
except ImportError:
    WXAUTO_AVAILABLE = False
    print("⚠️ wxauto未安装，部分功能不可用")

try:
    import uiautomation as auto
    UIAUTOMATION_AVAILABLE = True
except ImportError:
    UIAUTOMATION_AVAILABLE = False
    print("⚠️ uiautomation未安装，部分功能不可用")

class EnhancedWeChatLogger:
    """增强版微信日志记录器"""
    
    def __init__(self, log_dir: str = "wxauto_logs"):
        """初始化日志记录器"""
        self.log_dir = log_dir
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志记录器"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        today = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(self.log_dir, f"enhanced_{today}.log")
        
        log_format = "%(asctime)s [%(name)s] [%(levelname)s] [%(filename)s:%(lineno)d]  %(message)s"
        date_format = "%Y-%m-%d %H:%M:%S"
        
        logging.basicConfig(
            level=logging.DEBUG,
            format=log_format,
            datefmt=date_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("增强版微信自动化工具日志系统初始化完成")

class EnhancedWeChatAutomation:
    """增强版微信自动化主控制类"""
    
    def __init__(self):
        """初始化增强版微信自动化工具"""
        # 初始化日志
        self.logger_manager = EnhancedWeChatLogger()
        self.logger = self.logger_manager.logger
        
        # 检测可用的库
        self.wxauto_available = WXAUTO_AVAILABLE
        self.uiautomation_available = UIAUTOMATION_AVAILABLE
        
        # 微信客户端实例
        self.wx_wxauto = None
        self.wx_uiautomation = None
        
        # 配置文件
        self.friends_data_file = "friends_data.json"
        self.reply_rules_file = "reply_rules.json"
        
        self.logger.info(f"库可用性检测:")
        self.logger.info(f"  wxauto: {'✅' if self.wxauto_available else '❌'}")
        self.logger.info(f"  uiautomation: {'✅' if self.uiautomation_available else '❌'}")
    
    def init_wechat(self) -> bool:
        """初始化微信客户端"""
        success = False
        
        # 尝试初始化wxauto
        if self.wxauto_available:
            try:
                self.wx_wxauto = WeChat()
                self.logger.info("✅ wxauto客户端初始化成功")
                success = True
            except Exception as e:
                self.logger.error(f"❌ wxauto客户端初始化失败: {e}")
        
        # 尝试初始化uiautomation
        if self.uiautomation_available:
            try:
                wechat_window = auto.WindowControl(searchDepth=1, ClassName="WeChatMainWndForPC")
                if wechat_window.Exists(0, 0):
                    self.wx_uiautomation = wechat_window
                    self.logger.info("✅ uiautomation客户端初始化成功")
                    success = True
                else:
                    self.logger.error("❌ uiautomation未找到微信窗口")
            except Exception as e:
                self.logger.error(f"❌ uiautomation客户端初始化失败: {e}")
        
        if not success:
            self.logger.error("❌ 所有微信客户端初始化都失败")
            return False
        
        return True
    
    def send_message_wxauto(self, contact_name: str, message: str) -> bool:
        """使用wxauto发送消息"""
        try:
            if not self.wx_wxauto:
                return False
            
            self.wx_wxauto.ChatWith(contact_name)
            time.sleep(1)
            self.wx_wxauto.SendMsg(message)
            self.logger.info(f"✅ [wxauto] 向 {contact_name} 发送消息: {message}")
            return True
        except Exception as e:
            self.logger.error(f"❌ [wxauto] 发送消息失败: {e}")
            return False
    
    def send_message_uiautomation(self, contact_name: str, message: str) -> bool:
        """使用uiautomation发送消息"""
        try:
            if not self.wx_uiautomation:
                return False
            
            # 激活微信窗口
            self.wx_uiautomation.SetActive()
            time.sleep(0.5)
            
            # 使用Ctrl+F搜索
            auto.SendKeys("{Ctrl}f")
            time.sleep(0.5)
            auto.SendKeys(contact_name)
            time.sleep(1)
            auto.SendKeys("{Enter}")
            time.sleep(1)
            
            # 发送消息
            auto.SendKeys(message)
            time.sleep(0.5)
            auto.SendKeys("{Enter}")
            
            self.logger.info(f"✅ [uiautomation] 向 {contact_name} 发送消息: {message}")
            return True
        except Exception as e:
            self.logger.error(f"❌ [uiautomation] 发送消息失败: {e}")
            return False
    
    def send_message(self, contact_name: str, message: str) -> bool:
        """智能发送消息（优先使用最佳方法）"""
        # 优先使用wxauto，失败则使用uiautomation
        if self.wxauto_available and self.send_message_wxauto(contact_name, message):
            return True
        elif self.uiautomation_available and self.send_message_uiautomation(contact_name, message):
            return True
        else:
            self.logger.error(f"❌ 所有方法都无法发送消息给 {contact_name}")
            return False
    
    def add_friend_uiautomation(self, search_keyword: str, verify_msg: str = "") -> bool:
        """使用uiautomation添加好友"""
        try:
            if not self.wx_uiautomation:
                return False
            
            # 激活微信窗口
            self.wx_uiautomation.SetActive()
            time.sleep(0.5)
            
            # 搜索用户
            auto.SendKeys("{Ctrl}f")
            time.sleep(0.5)
            auto.SendKeys(search_keyword)
            time.sleep(2)
            
            # 查找并点击添加按钮
            add_button = self.wx_uiautomation.ButtonControl(Name="添加到通讯录")
            if add_button.Exists(0, 0):
                add_button.Click()
                time.sleep(1)
                
                # 输入验证消息
                if verify_msg:
                    auto.SendKeys(verify_msg)
                    time.sleep(0.5)
                
                # 点击发送
                send_button = self.wx_uiautomation.ButtonControl(Name="发送")
                if send_button.Exists(0, 0):
                    send_button.Click()
                    self.logger.info(f"✅ [uiautomation] 已发送好友申请给: {search_keyword}")
                    return True
            
            self.logger.warning(f"⚠️ [uiautomation] 未找到添加按钮: {search_keyword}")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ [uiautomation] 添加好友失败: {e}")
            return False
    
    def load_friends_data(self) -> List[Dict]:
        """加载好友数据"""
        try:
            if os.path.exists(self.friends_data_file):
                with open(self.friends_data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.logger.info(f"成功加载 {len(data)} 个好友数据")
                    return data
            else:
                self.logger.warning(f"文件 {self.friends_data_file} 不存在")
                return []
        except Exception as e:
            self.logger.error(f"加载好友数据失败: {e}")
            return []
    
    def batch_add_friends(self) -> Dict:
        """批量添加好友"""
        friends_data = self.load_friends_data()
        if not friends_data:
            self.logger.error("没有好友数据")
            return {"total": 0, "success": 0, "failed": 0}
        
        total_count = len(friends_data)
        success_count = 0
        failed_count = 0
        
        self.logger.info(f"开始批量添加 {total_count} 个好友...")
        
        for i, friend_info in enumerate(friends_data, 1):
            search_key = friend_info.get("search_key", "")
            verify_msg = friend_info.get("verify_msg", "")
            
            self.logger.info(f"[{i}/{total_count}] 添加好友: {search_key}")
            
            # 使用uiautomation添加好友
            if self.uiautomation_available and self.add_friend_uiautomation(search_key, verify_msg):
                success_count += 1
            else:
                failed_count += 1
                self.logger.info(f"需要手动添加: {search_key}")
                self.logger.info(f"  验证消息: {verify_msg}")
            
            # 等待时间
            wait_time = random.uniform(5, 15)
            self.logger.info(f"等待 {wait_time:.1f} 秒...")
            time.sleep(wait_time)
        
        return {
            "total": total_count,
            "success": success_count,
            "failed": failed_count
        }

def main():
    """主函数"""
    print("="*60)
    print("增强版微信自动化工具")
    print("集成wxauto + uiautomation")
    print("="*60)
    
    try:
        # 创建实例
        wechat = EnhancedWeChatAutomation()
        
        # 初始化微信
        if not wechat.init_wechat():
            print("❌ 微信初始化失败，请确保微信已打开")
            return
        
        while True:
            print("\n" + "="*40)
            print("功能菜单")
            print("="*40)
            print("1. 发送消息")
            print("2. 批量添加好友")
            print("3. 退出程序")
            print("="*40)
            
            choice = input("请选择功能 (1-3): ").strip()
            
            if choice == "1":
                contact = input("请输入联系人名称: ").strip()
                message = input("请输入消息内容: ").strip()
                if contact and message:
                    wechat.send_message(contact, message)
                else:
                    print("❌ 联系人和消息不能为空")
            
            elif choice == "2":
                result = wechat.batch_add_friends()
                print(f"\n批量添加好友完成:")
                print(f"  总数: {result['total']}")
                print(f"  成功: {result['success']}")
                print(f"  失败: {result['failed']}")
            
            elif choice == "3":
                print("程序退出")
                break
            
            else:
                print("❌ 无效选择")
    
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
