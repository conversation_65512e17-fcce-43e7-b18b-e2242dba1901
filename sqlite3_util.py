# 核心库导入
import os
import sqlite3

# 类型提示（仅用于开发时的类型检查）
try:
    from typing import List, Dict, Optional, Union, Tuple
except ImportError:
    # 兼容旧版本Python，定义空类型
    class _DummyType:
        def __getitem__(self, item):
            return self
    List = Dict = Optional = Union = Tuple = _DummyType()
# 创建数据库
def create_db(db_path='system.db'):
    try:
        if not os.path.exists(db_path):
            # 创建空数据库文件
            open(db_path, 'w').close()
            print(f"已创建空数据库: {os.path.abspath(db_path)}")
            return True
        else:
            print(f"数据库已存在: {os.path.abspath(db_path)}")
            return False
    except Exception as e:
        print(f"创建数据库失败: {str(e)}")
        return False

from typing import List, Optional

def create_table(
    db_path: str = 'system.db',
    table_name: str = None,
    columns: List[dict] = None,
    sql_statement: str = None
) -> bool:
    """
    在SQLite数据库中创建表
    
    参数:
        db_path: 数据库文件路径 (默认'system.db')
        table_name: 要创建的表名
        columns: 列定义列表 [{'name': 'id', 'type': 'INTEGER', 'constraints': 'PRIMARY KEY'}, ...]
        sql_statement: 直接提供完整的CREATE TABLE语句
        
    返回:
        bool: 是否创建成功
        
    注意:
        必须提供 columns 或 sql_statement 其中之一
    """
    if not (columns or sql_statement):
        print("错误：必须提供列定义或完整SQL语句")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        if sql_statement:
            # 使用直接提供的SQL语句
            cursor.execute(sql_statement)
        else:
            # 动态生成SQL语句
            columns_def = []
            for col in columns:
                col_def = f"{col['name']} {col['type']}"
                if 'constraints' in col:
                    col_def += f" {col['constraints']}"
                columns_def.append(col_def)
            
            create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns_def)})"
            cursor.execute(create_sql)
        
        conn.commit()
        return True
        
    except sqlite3.Error as e:
        print(f"创建表失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def table_exists(db_path: str, table_name: str) -> bool:
    """
    检查表是否已存在
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        return cursor.fetchone() is not None
    finally:
        conn.close()

def init_users_table(db_path: str = 'system.db') -> bool:
    """
    初始化 users 表
    包含字段：id, file_name, username, intro, unique_id, cmm_id, code, create_time
    """
    users_table_sql = """
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        username TEXT NOT NULL,
        intro TEXT,
        unique_id TEXT,
        cmm_id TEXT,
        code TEXT,
        create_time TEXT NOT NULL
    )
    """

    try:
        if not table_exists(db_path, 'users'):
            result = create_table(db_path=db_path, sql_statement=users_table_sql)
            if result:
                print("✅ users 表创建成功")
                return True
            else:
                print("❌ users 表创建失败")
                return False
        else:
            print("ℹ️  users 表已存在，跳过创建")
            return True
    except Exception as e:
        print(f"❌ 初始化 users 表失败: {str(e)}")
        return False

def init_tokens_table(db_path: str = 'system.db') -> bool:
    """
    初始化 tokens 表
    包含字段：id, token, create_time
    """
    tokens_table_sql = """
    CREATE TABLE IF NOT EXISTS tokens (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        token TEXT NOT NULL UNIQUE,
        create_time TEXT NOT NULL
    )
    """

    try:
        if not table_exists(db_path, 'tokens'):
            result = create_table(db_path=db_path, sql_statement=tokens_table_sql)
            if result:
                print("✅ tokens 表创建成功")
                return True
            else:
                print("❌ tokens 表创建失败")
                return False
        else:
            print("ℹ️  tokens 表已存在，跳过创建")
            return True
    except Exception as e:
        print(f"❌ 初始化 tokens 表失败: {str(e)}")
        return False

def init_wechat_phrases_table(db_path: str = 'system.db') -> bool:
    """
    初始化微信常用语表
    """
    try:
        sql = """
        CREATE TABLE IF NOT EXISTS wechat_phrases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            content TEXT NOT NULL,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            status INTEGER DEFAULT 1
        )
        """

        result = create_table(db_path=db_path, sql_statement=sql)

        if result:
            # 插入一些默认的常用语
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 检查是否已有数据
            cursor.execute("SELECT COUNT(*) FROM wechat_phrases")
            count = cursor.fetchone()[0]

            if count == 0:
                default_phrases = [
                    "你好，希望能加个好友！",
                    "您好，我是通过朋友介绍认识您的，希望能成为朋友。",
                    "Hi，看到您的朋友圈很有趣，想和您交个朋友。",
                    "您好，我们有共同的朋友，希望能认识一下。",
                    "你好，想和您交流学习一下，请多指教！"
                ]

                for phrase in default_phrases:
                    cursor.execute(
                        "INSERT INTO wechat_phrases (content) VALUES (?)",
                        (phrase,)
                    )

                conn.commit()
                print("✅ 已插入默认常用语")

            conn.close()
            print("ℹ️  wechat_phrases 表初始化成功")
            return True
        else:
            print("ℹ️  wechat_phrases 表已存在，跳过创建")
            return True
    except Exception as e:
        print(f"❌ 初始化 wechat_phrases 表失败: {str(e)}")
        return False

def init_adduser_logs_table(db_path: str = 'system.db') -> bool:
    """
    初始化添加用户日志表
    """
    try:
        if table_exists(db_path, 'adduser_logs'):
            print("ℹ️  adduser_logs 表已存在，跳过创建")
            return True

        sql_statement = '''
            CREATE TABLE IF NOT EXISTS adduser_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                wechat_id TEXT NOT NULL,
                add_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status INTEGER DEFAULT 0,
                img_path TEXT,
                file_name TEXT,
                verify_msg TEXT,
                error_msg TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        '''

        result = create_table(
            db_path=db_path,
            sql_statement=sql_statement
        )

        if result:
            print("ℹ️  adduser_logs 表初始化成功")
            return True
        else:
            print("❌ adduser_logs 表创建失败")
            return False

    except Exception as e:
        print(f"❌ 初始化 adduser_logs 表失败: {str(e)}")
        return False

def init_database(db_path: str = 'system.db') -> bool:
    """
    初始化数据库和所有表
    """
    print("🚀 开始初始化数据库...")

    # 1. 创建数据库文件
    create_db(db_path)

    # 2. 创建所有表
    tables_success = []

    # 创建 users 表
    users_result = init_users_table(db_path)
    tables_success.append(('users', users_result))

    # 创建 tokens 表
    tokens_result = init_tokens_table(db_path)
    tables_success.append(('tokens', tokens_result))

    # 创建 wechat_phrases 表
    phrases_result = init_wechat_phrases_table(db_path)
    tables_success.append(('wechat_phrases', phrases_result))

    # 创建 adduser_logs 表
    adduser_logs_result = init_adduser_logs_table(db_path)
    tables_success.append(('adduser_logs', adduser_logs_result))

    # 统计结果
    success_count = sum(1 for _, success in tables_success if success)
    total_count = len(tables_success)

    print(f"\n📊 数据库初始化完成:")
    print(f"   数据库文件: {os.path.abspath(db_path)}")
    print(f"   表创建结果: {success_count}/{total_count} 成功")

    for table_name, success in tables_success:
        status = "✅" if success else "❌"
        print(f"   {status} {table_name}")

    return success_count == total_count

def query_wechat_phrases(db_path: str = 'system.db', status: int = None) -> List[Dict]:
    """
    查询微信常用语
    """
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if status is not None:
            cursor.execute(
                "SELECT * FROM wechat_phrases WHERE status = ? ORDER BY create_time DESC",
                (status,)
            )
        else:
            cursor.execute("SELECT * FROM wechat_phrases ORDER BY create_time DESC")

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]
    except Exception as e:
        print(f"查询常用语失败: {str(e)}")
        return []

def add_wechat_phrase(db_path: str = 'system.db', content: str = '') -> bool:
    """
    添加微信常用语
    """
    try:
        if not content.strip():
            return False

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute(
            "INSERT INTO wechat_phrases (content) VALUES (?)",
            (content.strip(),)
        )

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"添加常用语失败: {str(e)}")
        return False

def update_wechat_phrase(db_path: str = 'system.db', phrase_id: int = None, content: str = '', status: int = None) -> bool:
    """
    更新微信常用语
    """
    try:
        if phrase_id is None:
            return False

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        if content and status is not None:
            cursor.execute(
                "UPDATE wechat_phrases SET content = ?, status = ? WHERE id = ?",
                (content.strip(), status, phrase_id)
            )
        elif content:
            cursor.execute(
                "UPDATE wechat_phrases SET content = ? WHERE id = ?",
                (content.strip(), phrase_id)
            )
        elif status is not None:
            cursor.execute(
                "UPDATE wechat_phrases SET status = ? WHERE id = ?",
                (status, phrase_id)
            )
        else:
            return False

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"更新常用语失败: {str(e)}")
        return False

def delete_wechat_phrase(db_path: str = 'system.db', phrase_id: int = None) -> bool:
    """
    删除微信常用语
    """
    try:
        if phrase_id is None:
            return False

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute("DELETE FROM wechat_phrases WHERE id = ?", (phrase_id,))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"删除常用语失败: {str(e)}")
        return False

def query_users(db_path: str = 'system.db', limit: int = None) -> List[Dict]:
    """
    查询 users 表数据
    """
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if limit:
            cursor.execute("SELECT * FROM users ORDER BY create_time DESC LIMIT ?", (limit,))
        else:
            cursor.execute("SELECT * FROM users ORDER BY create_time DESC")

        results = [dict(row) for row in cursor.fetchall()]
        return results
    except sqlite3.Error as e:
        print(f"查询 users 表失败: {str(e)}")
        return []
    finally:
        if 'conn' in locals():
            conn.close()

def get_users_count(db_path: str = 'system.db') -> int:
    """
    获取 users 表总记录数
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]
        return count
    except sqlite3.Error as e:
        print(f"获取 users 表记录数失败: {str(e)}")
        return 0
    finally:
        if 'conn' in locals():
            conn.close()

def verify_insert_result(db_path: str = 'system.db') -> Dict:
    """
    验证插入结果，检查数据库状态
    """
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            return {
                "success": False,
                "message": f"数据库文件不存在: {db_path}",
                "file_exists": False,
                "table_exists": False,
                "record_count": 0
            }

        # 检查表是否存在
        table_exists_flag = table_exists(db_path, 'users')

        # 获取记录数
        record_count = get_users_count(db_path) if table_exists_flag else 0

        # 获取最新几条记录
        latest_records = query_users(db_path, 5) if table_exists_flag else []

        return {
            "success": True,
            "message": f"数据库验证完成",
            "file_exists": True,
            "table_exists": table_exists_flag,
            "record_count": record_count,
            "latest_records": latest_records,
            "db_path": os.path.abspath(db_path)
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"验证失败: {str(e)}",
            "error": str(e)
        }

def query_table(
    db_path: str,
    table_name: str,
    columns: List[str] = ["*"],
    where: Optional[str] = None,
    params: Optional[Union[tuple, dict]] = None,
    order_by: Optional[str] = None,
    limit: Optional[int] = None
) -> List[Dict]:
    """
    通用SQLite表查询方法
    
    参数:
        db_path: 数据库文件路径
        table_name: 要查询的表名
        columns: 要查询的列名列表，默认查询所有列
        where: WHERE条件语句（不包含WHERE关键字）
        params: 查询参数（元组或字典）
        order_by: 排序条件（不包含ORDER BY关键字）
        limit: 返回结果数量限制
        
    返回:
        包含查询结果的字典列表，每个字典代表一行数据
    """
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使返回结果为字典形式
        cursor = conn.cursor()
        
        # 构建SQL语句
        query = f"SELECT {', '.join(columns)} FROM {table_name}"
        
        if where:
            query += f" WHERE {where}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
            
        if limit:
            query += f" LIMIT {limit}"
        
        # 执行查询
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        # 获取结果并转换为字典列表
        results = [dict(row) for row in cursor.fetchall()]
        return results
        
    except sqlite3.Error as e:
        print(f"查询表 {table_name} 失败: {str(e)}")
        return []
    finally:
        if 'conn' in locals():
            conn.close()


def batch_insert(
    db_path: str,
    table_name: str,
    field_names: List[str],
    data: List[Tuple],
    batch_size: int = 100
) -> int:
    """
    指定表名的批量数据插入方法
    
    参数:
        db_path: 数据库文件路径
        table_name: 要插入数据的表名
        field_names: 字段名称列表
        data: 要插入的数据列表（每个元素是字段值的元组）
        batch_size: 每批插入的数据量（默认100）
        
    返回:
        成功插入的行数
    """
    inserted_rows = 0
    conn = None
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 构建INSERT语句
        placeholders = ', '.join(['?'] * len(field_names))
        sql = f"""
        INSERT INTO {table_name} 
        ({', '.join(field_names)})
        VALUES ({placeholders})
        """
        
        # 分批执行插入
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            cursor.executemany(sql, batch)
            inserted_rows += len(batch)
            conn.commit()  # 每批提交一次
            
        return inserted_rows
        
    except sqlite3.Error as e:
        print(f"批量插入到表 {table_name} 失败: {str(e)}")
        return 0
    finally:
        if conn:
            conn.close()




def add_user_log(db_path: str = 'system.db', wechat_id: str = '', user_id: int = None,
                 status: int = 0, img_path: str = '', file_name: str = '',
                 verify_msg: str = '', error_msg: str = '') -> bool:
    """
    添加用户操作日志
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO adduser_logs (user_id, wechat_id, status, img_path, file_name, verify_msg, error_msg)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, wechat_id, status, img_path, file_name, verify_msg, error_msg))

        conn.commit()
        conn.close()
        return True

    except Exception as e:
        print(f"添加用户日志失败: {str(e)}")
        return False

def query_user_logs(db_path: str = 'system.db', limit: int = 100) -> List[Dict]:
    """
    查询用户操作日志
    """
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT l.*, u.unique_id, u.nickname, u.signature
            FROM adduser_logs l
            LEFT JOIN users u ON l.user_id = u.id
            ORDER BY l.add_time DESC
            LIMIT ?
        ''', (limit,))

        rows = cursor.fetchall()
        conn.close()

        return [dict(row) for row in rows]

    except Exception as e:
        print(f"查询用户日志失败: {str(e)}")
        return []

if __name__ == "__main__":
    print('载入数据库')
    # 插入数据


