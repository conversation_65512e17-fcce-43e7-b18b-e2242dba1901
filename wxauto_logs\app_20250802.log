2025-08-02 22:14:01 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:310]  ==================================================
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:311]  微信自动加好友工具启动
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:312]  ==================================================
2025-08-02 22:14:01 [__main__] [WARNING] [wx.py:221]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:319]  ⚠️ 请确保:
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:320]  1. 微信PC版已登录且窗口打开
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:321]  2. 网络连接正常
2025-08-02 22:14:01 [__main__] [INFO] [wx.py:322]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:14:01 [__main__] [ERROR] [wx.py:64]  检查微信状态失败: 'WeChat' object has no attribute 'IsOnline'
2025-08-02 22:14:01 [__main__] [ERROR] [wx.py:326]  微信状态检查失败，程序退出
2025-08-02 22:14:35 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:310]  ==================================================
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:311]  微信自动加好友工具启动
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:312]  ==================================================
2025-08-02 22:14:35 [__main__] [WARNING] [wx.py:221]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:319]  ⚠️ 请确保:
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:320]  1. 微信PC版已登录且窗口打开
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:321]  2. 网络连接正常
2025-08-02 22:14:35 [__main__] [INFO] [wx.py:322]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:14:35 [__main__] [ERROR] [wx.py:64]  检查微信状态失败: 'WeChat' object has no attribute 'IsOnline'
2025-08-02 22:14:35 [__main__] [ERROR] [wx.py:326]  微信状态检查失败，程序退出
2025-08-02 22:14:45 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:310]  ==================================================
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:311]  微信自动加好友工具启动
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:312]  ==================================================
2025-08-02 22:14:45 [__main__] [WARNING] [wx.py:221]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:319]  ⚠️ 请确保:
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:320]  1. 微信PC版已登录且窗口打开
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:321]  2. 网络连接正常
2025-08-02 22:14:45 [__main__] [INFO] [wx.py:322]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:14:45 [__main__] [ERROR] [wx.py:64]  检查微信状态失败: 'WeChat' object has no attribute 'IsOnline'
2025-08-02 22:14:45 [__main__] [ERROR] [wx.py:326]  微信状态检查失败，程序退出
2025-08-02 22:17:26 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:17:27 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:20:47 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:330]  ==================================================
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:331]  微信自动加好友工具启动
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:332]  ==================================================
2025-08-02 22:20:47 [__main__] [WARNING] [wx.py:235]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:339]  ⚠️ 请确保:
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:340]  1. 微信PC版已登录且窗口打开
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:341]  2. 网络连接正常
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:342]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:20:47 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:20:48 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:20:48 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:21:51 [__main__] [INFO] [wx.py:364]  准备批量添加 3 个好友 (手动指导模式)...
2025-08-02 22:22:31 [__main__] [INFO] [wx.py:183]  开始批量处理 3 个好友...
2025-08-02 22:22:31 [__main__] [INFO] [wx.py:186]  使用手动指导模式
2025-08-02 22:22:31 [__main__] [INFO] [wx.py:189]  
[1/3] 处理: user001
2025-08-02 22:23:37 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:330]  ==================================================
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:331]  微信自动加好友工具启动
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:332]  ==================================================
2025-08-02 22:23:37 [__main__] [WARNING] [wx.py:235]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:339]  ⚠️ 请确保:
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:340]  1. 微信PC版已登录且窗口打开
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:341]  2. 网络连接正常
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:342]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:23:37 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:23:38 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:23:38 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:23:50 [__main__] [INFO] [wx.py:364]  准备批量添加 3 个好友 (自动检测模式)...
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:183]  开始批量处理 3 个好友...
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:194]  使用自动检测模式
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:197]  
[1/3] 处理: user001
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:102]  开始添加好友: user001
2025-08-02 22:24:06 [__main__] [WARNING] [wx.py:108]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:109]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:110]  需要添加的好友信息:
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:111]    搜索关键词: user001
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:112]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:114]    建议备注: 客户001
2025-08-02 22:24:06 [__main__] [INFO] [wx.py:116]    建议标签: 客户, 潜在合作
2025-08-02 22:24:06 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: user001, False, False, 0.5
2025-08-02 22:24:12 [__main__] [INFO] [wx.py:121]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-02 22:24:12 [__main__] [INFO] [wx.py:208]  ⏱️ 等待 9.6 秒...
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:197]  
[2/3] 处理: user002
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:102]  开始添加好友: user002
2025-08-02 22:24:22 [__main__] [WARNING] [wx.py:108]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:109]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:110]  需要添加的好友信息:
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:111]    搜索关键词: user002
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:112]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:114]    建议备注: 行业联系人002
2025-08-02 22:24:22 [__main__] [INFO] [wx.py:116]    建议标签: 行业, 交流
2025-08-02 22:24:22 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: user002, False, False, 0.5
2025-08-02 22:24:28 [__main__] [INFO] [wx.py:121]  ✅ user002 可能已经是好友或可以直接聊天
2025-08-02 22:24:28 [__main__] [INFO] [wx.py:208]  ⏱️ 等待 7.1 秒...
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:197]  
[3/3] 处理: user003
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:102]  开始添加好友: user003
2025-08-02 22:24:35 [__main__] [WARNING] [wx.py:108]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:109]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:110]  需要添加的好友信息:
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:111]    搜索关键词: user003
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:112]    验证消息: 看到您在XX群的专业分享，希望能向您请教
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:114]    建议备注: 专业人士003
2025-08-02 22:24:35 [__main__] [INFO] [wx.py:116]    建议标签: 专业, 学习
2025-08-02 22:24:35 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: user003, False, False, 0.5
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:121]  ✅ user003 可能已经是好友或可以直接聊天
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:381]  
==============================
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:382]  批量处理好友完成！
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:383]  ✅ 成功: 3 人
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:384]  ❌ 失败: 0 人
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:385]  ⏭️ 跳过: 0 人
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:386]  ==============================
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:407]  
⚠️ 重要提示:
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:408]  1. 微信对频繁添加好友有严格限制
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:409]  2. 建议每日添加好友不超过10次
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:410]  3. 如遇到限制，请24小时后再试
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:411]  4. 请遵守微信使用规范，避免账号风险
2025-08-02 22:24:42 [__main__] [INFO] [wx.py:413]  程序执行完成！
2025-08-02 22:24:53 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:330]  ==================================================
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:331]  微信自动加好友工具启动
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:332]  ==================================================
2025-08-02 22:24:53 [__main__] [WARNING] [wx.py:235]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:339]  ⚠️ 请确保:
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:340]  1. 微信PC版已登录且窗口打开
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:341]  2. 网络连接正常
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:342]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:24:53 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:25:04 [__main__] [INFO] [wx.py:276]  正在获取会话统计信息...
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:281]  当前会话总数: 10
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:284]  会话详情:
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:285]    总会话数: 10
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:290]    会话1: {'name': '微信运动', 'time': '22:10', 'content': '[不支持类型消息]', 'isnew': True, 'new_count': 1, 'ismute': False}
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:290]    会话2: {'name': '宝宝', 'time': '21:57', 'content': '好', 'isnew': False, 'new_count': 0, 'ismute': False}
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:290]    会话3: {'name': '文件传输助手', 'time': '19:59', 'content': '[图片]', 'isnew': False, 'new_count': 0, 'ismute': False}
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:290]    会话4: {'name': '微信游戏', 'time': '19:52', 'content': '[不支持类型消息]', 'isnew': True, 'new_count': 5, 'ismute': True}
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:290]    会话5: {'name': '订阅号', 'time': '17:17', 'content': 'ROM乐园: 面具Magisk正式版V30.1推送下载-修复模块挂载-适配安卓16系统', 'isnew': True, 'new_count': 1, 'ismute': False}
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:293]    ... 还有 5 个会话
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:407]  
⚠️ 重要提示:
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:408]  1. 微信对频繁添加好友有严格限制
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:409]  2. 建议每日添加好友不超过10次
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:410]  3. 如遇到限制，请24小时后再试
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:411]  4. 请遵守微信使用规范，避免账号风险
2025-08-02 22:25:05 [__main__] [INFO] [wx.py:413]  程序执行完成！
2025-08-02 22:25:31 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:330]  ==================================================
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:331]  微信自动加好友工具启动
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:332]  ==================================================
2025-08-02 22:25:31 [__main__] [WARNING] [wx.py:235]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:339]  ⚠️ 请确保:
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:340]  1. 微信PC版已登录且窗口打开
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:341]  2. 网络连接正常
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:342]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:25:31 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:25:32 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:25:32 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:25:41 [__main__] [INFO] [wx.py:364]  准备批量添加 3 个好友 (手动指导模式)...
2025-08-02 22:25:49 [__main__] [INFO] [wx.py:183]  开始批量处理 3 个好友...
2025-08-02 22:25:49 [__main__] [INFO] [wx.py:186]  使用手动指导模式
2025-08-02 22:25:49 [__main__] [INFO] [wx.py:189]  
[1/3] 处理: user001
2025-08-02 22:25:56 [__main__] [INFO] [wx.py:189]  
[2/3] 处理: user002
2025-08-02 22:25:58 [__main__] [INFO] [wx.py:189]  
[3/3] 处理: user003
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:381]  
==============================
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:382]  批量处理好友完成！
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:383]  ✅ 成功: 3 人
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:384]  ❌ 失败: 0 人
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:385]  ⏭️ 跳过: 0 人
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:386]  ==============================
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:407]  
⚠️ 重要提示:
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:408]  1. 微信对频繁添加好友有严格限制
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:409]  2. 建议每日添加好友不超过10次
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:410]  3. 如遇到限制，请24小时后再试
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:411]  4. 请遵守微信使用规范，避免账号风险
2025-08-02 22:25:59 [__main__] [INFO] [wx.py:413]  程序执行完成！
2025-08-02 22:26:17 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:330]  ==================================================
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:331]  微信自动加好友工具启动
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:332]  ==================================================
2025-08-02 22:26:17 [__main__] [WARNING] [wx.py:235]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:339]  ⚠️ 请确保:
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:340]  1. 微信PC版已登录且窗口打开
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:341]  2. 网络连接正常
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:342]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:26:17 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:26:26 [__main__] [INFO] [wx.py:364]  准备批量添加 3 个好友 (自动检测模式)...
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:183]  开始批量处理 3 个好友...
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:194]  使用自动检测模式
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:197]  
[1/3] 处理: user001
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:102]  开始添加好友: user001
2025-08-02 22:26:34 [__main__] [WARNING] [wx.py:108]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:109]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:110]  需要添加的好友信息:
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:111]    搜索关键词: user001
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:112]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:114]    建议备注: 客户001
2025-08-02 22:26:34 [__main__] [INFO] [wx.py:116]    建议标签: 客户, 潜在合作
2025-08-02 22:26:34 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: user001, False, False, 0.5
2025-08-02 22:26:40 [__main__] [INFO] [wx.py:121]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-02 22:26:40 [__main__] [INFO] [wx.py:208]  ⏱️ 等待 9.5 秒...
2025-08-02 22:27:35 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:330]  ==================================================
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:331]  微信自动加好友工具启动
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:332]  ==================================================
2025-08-02 22:27:36 [__main__] [WARNING] [wx.py:235]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:339]  ⚠️ 请确保:
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:340]  1. 微信PC版已登录且窗口打开
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:341]  2. 网络连接正常
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:342]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:27:36 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:27:39 [__main__] [INFO] [wx.py:364]  准备批量添加 3 个好友 (自动检测模式)...
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:183]  开始批量处理 3 个好友...
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:194]  使用自动检测模式
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:197]  
[1/3] 处理: bitlesu
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:102]  开始添加好友: bitlesu
2025-08-02 22:27:42 [__main__] [WARNING] [wx.py:108]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:109]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:110]  需要添加的好友信息:
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:111]    搜索关键词: bitlesu
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:112]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:114]    建议备注: 客户001
2025-08-02 22:27:42 [__main__] [INFO] [wx.py:116]    建议标签: 客户, 潜在合作
2025-08-02 22:27:42 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: bitlesu, False, False, 0.5
2025-08-02 22:27:48 [__main__] [INFO] [wx.py:121]  ✅ bitlesu 可能已经是好友或可以直接聊天
2025-08-02 22:27:48 [__main__] [INFO] [wx.py:208]  ⏱️ 等待 8.5 秒...
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:197]  
[2/3] 处理: ibitle
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:102]  开始添加好友: ibitle
2025-08-02 22:27:56 [__main__] [WARNING] [wx.py:108]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:109]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:110]  需要添加的好友信息:
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:111]    搜索关键词: ibitle
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:112]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:114]    建议备注: 行业联系人002
2025-08-02 22:27:56 [__main__] [INFO] [wx.py:116]    建议标签: 行业, 交流
2025-08-02 22:27:56 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: ibitle, False, False, 0.5
2025-08-02 22:27:58 [wxauto] [DEBUG] [sessionbox.py:102]  ibitle 匹配到微信号：比特币
2025-08-02 22:27:58 [__main__] [INFO] [wx.py:121]  ✅ ibitle 可能已经是好友或可以直接聊天
2025-08-02 22:27:58 [__main__] [INFO] [wx.py:208]  ⏱️ 等待 7.3 秒...
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:197]  
[3/3] 处理: user003
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:102]  开始添加好友: user003
2025-08-02 22:28:05 [__main__] [WARNING] [wx.py:108]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:109]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:110]  需要添加的好友信息:
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:111]    搜索关键词: user003
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:112]    验证消息: 看到您在XX群的专业分享，希望能向您请教
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:114]    建议备注: 专业人士003
2025-08-02 22:28:05 [__main__] [INFO] [wx.py:116]    建议标签: 专业, 学习
2025-08-02 22:28:05 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: user003, False, False, 0.5
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:121]  ✅ user003 可能已经是好友或可以直接聊天
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:381]  
==============================
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:382]  批量处理好友完成！
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:383]  ✅ 成功: 3 人
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:384]  ❌ 失败: 0 人
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:385]  ⏭️ 跳过: 0 人
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:386]  ==============================
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:407]  
⚠️ 重要提示:
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:408]  1. 微信对频繁添加好友有严格限制
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:409]  2. 建议每日添加好友不超过10次
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:410]  3. 如遇到限制，请24小时后再试
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:411]  4. 请遵守微信使用规范，避免账号风险
2025-08-02 22:28:11 [__main__] [INFO] [wx.py:413]  程序执行完成！
2025-08-02 22:29:44 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:29:44 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:32:35 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:36:01 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:417]  ==================================================
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:418]  微信自动加好友工具启动
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:419]  ==================================================
2025-08-02 22:36:01 [__main__] [WARNING] [wx.py:322]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:426]  ⚠️ 请确保:
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:427]  1. 微信PC版已登录且窗口打开
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:428]  2. 网络连接正常
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:429]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:36:01 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:37:35 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:37:35 [wx] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:37:35 [wx] [INFO] [wx.py:93]  使用UI自动化添加好友: nonexistent_user_12345
2025-08-02 22:50:06 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:23]  微信客户端初始化成功
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:417]  ==================================================
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:418]  微信自动加好友工具启动
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:419]  ==================================================
2025-08-02 22:50:06 [__main__] [WARNING] [wx.py:322]  文件 friends_data.json 不存在，使用默认数据
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:426]  ⚠️ 请确保:
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:427]  1. 微信PC版已登录且窗口打开
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:428]  2. 网络连接正常
2025-08-02 22:50:06 [__main__] [INFO] [wx.py:429]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-02 22:50:07 [__main__] [INFO] [wx.py:58]  微信窗口检测成功
2025-08-02 22:50:07 [__main__] [INFO] [wx.py:63]  微信状态正常，当前有 10 个会话
2025-08-02 22:50:07 [__main__] [INFO] [wx.py:69]  当前微信账号: 又是一年冬
2025-08-02 22:50:12 [__main__] [INFO] [wx.py:451]  准备批量添加 3 个好友 (UI自动化模式)...
2025-08-02 22:50:15 [__main__] [INFO] [wx.py:266]  开始批量处理 3 个好友...
2025-08-02 22:50:15 [__main__] [INFO] [wx.py:277]  使用UI自动化模式
2025-08-02 22:50:15 [__main__] [INFO] [wx.py:280]  
[1/3] 处理: bitlesu
2025-08-02 22:50:15 [__main__] [INFO] [wx.py:194]  开始添加好友: bitlesu
2025-08-02 22:50:15 [__main__] [INFO] [wx.py:93]  使用UI自动化添加好友: bitlesu
2025-08-02 22:50:26 [__main__] [WARNING] [wx.py:207]  ⚠️ 未找到用户 bitlesu 或用户隐私受限
2025-08-02 22:50:26 [__main__] [INFO] [wx.py:295]  ⏱️ 安全等待 18.9 秒...
2025-08-02 22:50:45 [__main__] [INFO] [wx.py:280]  
[2/3] 处理: ibitle
2025-08-02 22:50:45 [__main__] [INFO] [wx.py:194]  开始添加好友: ibitle
2025-08-02 22:50:45 [__main__] [INFO] [wx.py:93]  使用UI自动化添加好友: ibitle
2025-08-02 22:50:52 [__main__] [WARNING] [wx.py:207]  ⚠️ 未找到用户 ibitle 或用户隐私受限
2025-08-02 22:50:52 [__main__] [INFO] [wx.py:295]  ⏱️ 安全等待 17.8 秒...
2025-08-02 22:51:10 [__main__] [INFO] [wx.py:280]  
[3/3] 处理: user003
2025-08-02 22:51:10 [__main__] [INFO] [wx.py:194]  开始添加好友: user003
2025-08-02 22:51:10 [__main__] [INFO] [wx.py:93]  使用UI自动化添加好友: user003
2025-08-02 22:51:17 [__main__] [WARNING] [wx.py:207]  ⚠️ 未找到微信窗口
2025-08-02 22:51:17 [__main__] [WARNING] [wx.py:289]  连续失败次数过多，可能遇到限制或网络问题
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:469]  
==============================
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:470]  批量处理好友完成！
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:471]  ✅ 成功: 0 人
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:472]  ❌ 失败: 3 人
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:473]  ⏭️ 跳过: 0 人
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:474]  ==============================
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:495]  
⚠️ 重要提示:
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:496]  1. 微信对频繁添加好友有严格限制
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:497]  2. 建议每日添加好友不超过10次
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:498]  3. 如遇到限制，请24小时后再试
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:499]  4. 请遵守微信使用规范，避免账号风险
2025-08-02 22:51:17 [__main__] [INFO] [wx.py:501]  程序执行完成！
