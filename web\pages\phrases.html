<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>常用语管理</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//cdn.jsdelivr.net/npm/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        .container {
            padding: 24px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .header-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .title-text h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
        }

        .title-text p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #64748b;
        }

        .content-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .add-form {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid #e2e8f0;
        }

        .phrases-list {
            display: grid;
            gap: 12px;
        }

        .phrase-item {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s ease;
            position: relative;
        }

        .phrase-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 12px rgba(59, 130, 246, 0.1);
        }

        .phrase-content {
            font-size: 14px;
            color: #374151;
            line-height: 1.6;
            margin-bottom: 12px;
            word-break: break-all;
        }

        .phrase-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }

        .phrase-actions {
            display: flex;
            gap: 8px;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fef2f2;
            color: #991b1b;
        }

        /* 按钮样式 */
        .primary-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            border: none !important;
            border-radius: 6px !important;
            transition: all 0.3s ease !important;
        }

        .primary-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3) !important;
        }

        /* 输入框样式 */
        .el-textarea__inner {
            border-radius: 6px !important;
            border: 1px solid #d1d5db !important;
            transition: all 0.3s ease !important;
        }

        .el-textarea__inner:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-icon {
            font-size: 48px;
            color: #d1d5db;
            margin-bottom: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .header-section,
            .content-section {
                padding: 16px;
            }
            
            .phrase-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 头部区域 -->
            <div class="header-section animate__animated animate__fadeInDown">
                <div class="header-title">
                    <div class="header-icon">
                        <el-icon><ChatLineRound /></el-icon>
                    </div>
                    <div class="title-text">
                        <h1>常用语管理</h1>
                        <p>管理微信添加好友时的验证消息模板，提高工作效率</p>
                    </div>
                </div>
                
                <div style="display: flex; gap: 12px; align-items: center;">
                    <el-tag type="info" size="small">
                        <el-icon style="margin-right: 4px;"><Document /></el-icon>
                        共 {{ phrases.length }} 条常用语
                    </el-tag>
                    <el-tag type="success" size="small">
                        <el-icon style="margin-right: 4px;"><Check /></el-icon>
                        {{ activePhrases.length }} 条启用中
                    </el-tag>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-section animate__animated animate__fadeInUp">
                <!-- 添加新常用语 -->
                <div class="add-form">
                    <h3 style="margin: 0 0 16px 0; color: #374151; font-size: 16px;">
                        <el-icon style="margin-right: 8px;"><Plus /></el-icon>
                        添加新常用语
                    </h3>
                    
                    <el-form :model="newPhrase" label-width="0">
                        <el-form-item>
                            <el-input
                                v-model="newPhrase.content"
                                type="textarea"
                                :rows="3"
                                placeholder="请输入常用的验证消息内容..."
                                maxlength="200"
                                show-word-limit
                                clearable
                            />
                        </el-form-item>
                        <el-form-item style="margin-bottom: 0;">
                            <el-button 
                                type="primary" 
                                class="primary-btn"
                                @click="addPhrase"
                                :loading="adding"
                                :disabled="!newPhrase.content.trim()"
                            >
                                <el-icon><Plus /></el-icon>
                                添加常用语
                            </el-button>
                            <el-button @click="clearForm">
                                <el-icon><Refresh /></el-icon>
                                清空
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 常用语列表 -->
                <div v-if="phrases.length > 0">
                    <h3 style="margin: 0 0 16px 0; color: #374151; font-size: 16px;">
                        <el-icon style="margin-right: 8px;"><List /></el-icon>
                        常用语列表
                    </h3>
                    
                    <div class="phrases-list">
                        <div 
                            v-for="phrase in phrases" 
                            :key="phrase.id" 
                            class="phrase-item animate__animated animate__fadeInUp"
                        >
                            <div class="phrase-content">{{ phrase.content }}</div>
                            <div class="phrase-meta">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span>创建时间：{{ formatTime(phrase.create_time) }}</span>
                                    <span 
                                        class="status-badge"
                                        :class="phrase.status === 1 ? 'status-active' : 'status-inactive'"
                                    >
                                        {{ phrase.status === 1 ? '启用' : '禁用' }}
                                    </span>
                                </div>
                                <div class="phrase-actions">
                                    <el-button 
                                        size="small" 
                                        type="primary" 
                                        link
                                        @click="toggleStatus(phrase)"
                                    >
                                        {{ phrase.status === 1 ? '禁用' : '启用' }}
                                    </el-button>
                                    <el-button 
                                        size="small" 
                                        type="warning" 
                                        link
                                        @click="editPhrase(phrase)"
                                    >
                                        编辑
                                    </el-button>
                                    <el-button 
                                        size="small" 
                                        type="danger" 
                                        link
                                        @click="deletePhrase(phrase)"
                                    >
                                        删除
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-else class="empty-state">
                    <div class="empty-icon">
                        <el-icon><Document /></el-icon>
                    </div>
                    <p>暂无常用语，点击上方添加按钮创建第一条常用语吧！</p>
                </div>
            </div>
        </div>

        <!-- 编辑对话框 -->
        <el-dialog
            v-model="editDialog.visible"
            title="编辑常用语"
            width="500px"
            :before-close="cancelEdit"
        >
            <el-form :model="editDialog.form" label-width="0">
                <el-form-item>
                    <el-input
                        v-model="editDialog.form.content"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入常用语内容..."
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <el-button @click="cancelEdit">取消</el-button>
                <el-button 
                    type="primary" 
                    class="primary-btn"
                    @click="saveEdit"
                    :loading="editDialog.saving"
                >
                    保存
                </el-button>
            </template>
        </el-dialog>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive, computed, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                // 数据
                const phrases = ref([]);
                const adding = ref(false);

                const newPhrase = reactive({
                    content: ''
                });

                const editDialog = reactive({
                    visible: false,
                    saving: false,
                    form: {
                        id: null,
                        content: ''
                    }
                });

                // 计算属性
                const activePhrases = computed(() => {
                    return phrases.value.filter(phrase => phrase.status === 1);
                });

                // 获取pywebview API
                const getPywebviewApi = () => {
                    if (window.pywebview && window.pywebview.api) {
                        return window.pywebview.api;
                    }
                    return null;
                };

                // 加载常用语列表
                const loadPhrases = async () => {
                    try {
                        const api = getPywebviewApi();
                        if (!api) {
                            throw new Error('无法连接到后端API');
                        }

                        const result = await api.get_wechat_phrases();
                        if (result && result.success) {
                            phrases.value = result.data || [];
                        } else {
                            throw new Error(result?.message || '获取常用语失败');
                        }
                    } catch (error) {
                        console.error('加载常用语失败:', error);
                        ElMessage.error('加载常用语失败：' + error.message);
                    }
                };

                // 添加常用语
                const addPhrase = async () => {
                    if (!newPhrase.content.trim()) {
                        ElMessage.warning('请输入常用语内容');
                        return;
                    }

                    adding.value = true;
                    try {
                        const api = getPywebviewApi();
                        if (!api) {
                            throw new Error('无法连接到后端API');
                        }

                        const result = await api.add_wechat_phrase({
                            content: newPhrase.content.trim()
                        });

                        if (result && result.success) {
                            ElMessage.success('添加成功');
                            newPhrase.content = '';
                            await loadPhrases();
                        } else {
                            throw new Error(result?.message || '添加失败');
                        }
                    } catch (error) {
                        console.error('添加常用语失败:', error);
                        ElMessage.error('添加失败：' + error.message);
                    } finally {
                        adding.value = false;
                    }
                };

                // 清空表单
                const clearForm = () => {
                    newPhrase.content = '';
                };

                // 切换状态
                const toggleStatus = async (phrase) => {
                    try {
                        const api = getPywebviewApi();
                        if (!api) {
                            throw new Error('无法连接到后端API');
                        }

                        const newStatus = phrase.status === 1 ? 0 : 1;
                        const result = await api.update_wechat_phrase({
                            id: phrase.id,
                            status: newStatus
                        });

                        if (result && result.success) {
                            phrase.status = newStatus;
                            ElMessage.success(newStatus === 1 ? '已启用' : '已禁用');
                        } else {
                            throw new Error(result?.message || '更新失败');
                        }
                    } catch (error) {
                        console.error('更新状态失败:', error);
                        ElMessage.error('更新失败：' + error.message);
                    }
                };

                // 编辑常用语
                const editPhrase = (phrase) => {
                    editDialog.form.id = phrase.id;
                    editDialog.form.content = phrase.content;
                    editDialog.visible = true;
                };

                // 保存编辑
                const saveEdit = async () => {
                    if (!editDialog.form.content.trim()) {
                        ElMessage.warning('请输入常用语内容');
                        return;
                    }

                    editDialog.saving = true;
                    try {
                        const api = getPywebviewApi();
                        if (!api) {
                            throw new Error('无法连接到后端API');
                        }

                        const result = await api.update_wechat_phrase({
                            id: editDialog.form.id,
                            content: editDialog.form.content.trim()
                        });

                        if (result && result.success) {
                            ElMessage.success('更新成功');
                            editDialog.visible = false;
                            await loadPhrases();
                        } else {
                            throw new Error(result?.message || '更新失败');
                        }
                    } catch (error) {
                        console.error('更新常用语失败:', error);
                        ElMessage.error('更新失败：' + error.message);
                    } finally {
                        editDialog.saving = false;
                    }
                };

                // 取消编辑
                const cancelEdit = () => {
                    editDialog.visible = false;
                    editDialog.form.id = null;
                    editDialog.form.content = '';
                };

                // 删除常用语
                const deletePhrase = async (phrase) => {
                    try {
                        await ElMessageBox.confirm(
                            `确定要删除这条常用语吗？\n\n"${phrase.content}"`,
                            '确认删除',
                            {
                                confirmButtonText: '确定删除',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );

                        const api = getPywebviewApi();
                        if (!api) {
                            throw new Error('无法连接到后端API');
                        }

                        const result = await api.delete_wechat_phrase({
                            id: phrase.id
                        });

                        if (result && result.success) {
                            ElMessage.success('删除成功');
                            await loadPhrases();
                        } else {
                            throw new Error(result?.message || '删除失败');
                        }
                    } catch (error) {
                        if (error === 'cancel') {
                            return;
                        }
                        console.error('删除常用语失败:', error);
                        ElMessage.error('删除失败：' + error.message);
                    }
                };

                // 格式化时间
                const formatTime = (timeStr) => {
                    try {
                        const date = new Date(timeStr);
                        return date.toLocaleString('zh-CN');
                    } catch {
                        return timeStr;
                    }
                };

                // 组件挂载时加载数据
                onMounted(() => {
                    loadPhrases();
                });

                return {
                    // 数据
                    phrases,
                    adding,
                    newPhrase,
                    editDialog,

                    // 计算属性
                    activePhrases,

                    // 方法
                    addPhrase,
                    clearForm,
                    toggleStatus,
                    editPhrase,
                    saveEdit,
                    cancelEdit,
                    deletePhrase,
                    formatTime
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
