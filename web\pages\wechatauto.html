<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信自动化工具</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//cdn.jsdelivr.net/npm/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            overflow: hidden;
        }

        .main-container {
            height: 100vh;
            padding: 16px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        /* 功能卡片区域 */
        .cards-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .function-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 20px;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: fit-content;
            backdrop-filter: blur(10px);
        }

        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
            border-radius: 16px 16px 0 0;
        }

        .function-card::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .function-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                0 4px 12px rgba(59, 130, 246, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .function-card:hover::after {
            opacity: 1;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 16px;
        }

        .card-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            font-size: 22px;
            box-shadow:
                0 4px 12px rgba(59, 130, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .card-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: transform 0.6s ease;
        }

        .function-card:hover .card-icon::before {
            transform: rotate(45deg) translate(100%, 100%);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .card-subtitle {
            font-size: 12px;
            color: #64748b;
            margin: 2px 0 0 0;
        }

        /* 控制台区域 */
        .console-section {
            flex: 1;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 20px;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .console-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e2e8f0;
        }

        .console-title {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1e293b;
            font-size: 16px;
            font-weight: 600;
        }

        .console-controls {
            display: flex;
            gap: 8px;
        }

        .console-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .console-btn.close { background: #ff5f57; }
        .console-btn.minimize { background: #ffbd2e; }
        .console-btn.maximize { background: #28ca42; }

        .console-content {
            height: calc(100% - 60px);
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            padding: 16px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            border: 1px solid #e2e8f0;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .console-line {
            margin-bottom: 6px;
            white-space: pre-wrap;
            word-break: break-all;
            padding: 2px 0;
        }

        .log-info { color: #3b82f6; }
        .log-warning { color: #f59e0b; }
        .log-error { color: #ef4444; }
        .log-success { color: #10b981; }

        /* 滚动条样式 */
        .console-content::-webkit-scrollbar {
            width: 6px;
        }

        .console-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .console-content::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .console-content::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 输入框样式 */
        .el-input__wrapper {
            background: rgba(255, 255, 255, 0.9) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }

        .el-input__wrapper:hover {
            border-color: #667eea !important;
        }

        .el-input__wrapper.is-focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
        }

        /* 按钮样式 */
        .action-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            border: none !important;
            border-radius: 6px !important;
            transition: all 0.3s ease !important;
            font-size: 14px !important;
            height: 36px !important;
        }

        .action-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3) !important;
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-ready {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .status-processing {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .status-error {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 主内容网格布局 */
        .main-content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            flex: 1;
            min-height: 0; /* 重要：允许flex子项收缩 */
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .cards-section {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 12px;
                gap: 12px;
            }

            .cards-section {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .function-card {
                padding: 16px;
            }

            /* 小屏幕时垂直布局 */
            .main-content-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            /* 调整表格在小屏幕的显示 */
            .el-table .el-table__cell {
                padding: 8px 4px !important;
            }

            /* 控制台在小屏幕时的高度 */
            .console-section {
                min-height: 300px;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                padding: 8px;
                gap: 8px;
            }

            .cards-section {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .function-card {
                padding: 12px;
            }

            .card-title {
                font-size: 14px;
            }

            .card-subtitle {
                font-size: 11px;
            }

            /* 超小屏幕时隐藏部分列 */
            .el-table .hidden-xs {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="main-container">
            <!-- 功能卡片区域 -->
            <div class="cards-section">
                <!-- 用户数据卡片 -->
                <div class="function-card animate__animated animate__fadeInUp">
                    <div class="card-header">
                        <div class="card-icon">
                            <el-icon><User /></el-icon>
                        </div>
                        <div>
                            <h3 class="card-title">用户数据</h3>
                            <p class="card-subtitle">从数据库加载用户信息</p>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>总用户数：</span>
                            <el-tag type="info">{{ users.length }} 人</el-tag>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>已选择：</span>
                            <el-tag type="primary">{{ selectedUsers.length }} 人</el-tag>
                        </div>
                        <el-button
                            type="info"
                            size="small"
                            @click="loadUsers"
                            :loading="loadingUsers"
                        >
                            <el-icon><Refresh /></el-icon>
                            刷新用户数据
                        </el-button>
                    </div>
                </div>

                <!-- 常用语卡片 -->
                <div class="function-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                    <div class="card-header">
                        <div class="card-icon">
                            <el-icon><ChatLineRound /></el-icon>
                        </div>
                        <div>
                            <h3 class="card-title">验证消息</h3>
                            <p class="card-subtitle">选择常用验证消息</p>
                        </div>
                    </div>

                    <el-select
                        v-model="selectedPhrase"
                        placeholder="选择常用验证消息"
                        style="width: 100%;"
                        @change="onPhraseChange"
                    >
                        <el-option
                            v-for="phrase in activePhrases"
                            :key="phrase.id"
                            :label="phrase.content"
                            :value="phrase.content"
                        />
                    </el-select>

                    <el-input
                        v-model="customMessage"
                        type="textarea"
                        :rows="2"
                        placeholder="或输入自定义验证消息..."
                        style="margin-top: 12px;"
                    />
                </div>

                <!-- 系统状态卡片 -->
                <div class="function-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                    <div class="card-header">
                        <div class="card-icon">
                            <el-icon><Monitor /></el-icon>
                        </div>
                        <div>
                            <h3 class="card-title">系统状态</h3>
                            <p class="card-subtitle">微信自动化系统运行状态</p>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 12px;">微信连接：</span>
                            <span class="status-indicator" :class="wechatStatus.class">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: currentColor;" :class="{ pulse: wechatStatus.connected }"></div>
                                {{ wechatStatus.text }}
                            </span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 12px;">自动化引擎：</span>
                            <span class="status-indicator status-ready">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: currentColor;" class="pulse"></div>
                                已就绪
                            </span>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 12px;">截图功能：</span>
                            <span class="status-indicator status-ready">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: currentColor;"></div>
                                正常
                            </span>
                        </div>

                        <el-button
                            type="info"
                            size="small"
                            @click="checkWechatStatus"
                            :loading="statusChecking"
                            style="margin-top: 4px;"
                        >
                            <el-icon><Refresh /></el-icon>
                            检查微信状态
                        </el-button>
                    </div>
                </div>

                <!-- 批量操作卡片 -->
                <div class="function-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="card-header">
                        <div class="card-icon">
                            <el-icon><UserPlus /></el-icon>
                        </div>
                        <div>
                            <h3 class="card-title">批量添加</h3>
                            <p class="card-subtitle">批量添加选中的用户</p>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <el-button
                            type="primary"
                            class="action-btn"
                            @click="batchAddFriends"
                            :loading="isProcessing"
                            :disabled="selectedUsers.length === 0 || (!selectedPhrase && !customMessage)"
                            style="width: 100%;"
                        >
                            <el-icon><Plus /></el-icon>
                            添加 {{ selectedUsers.length }} 个好友
                        </el-button>

                        <div style="display: flex; gap: 8px;">
                            <el-button
                                size="small"
                                @click="selectAllUsers"
                                :disabled="users.length === 0"
                            >
                                全选
                            </el-button>
                            <el-button
                                size="small"
                                @click="clearSelection"
                                :disabled="selectedUsers.length === 0"
                            >
                                清空
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户数据表格区域 -->
            <div class="main-content-grid">
                <!-- 用户数据表格 -->
                <div class="console-section animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                    <div class="console-header">
                        <div class="console-title">
                            <el-icon><User /></el-icon>
                            <span>用户数据表格</span>
                            <span style="font-size: 12px; opacity: 0.7; margin-left: 8px;">
                                共 {{ users.length }} 条数据
                            </span>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <el-button size="small" @click="loadUsers" :loading="loadingUsers">
                                <el-icon><Refresh /></el-icon>
                            </el-button>
                        </div>
                    </div>
                    <div style="height: calc(100% - 60px); overflow: auto;">
                        <el-table
                            :data="users"
                            style="width: 100%; height: 100%;"
                            @selection-change="handleSelectionChange"
                            size="small"
                            stripe
                        >
                            <el-table-column type="selection" width="40" />
                            <el-table-column prop="unique_id" label="微信号" width="120" show-overflow-tooltip />
                            <el-table-column prop="signature" label="个性签名" show-overflow-tooltip />
                            <el-table-column prop="follower_count" label="粉丝数" width="80" />
                            <el-table-column label="操作" width="80">
                                <template #default="scope">
                                    <el-button
                                        size="small"
                                        type="primary"
                                        link
                                        @click="addSingleFriend(scope.row)"
                                        :loading="isProcessing"
                                    >
                                        添加
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <div v-if="users.length === 0" style="text-align: center; padding: 40px; color: #6b7280;">
                            <el-icon size="48" style="color: #d1d5db; margin-bottom: 16px;"><User /></el-icon>
                            <p>暂无用户数据</p>
                            <el-button type="primary" @click="loadUsers" :loading="loadingUsers">
                                <el-icon><Refresh /></el-icon>
                                加载数据
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 控制台区域 -->
                <div class="console-section animate__animated animate__fadeInUp" style="animation-delay: 0.5s;">
                    <div class="console-header">
                        <div class="console-title">
                            <el-icon><Monitor /></el-icon>
                            <span>实时控制台</span>
                            <span style="font-size: 12px; opacity: 0.7; margin-left: 8px;">
                                {{ new Date().toLocaleString() }}
                            </span>
                        </div>
                        <div class="console-controls">
                            <div class="console-btn close" @click="clearConsole"></div>
                            <div class="console-btn minimize"></div>
                            <div class="console-btn maximize" @click="scrollToBottom"></div>
                        </div>
                    </div>
                    <div class="console-content" ref="consoleContainer">
                        <div v-for="(log, index) in consoleLogs" :key="index" class="console-line" :class="getLogClass(log.type)">
                            <span style="opacity: 0.7;">[{{ log.time }}]</span> {{ log.message }}
                        </div>
                        <div v-if="consoleLogs.length === 0" class="console-line log-info">
                            欢迎使用微信自动化工具！请从左侧用户表格选择要添加的好友...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive, computed, nextTick, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                // 数据管理
                const users = ref([]);
                const selectedUsers = ref([]);
                const phrases = ref([]);
                const activePhrases = computed(() => phrases.value.filter(p => p.status === 1));

                // 表单数据
                const selectedPhrase = ref('');
                const customMessage = ref('');

                // 状态管理
                const isProcessing = ref(false);
                const statusChecking = ref(false);
                const loadingUsers = ref(false);
                const consoleLogs = ref([]);
                const consoleContainer = ref(null);

                // 微信状态
                const wechatStatus = ref({
                    connected: false,
                    text: '未连接',
                    class: 'status-error'
                });

                // 获取pywebview API - 借鉴talent.html的实现
                const getPywebviewApi = () => {
                    if (window.pywebview && window.pywebview.api) {
                        return window.pywebview.api;
                    } else if (window.parent && window.parent.pywebview && window.parent.pywebview.api) {
                        return window.parent.pywebview.api;
                    } else if (window.top && window.top.pywebview && window.top.pywebview.api) {
                        return window.top.pywebview.api;
                    }
                    return null;
                };

                // 添加控制台日志
                const addLog = (message, type = 'info') => {
                    const time = new Date().toLocaleTimeString();
                    consoleLogs.value.push({
                        time,
                        message,
                        type
                    });

                    nextTick(() => {
                        scrollToBottom();
                    });
                };

                // 滚动到底部
                const scrollToBottom = () => {
                    if (consoleContainer.value) {
                        consoleContainer.value.scrollTop = consoleContainer.value.scrollHeight;
                    }
                };

                // 清空控制台
                const clearConsole = () => {
                    consoleLogs.value = [];
                    addLog('控制台已清空', 'info');
                };

                // 获取日志样式类
                const getLogClass = (type) => {
                    const classMap = {
                        'info': 'log-info',
                        'warning': 'log-warning',
                        'error': 'log-error',
                        'success': 'log-success'
                    };
                    return classMap[type] || 'log-info';
                };

                // 检查微信状态
                const checkWechatStatus = async () => {
                    statusChecking.value = true;
                    addLog('正在检查微信连接状态...', 'info');

                    try {
                        let api = getPywebviewApi();

                        // 如果API不可用，等待并重试几次
                        if (!api) {
                            addLog('等待API连接...', 'info');
                            for (let i = 0; i < 5; i++) {
                                await new Promise(resolve => setTimeout(resolve, 500));
                                api = getPywebviewApi();
                                if (api) break;
                                addLog(`重试连接API... (${i + 1}/5)`, 'info');
                            }

                            if (!api) {
                                throw new Error('无法连接到后端API，请确保应用正常运行');
                            }
                        }

                        // 调用后端检查微信状态
                        const result = await api.check_wechat_status();

                        if (result && result.success) {
                            wechatStatus.value = {
                                connected: true,
                                text: '已连接',
                                class: 'status-ready'
                            };
                            addLog('✅ 微信连接正常', 'success');
                        } else {
                            wechatStatus.value = {
                                connected: false,
                                text: '连接失败',
                                class: 'status-error'
                            };
                            addLog('❌ 微信连接失败：' + (result?.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('检查微信状态失败:', error);
                        wechatStatus.value = {
                            connected: false,
                            text: '检查失败',
                            class: 'status-error'
                        };
                        addLog('❌ 检查微信状态失败：' + error.message, 'error');
                    } finally {
                        statusChecking.value = false;
                    }
                };

                // 等待API就绪的通用方法
                const waitForApi = async () => {
                    let api = getPywebviewApi();
                    if (!api) {
                        for (let i = 0; i < 5; i++) {
                            await new Promise(resolve => setTimeout(resolve, 300));
                            api = getPywebviewApi();
                            if (api) break;
                        }
                        if (!api) {
                            throw new Error('无法连接到后端API，请确保应用正常运行');
                        }
                    }
                    return api;
                };

                // 加载用户数据
                const loadUsers = async () => {
                    loadingUsers.value = true;
                    addLog('正在加载用户数据...', 'info');

                    try {
                        const api = await waitForApi();

                        const result = await api.get_users();
                        if (result && result.success) {
                            users.value = result.data || [];
                            addLog(`✅ 加载了 ${users.value.length} 条用户数据`, 'success');
                        } else {
                            throw new Error(result?.message || '获取用户数据失败');
                        }
                    } catch (error) {
                        console.error('加载用户数据失败:', error);
                        addLog('❌ 加载用户数据失败：' + error.message, 'error');
                        ElMessage.error('加载用户数据失败：' + error.message);
                    } finally {
                        loadingUsers.value = false;
                    }
                };

                // 加载常用语
                const loadPhrases = async () => {
                    try {
                        const api = await waitForApi();

                        const result = await api.get_wechat_phrases();
                        if (result && result.success) {
                            phrases.value = result.data || [];
                        }
                    } catch (error) {
                        console.error('加载常用语失败:', error);
                    }
                };

                // 处理用户选择变化
                const handleSelectionChange = (selection) => {
                    selectedUsers.value = selection;
                };

                // 全选用户
                const selectAllUsers = () => {
                    selectedUsers.value = [...users.value];
                };

                // 清空选择
                const clearSelection = () => {
                    selectedUsers.value = [];
                };

                // 常用语选择变化
                const onPhraseChange = (value) => {
                    customMessage.value = value;
                };

                // 添加单个好友
                const addSingleFriend = async (user) => {
                    const verifyMsg = customMessage.value || selectedPhrase.value;
                    if (!verifyMsg) {
                        ElMessage.warning('请选择验证消息或输入自定义消息');
                        return;
                    }

                    isProcessing.value = true;
                    addLog(`开始添加好友：${user.unique_id}`, 'info');

                    try {
                        const api = await waitForApi();

                        const result = await api.add_wechat_friend({
                            wechat_id: user.unique_id,
                            verify_msg: verifyMsg,
                            remark_name: user.signature || user.unique_id
                        });

                        if (result && result.success) {
                            addLog(`✅ 成功添加好友：${user.unique_id}`, 'success');
                            if (result.screenshot_path) {
                                addLog(`📸 截图已保存：${result.screenshot_path}`, 'info');
                            }
                            ElMessage.success('好友申请发送成功！');
                        } else {
                            const errorMsg = result?.message || '添加好友失败';
                            addLog(`❌ 添加失败：${user.unique_id} - ${errorMsg}`, 'error');
                            ElMessage.error(errorMsg);
                        }

                    } catch (error) {
                        console.error('添加好友失败:', error);
                        const errorMsg = '添加好友失败：' + error.message;
                        addLog(`❌ ${errorMsg}`, 'error');
                        ElMessage.error(errorMsg);
                    } finally {
                        isProcessing.value = false;
                    }
                };

                // 批量添加好友
                const batchAddFriends = async () => {
                    const verifyMsg = customMessage.value || selectedPhrase.value;
                    if (!verifyMsg) {
                        ElMessage.warning('请选择验证消息或输入自定义消息');
                        return;
                    }

                    if (selectedUsers.value.length === 0) {
                        ElMessage.warning('请选择要添加的用户');
                        return;
                    }

                    try {
                        await ElMessageBox.confirm(
                            `确定要批量添加 ${selectedUsers.value.length} 个好友吗？`,
                            '确认批量添加',
                            {
                                confirmButtonText: '确定添加',
                                cancelButtonText: '取消',
                                type: 'info',
                            }
                        );
                    } catch {
                        return;
                    }

                    isProcessing.value = true;
                    addLog(`开始批量添加 ${selectedUsers.value.length} 个好友...`, 'info');

                    let successCount = 0;
                    let failCount = 0;

                    for (let i = 0; i < selectedUsers.value.length; i++) {
                        const user = selectedUsers.value[i];
                        addLog(`[${i + 1}/${selectedUsers.value.length}] 正在添加：${user.unique_id}`, 'info');

                        try {
                            const api = getPywebviewApi();
                            if (!api) {
                                throw new Error('pywebview API未就绪');
                            }

                            const result = await api.add_wechat_friend({
                                wechat_id: user.unique_id,
                                verify_msg: verifyMsg,
                                remark_name: user.signature || user.unique_id
                            });

                            if (result && result.success) {
                                successCount++;
                                addLog(`✅ [${i + 1}/${selectedUsers.value.length}] 成功：${user.unique_id}`, 'success');
                            } else {
                                failCount++;
                                addLog(`❌ [${i + 1}/${selectedUsers.value.length}] 失败：${user.unique_id} - ${result?.message || '未知错误'}`, 'error');
                            }

                            // 添加延迟避免频率过快
                            if (i < selectedUsers.value.length - 1) {
                                await new Promise(resolve => setTimeout(resolve, 2000));
                            }

                        } catch (error) {
                            failCount++;
                            addLog(`❌ [${i + 1}/${selectedUsers.value.length}] 异常：${user.unique_id} - ${error.message}`, 'error');
                        }
                    }

                    isProcessing.value = false;
                    addLog(`🎉 批量添加完成！成功：${successCount}，失败：${failCount}`, 'success');
                    ElMessage.success(`批量添加完成！成功：${successCount}，失败：${failCount}`);

                    // 清空选择
                    selectedUsers.value = [];
                };

                // 组件挂载时初始化
                onMounted(() => {
                    addLog('微信自动化工具已启动', 'success');
                    addLog('正在初始化数据...', 'info');

                    // 延迟加载确保API就绪
                    setTimeout(() => {
                        loadUsers();
                        loadPhrases();
                        checkWechatStatus();
                    }, 1000);
                });

                return {
                    // 数据
                    users,
                    selectedUsers,
                    phrases,
                    activePhrases,

                    // 表单数据
                    selectedPhrase,
                    customMessage,

                    // 状态
                    isProcessing,
                    statusChecking,
                    loadingUsers,
                    wechatStatus,

                    // 控制台
                    consoleLogs,
                    consoleContainer,

                    // 方法
                    loadUsers,
                    loadPhrases,
                    handleSelectionChange,
                    selectAllUsers,
                    clearSelection,
                    onPhraseChange,
                    addSingleFriend,
                    batchAddFriends,
                    checkWechatStatus,
                    clearConsole,
                    scrollToBottom,
                    getLogClass
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
