<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信自动化工具</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <script src="//cdn.jsdelivr.net/npm/@element-plus/icons-vue"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            overflow: hidden;
        }

        .main-container {
            height: 100vh;
            padding: 16px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        /* 功能卡片区域 */
        .cards-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .function-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: fit-content;
        }

        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
        }

        .function-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border-color: #3b82f6;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 16px;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            font-size: 20px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .card-subtitle {
            font-size: 12px;
            color: #64748b;
            margin: 2px 0 0 0;
        }

        /* 控制台区域 */
        .console-section {
            flex: 1;
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .console-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e2e8f0;
        }

        .console-title {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1e293b;
            font-size: 16px;
            font-weight: 600;
        }

        .console-controls {
            display: flex;
            gap: 8px;
        }

        .console-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .console-btn.close { background: #ff5f57; }
        .console-btn.minimize { background: #ffbd2e; }
        .console-btn.maximize { background: #28ca42; }

        .console-content {
            height: calc(100% - 60px);
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            border: 1px solid #e2e8f0;
        }

        .console-line {
            margin-bottom: 6px;
            white-space: pre-wrap;
            word-break: break-all;
            padding: 2px 0;
        }

        .log-info { color: #3b82f6; }
        .log-warning { color: #f59e0b; }
        .log-error { color: #ef4444; }
        .log-success { color: #10b981; }

        /* 滚动条样式 */
        .console-content::-webkit-scrollbar {
            width: 6px;
        }

        .console-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .console-content::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .console-content::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 输入框样式 */
        .el-input__wrapper {
            background: rgba(255, 255, 255, 0.9) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }

        .el-input__wrapper:hover {
            border-color: #667eea !important;
        }

        .el-input__wrapper.is-focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
        }

        /* 按钮样式 */
        .action-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            border: none !important;
            border-radius: 6px !important;
            transition: all 0.3s ease !important;
            font-size: 14px !important;
            height: 36px !important;
        }

        .action-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3) !important;
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-ready {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .status-processing {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .status-error {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
                gap: 10px;
            }
            
            .cards-section {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .function-card {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="main-container">
            <!-- 功能卡片区域 -->
            <div class="cards-section">
                <!-- 添加好友卡片 -->
                <div class="function-card animate__animated animate__fadeInUp">
                    <div class="card-header">
                        <div class="card-icon">
                            <el-icon><UserPlus /></el-icon>
                        </div>
                        <div>
                            <h3 class="card-title">自动添加好友</h3>
                            <p class="card-subtitle">智能搜索并添加微信好友</p>
                        </div>
                    </div>
                    
                    <el-form :model="addFriendForm" label-width="80px" size="default">
                        <el-form-item label="微信号">
                            <el-input 
                                v-model="addFriendForm.wechatId" 
                                placeholder="请输入要添加的微信号"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item label="验证消息">
                            <el-input 
                                v-model="addFriendForm.verifyMsg" 
                                placeholder="请输入验证消息"
                                type="textarea"
                                :rows="2"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item label="备注名">
                            <el-input 
                                v-model="addFriendForm.remarkName" 
                                placeholder="请输入备注名（可选）"
                                clearable
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button 
                                type="primary" 
                                class="action-btn"
                                @click="addFriend"
                                :loading="isProcessing"
                                :disabled="!addFriendForm.wechatId"
                            >
                                <el-icon><Plus /></el-icon>
                                开始添加好友
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 系统状态卡片 -->
                <div class="function-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                    <div class="card-header">
                        <div class="card-icon">
                            <el-icon><Monitor /></el-icon>
                        </div>
                        <div>
                            <h3 class="card-title">系统状态</h3>
                            <p class="card-subtitle">微信自动化系统运行状态</p>
                        </div>
                    </div>
                    
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>微信连接状态：</span>
                            <span class="status-indicator" :class="wechatStatus.class">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: currentColor;" :class="{ pulse: wechatStatus.connected }"></div>
                                {{ wechatStatus.text }}
                            </span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>自动化引擎：</span>
                            <span class="status-indicator status-ready">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: currentColor;" class="pulse"></div>
                                已就绪
                            </span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>截图功能：</span>
                            <span class="status-indicator status-ready">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: currentColor;"></div>
                                正常
                            </span>
                        </div>
                        
                        <el-button 
                            type="info" 
                            size="small" 
                            @click="checkWechatStatus"
                            :loading="statusChecking"
                            style="margin-top: 8px;"
                        >
                            <el-icon><Refresh /></el-icon>
                            检查微信状态
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 控制台区域 -->
            <div class="console-section animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="console-header">
                    <div class="console-title">
                        <el-icon><Monitor /></el-icon>
                        <span>实时控制台</span>
                        <span style="font-size: 12px; opacity: 0.7; margin-left: 8px;">
                            {{ new Date().toLocaleString() }}
                        </span>
                    </div>
                    <div class="console-controls">
                        <div class="console-btn close" @click="clearConsole"></div>
                        <div class="console-btn minimize"></div>
                        <div class="console-btn maximize" @click="scrollToBottom"></div>
                    </div>
                </div>
                <div class="console-content" ref="consoleContainer">
                    <div v-for="(log, index) in consoleLogs" :key="index" class="console-line" :class="getLogClass(log.type)">
                        <span style="opacity: 0.7;">[{{ log.time }}]</span> {{ log.message }}
                    </div>
                    <div v-if="consoleLogs.length === 0" class="console-line log-info">
                        欢迎使用微信自动化工具！请选择功能开始操作...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive, computed, nextTick, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                // 表单数据
                const addFriendForm = reactive({
                    wechatId: '',
                    verifyMsg: '',
                    remarkName: ''
                });

                // 状态管理
                const isProcessing = ref(false);
                const statusChecking = ref(false);
                const consoleLogs = ref([]);
                const consoleContainer = ref(null);

                // 微信状态
                const wechatStatus = ref({
                    connected: false,
                    text: '未连接',
                    class: 'status-error'
                });

                // 获取pywebview API
                const getPywebviewApi = () => {
                    if (window.pywebview && window.pywebview.api) {
                        return window.pywebview.api;
                    }
                    return null;
                };

                // 添加控制台日志
                const addLog = (message, type = 'info') => {
                    const time = new Date().toLocaleTimeString();
                    consoleLogs.value.push({
                        time,
                        message,
                        type
                    });

                    nextTick(() => {
                        scrollToBottom();
                    });
                };

                // 滚动到底部
                const scrollToBottom = () => {
                    if (consoleContainer.value) {
                        consoleContainer.value.scrollTop = consoleContainer.value.scrollHeight;
                    }
                };

                // 清空控制台
                const clearConsole = () => {
                    consoleLogs.value = [];
                    addLog('控制台已清空', 'info');
                };

                // 获取日志样式类
                const getLogClass = (type) => {
                    const classMap = {
                        'info': 'log-info',
                        'warning': 'log-warning',
                        'error': 'log-error',
                        'success': 'log-success'
                    };
                    return classMap[type] || 'log-info';
                };

                // 检查微信状态
                const checkWechatStatus = async () => {
                    statusChecking.value = true;
                    addLog('正在检查微信连接状态...', 'info');

                    try {
                        const api = getPywebviewApi();
                        if (!api) {
                            // 如果在浏览器环境中，模拟一个成功的响应用于演示
                            if (typeof window !== 'undefined' && !window.pywebview) {
                                addLog('⚠️ 当前在浏览器环境中，无法连接到后端API', 'warning');
                                addLog('💡 请在应用程序中使用此功能', 'info');
                                wechatStatus.value = {
                                    connected: false,
                                    text: '浏览器环境',
                                    class: 'status-warning'
                                };
                                return;
                            }
                            throw new Error('无法连接到后端API');
                        }

                        // 调用后端检查微信状态
                        const result = await api.check_wechat_status();

                        if (result && result.success) {
                            wechatStatus.value = {
                                connected: true,
                                text: '已连接',
                                class: 'status-ready'
                            };
                            addLog('✅ 微信连接正常', 'success');
                        } else {
                            wechatStatus.value = {
                                connected: false,
                                text: '连接失败',
                                class: 'status-error'
                            };
                            addLog('❌ 微信连接失败：' + (result?.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('检查微信状态失败:', error);
                        wechatStatus.value = {
                            connected: false,
                            text: '检查失败',
                            class: 'status-error'
                        };
                        addLog('❌ 检查微信状态失败：' + error.message, 'error');
                    } finally {
                        statusChecking.value = false;
                    }
                };

                // 添加好友
                const addFriend = async () => {
                    if (!addFriendForm.wechatId.trim()) {
                        ElMessage.warning('请输入微信号');
                        return;
                    }

                    isProcessing.value = true;
                    addLog(`开始添加好友：${addFriendForm.wechatId}`, 'info');

                    try {
                        const api = getPywebviewApi();
                        if (!api) {
                            throw new Error('无法连接到后端API');
                        }

                        addLog('正在搜索微信号...', 'info');

                        // 调用后端添加好友接口
                        const result = await api.add_wechat_friend({
                            wechat_id: addFriendForm.wechatId.trim(),
                            verify_msg: addFriendForm.verifyMsg.trim(),
                            remark_name: addFriendForm.remarkName.trim()
                        });

                        if (result && result.success) {
                            addLog('✅ 好友申请发送成功！', 'success');
                            if (result.screenshot_path) {
                                addLog(`📸 截图已保存：${result.screenshot_path}`, 'info');
                            }

                            ElMessage.success('好友申请发送成功！');

                            // 清空表单
                            addFriendForm.wechatId = '';
                            addFriendForm.verifyMsg = '';
                            addFriendForm.remarkName = '';

                        } else {
                            const errorMsg = result?.message || '添加好友失败';
                            addLog('❌ ' + errorMsg, 'error');
                            ElMessage.error(errorMsg);
                        }

                    } catch (error) {
                        console.error('添加好友失败:', error);
                        const errorMsg = '添加好友失败：' + error.message;
                        addLog('❌ ' + errorMsg, 'error');
                        ElMessage.error(errorMsg);
                    } finally {
                        isProcessing.value = false;
                    }
                };

                // 组件挂载时初始化
                onMounted(() => {
                    addLog('微信自动化工具已启动', 'success');
                    addLog('请确保微信PC版已打开并登录', 'info');

                    // 自动检查微信状态
                    setTimeout(() => {
                        checkWechatStatus();
                    }, 1000);
                });

                return {
                    // 表单数据
                    addFriendForm,

                    // 状态
                    isProcessing,
                    statusChecking,
                    wechatStatus,

                    // 控制台
                    consoleLogs,
                    consoleContainer,

                    // 方法
                    addFriend,
                    checkWechatStatus,
                    clearConsole,
                    scrollToBottom,
                    getLogClass
                };
            }
        });

        // 注册所有图标
        Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
