<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

     <!-- Element Plus CSS -->
     <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
     <!-- Element Plus Icons -->
     <script src="//cdn.jsdelivr.net/npm/@element-plus/icons-vue"></script>
     <!-- Animate.css -->
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }

        /* 统一的容器样式 */
        .el-container {
            height: 100vh;
            overflow: hidden;
        }

        /* 侧边栏精致样式 */
        .el-aside {
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        /* Logo区域精致样式 */
        .logo-header {
            background: #ffffff;
            color: #1e293b;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .logo-icon {
            width: 28px;
            height: 28px;
            background: #3b82f6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 菜单精致样式 */
        .el-menu {
            background: transparent !important;
            border: none !important;
            padding: 12px 0;
        }

        .el-menu-item, .el-sub-menu__title {
            margin: 4px 12px;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #1e293b;
            background: white;
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
        }

        .el-menu-item::before, .el-sub-menu__title::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .el-menu-item:hover::before, .el-sub-menu__title:hover::before {
            left: 100%;
        }

        .el-menu-item:hover, .el-sub-menu__title:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
            color: white !important;
            border-color: #3b82f6;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }

        .el-menu-item.is-active {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
            color: white !important;
            font-weight: 600;
            box-shadow: 0 2px 12px rgba(59, 130, 246, 0.3);
            border: 1px solid #3b82f6;
        }

        .el-sub-menu.is-active .el-sub-menu__title {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
            color: white !important;
            font-weight: 600;
            border: 1px solid #3b82f6;
        }

        /* 顶部标题栏精致样式 */
        .header-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .header-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
        }

        /* 按钮精致样式 */
        .header-btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            position: relative;
            overflow: hidden;
        }

        .header-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .header-btn:hover::before {
            width: 100px;
            height: 100px;
        }

        .header-btn:hover {
            background: #f8fafc;
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }



        /* iframe容器 */
        .iframe-container {
            background: #EFEDED;
            overflow: hidden;
        }

        .iframe-container iframe {
            border: none;
        }

        /* 主内容区域 */
        .el-main {
            padding: 0;
            overflow: hidden;
            background: #f9fafb;
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container style="height: 100vh;">
            <!-- 左侧导航栏 -->
            <el-aside width="220px">
                <el-container direction="vertical" style="height: 100%;">
                    <!-- Logo 区域 -->
                    <el-header height="60px" class="logo-header">
                        <div class="logo-content">
                            <div class="logo-icon">
                                <el-icon size="16" color="white"><Monitor /></el-icon>
                            </div>
                            <div style="text-align: left;">
                                <div style="font-size: 16px; font-weight: 600; line-height: 1.2;">
                                    智能办公系统
                                </div>
                                <div style="font-size: 10px; opacity: 0.7; font-weight: 400;">
                                    Smart office System
                                </div>
                            </div>
                        </div>
                    </el-header>

                    <!-- 导航菜单 -->
                    <el-main style="padding: 0;">
                        <el-menu
                            :default-active="activeMenu"
                            @select="handleMenuSelect"
                            unique-opened
                            style="border-right: none;"
                        >
                            <el-menu-item index="home">
                                <el-icon><House /></el-icon>
                                <span>首页</span>
                            </el-menu-item>

                            <el-sub-menu index="chanmama">
                                <template #title>
                                    <el-icon><Notebook /></el-icon>
                                    <span>蝉妈妈</span>
                                </template>
                                <el-menu-item index="chanmama-home">
                                    <el-icon><Connection /></el-icon>
                                    <span>蝉妈妈主页</span>
                                </el-menu-item>
                                <el-menu-item index="chanmama-talent">
                                    <el-icon><User /></el-icon>
                                    <span>达人数据提取</span>
                                </el-menu-item>
                            </el-sub-menu>

                            <el-sub-menu index="wechat">
                                <template #title>
                                    <el-icon><ChatDotRound /></el-icon>
                                    <span>微信自动化</span>
                                </template>
                                <el-menu-item index="wechat-auto">
                                    <el-icon><Robot /></el-icon>
                                    <span>自动化工具</span>
                                </el-menu-item>
                                <el-menu-item index="wechat-phrases">
                                    <el-icon><ChatLineRound /></el-icon>
                                    <span>常用语管理</span>
                                </el-menu-item>
                            </el-sub-menu>
                        </el-menu>
                    </el-main>
                </el-container>
            </el-aside>

            <!-- 右侧主内容区 -->
            <el-container direction="vertical">
                <!-- 顶部标题栏 -->
                <el-header height="60px" class="header-container" style="display: flex; align-items: center; justify-content: space-between; padding: 0 32px;">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);">
                            <el-icon size="20" color="white">
                                <House v-if="activeMenu === 'home'" />
                                <TrendCharts v-else-if="activeMenu === 'chanmama-talent'" />
                                <ChatDotRound v-else-if="activeMenu === 'wechat-auto'" />
                                <ChatLineRound v-else-if="activeMenu === 'wechat-phrases'" />
                                <House v-else />
                            </el-icon>
                        </div>
                        <div>
                            <h2 style="margin: 0; color: #1e293b; font-size: 20px; font-weight: 700;">{{ currentPageTitle }}</h2>
                            <p style="margin: 2px 0 0 0; color: #64748b; font-size: 14px;">{{ currentPageSubtitle }}</p>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="display: flex; align-items: center; gap: 8px; padding: 8px 16px; background: rgba(59, 130, 246, 0.1); border-radius: 20px; margin-right: 8px;">
                            <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; animation: pulse 2s infinite;"></div>
                            <span style="font-size: 12px; color: #059669; font-weight: 500;">系统正常</span>
                        </div>
                        <el-button class="header-btn" circle @click="refreshContent" :loading="pageLoading">
                            <el-icon><Refresh /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Setting /></el-icon>
                        </el-button>
                        <el-button class="header-btn" circle>
                            <el-icon><Bell /></el-icon>
                        </el-button>
                        <el-dropdown>
                            <el-button class="header-btn" circle>
                                <el-icon><User /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>
                                        <el-icon style="margin-right: 8px;"><User /></el-icon>
                                        个人中心
                                    </el-dropdown-item>
                                    <el-dropdown-item>
                                        <el-icon style="margin-right: 8px;"><Setting /></el-icon>
                                        系统设置
                                    </el-dropdown-item>
                                    <el-dropdown-item divided>
                                        <el-icon style="margin-right: 8px;"><CircleClose /></el-icon>
                                        退出登录
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </el-header>

                <!-- 主内容区 -->
                <el-main class="el-main" v-loading="pageLoading" element-loading-text="页面加载中...">
                    <transition name="fade" mode="out-in">
                        <div v-if="activeMenu === 'home'" key="home">
                            <div class="iframe-container" style="height: calc(100vh - 60px); width: 100%;">
                                <iframe
                                    src="http://120.24.188.186/"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                        <div v-else-if="activeMenu === 'chanmama-home'" key="cmmtalent">
                            <div class="iframe-container" style="height: calc(100vh - 60px); width: 100%;">
                                <iframe
                                    src="https://www.chanmama.com/"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                        <div v-else-if="activeMenu === 'chanmama-talent'" key="talent">
                            <div class="iframe-container" style="height: calc(100vh - 60px); width: 100%;">
                                <iframe
                                    src="talent.html"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                        <div v-else-if="activeMenu === 'wechat-auto'" key="wechat">
                            <div class="iframe-container" style="height: calc(100vh - 60px); width: 100%;">
                                <iframe
                                    src="wechatauto.html"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                        <div v-else-if="activeMenu === 'wechat-phrases'" key="phrases">
                            <div class="iframe-container" style="height: calc(100vh - 60px); width: 100%;">
                                <iframe
                                    src="phrases.html"
                                    style="width: 100%; height: 100%;"
                                    @load="onIframeLoad"
                                ></iframe>
                            </div>
                        </div>
                    </transition>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');
                const pageLoading = ref(false);

                const currentPageTitle = computed(() => {
                    const titles = {
                        'home': '首页概览',
                        'chanmama-talent': '达人数据提取',
                        'chanmama-home': '蝉妈妈主页',
                        'wechat-auto': '微信自动化',
                        'wechat-phrases': '常用语管理'
                    };
                    return titles[activeMenu.value] || '首页概览';
                });

                const currentPageSubtitle = computed(() => {
                    const subtitles = {
                        'home': '系统运行状态一览',
                        'chanmama-talent': '蝉妈妈平台数据分析',
                        'chanmama-home': '蝉妈妈平台主页',
                        'wechat-auto': '智能微信营销助手',
                        'wechat-phrases': '微信验证消息模板管理'
                    };
                    return subtitles[activeMenu.value] || '系统运行状态一览';
                });

                const handleMenuSelect = (index) => {
                    pageLoading.value = true;
                    activeMenu.value = index;
                    // 模拟加载时间
                    setTimeout(() => {
                        pageLoading.value = false;
                    }, 300);
                };

                const refreshContent = () => {
                    pageLoading.value = true;
                    setTimeout(() => {
                        pageLoading.value = false;
                        ElMessage.success('刷新完成');
                    }, 500);
                };

                const onIframeLoad = () => {
                    pageLoading.value = false;
                };

                return {
                    activeMenu,
                    pageLoading,
                    currentPageTitle,
                    currentPageSubtitle,
                    handleMenuSelect,
                    refreshContent,
                    onIframeLoad
                };
            }
        });

        // 注册所有图标
            Object.keys(ElementPlusIconsVue).forEach(key => {
            app.component(key, ElementPlusIconsVue[key]);
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>