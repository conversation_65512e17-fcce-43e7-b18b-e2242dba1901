#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化工具测试脚本
"""

from wechat import WeChatAutomation

def test_basic_functionality():
    """测试基本功能"""
    print("="*50)
    print("微信自动化工具测试")
    print("="*50)
    
    try:
        # 创建实例
        wechat_auto = WeChatAutomation()
        print("✅ 微信自动化实例创建成功")
        
        # 测试微信初始化
        if wechat_auto.init_wechat():
            print("✅ 微信客户端初始化成功")
            
            # 测试获取会话信息
            session_info = wechat_auto.get_session_info()
            if "error" not in session_info:
                print("✅ 获取会话信息成功")
            else:
                print(f"⚠️ 获取会话信息失败: {session_info['error']}")
            
            # 测试回复规则加载
            reply_rules = wechat_auto.auto_reply.load_reply_rules()
            if reply_rules:
                print("✅ 回复规则加载成功")
            
            # 测试好友数据加载
            friends_data = wechat_auto.auto_friend.load_friends_data()
            if friends_data:
                print(f"✅ 好友数据加载成功，共 {len(friends_data)} 个好友")
            
            print("\n" + "="*50)
            print("基本功能测试完成！")
            print("建议运行 python wechat.py 进入交互模式")
            print("="*50)
            
        else:
            print("❌ 微信客户端初始化失败")
            print("请确保:")
            print("1. 微信PC版已登录")
            print("2. 微信窗口已打开")
            print("3. wxauto库已正确安装")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_functionality()
