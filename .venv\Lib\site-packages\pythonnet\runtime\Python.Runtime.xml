<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Python.Runtime</name>
    </assembly>
    <members>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:Python.Runtime.AssemblyManager">
            <summary>
            The AssemblyManager maintains information about loaded assemblies
            namespaces and provides an interface for name-based type lookup.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.Initialize">
            <summary>
            Initialization performed on startup of the Python runtime. Here we
            scan all of the currently loaded assemblies to determine exported
            names, and register to be notified of new assembly loads.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.Shutdown">
            <summary>
            Cleanup resources upon shutdown of the Python runtime.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.AssemblyLoadHandler(System.Object,System.AssemblyLoadEventArgs)">
            <summary>
            Event handler for assembly load events. At the time the Python
            runtime loads, we scan the app domain to map the assemblies that
            are loaded at the time. We also have to register this event handler
            so that we can know about assemblies that get loaded after the
            Python runtime is initialized.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.ResolveHandler(System.Object,System.ResolveEventArgs)">
            <summary>
            Event handler for assembly resolve events. This is needed because
            we augment the assembly search path with the PYTHONPATH when we
            load an assembly from Python. Because of that, we need to listen
            for failed loads, because they might be dependencies of something
            we loaded from Python which also needs to be found on PYTHONPATH.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.UpdatePath">
            <summary>
            We __really__ want to avoid using Python objects or APIs when
            probing for assemblies to load, since our ResolveHandler may be
            called in contexts where we don't have the Python GIL and can't
            even safely try to get it without risking a deadlock ;(
            To work around that, we update a managed copy of sys.path (which
            is the main thing we care about) when UpdatePath is called. The
            import hook calls this whenever it knows its about to use the
            assembly manager, which lets us keep up with changes to sys.path
            in a relatively lightweight and low-overhead way.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.FindAssembly(System.Reflection.AssemblyName)">
            <summary>
            Given an assembly name, try to find this assembly file using the
            PYTHONPATH. If not found, return null to indicate implicit load
            using standard load semantics (app base directory then GAC, etc.)
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.FindAssembly(System.String)">
            <summary>
            Given an assembly name, try to find this assembly file using the
            PYTHONPATH. If not found, return null to indicate implicit load
            using standard load semantics (app base directory then GAC, etc.)
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.LoadAssembly(System.Reflection.AssemblyName)">
            <summary>
            Loads an assembly from the application directory or the GAC
            given its name. Returns the assembly if loaded.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.LoadAssemblyPath(System.String)">
            <summary>
            Loads an assembly using an augmented search path (the python path).
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.LoadAssemblyFullPath(System.String)">
            <summary>
            Loads an assembly using full path.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.FindLoadedAssembly(System.String)">
            <summary>
            Returns an assembly that's already been loaded
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.ScanAssembly(System.Reflection.Assembly)">
            <summary>
            Scans an assembly for exported namespaces, adding them to the
            mapping of valid namespaces. Note that for a given namespace
            a.b.c.d, each of a, a.b, a.b.c and a.b.c.d are considered to
            be valid namespaces (to better match Python import semantics).
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.IsValidNamespace(System.String)">
            <summary>
            Returns true if the given qualified name matches a namespace
            exported by an assembly loaded in the current app domain.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.GetNamespaces">
            <summary>
            Returns an enumerable collection containing the namepsaces exported
            by loaded assemblies in the current app domain.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.GetAssemblies(System.String)">
            <summary>
            Returns list of assemblies that declare types in a given namespace
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.GetNames(System.String)">
            <summary>
            Returns the current list of valid names for the input namespace.
            </summary>
        </member>
        <member name="M:Python.Runtime.AssemblyManager.LookupTypes(System.String)">
            <summary>
            Returns the <see cref="T:System.Type"/> objects for the given qualified name,
            looking in the currently loaded assemblies for the named
            type.
            </summary>
        </member>
        <member name="T:Python.Runtime.ClassManager">
            <summary>
            The ClassManager is responsible for creating and managing instances
            that implement the Python type objects that reflect managed classes.
            Each managed type reflected to Python is represented by an instance
            of a concrete subclass of ClassBase. Each instance is associated with
            a generated Python type object, whose slots point to static methods
            of the managed instance's class.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassManager.GetClass(System.Type)">
            <summary>
            Return the ClassBase-derived instance that implements a particular
            reflected managed type, creating it if it doesn't yet exist.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassManager.CreateClass(System.Type)">
            <summary>
            Create a new ClassBase-derived instance that implements a reflected
            managed type. The new object will be associated with a generated
            Python type object.
            </summary>
        </member>
        <member name="T:Python.Runtime.ClassManager.ClassInfo">
            <summary>
            This class owns references to PyObjects in the `members` member.
            The caller has responsibility to DECREF them.
            </summary>
        </member>
        <member name="T:Python.Runtime.Codecs.DecoderGroup">
            <summary>
            Represents a group of <see cref="T:Python.Runtime.IPyObjectDecoder"/>s. Useful to group them by priority.
            </summary>
        </member>
        <member name="M:Python.Runtime.Codecs.DecoderGroup.Add(Python.Runtime.IPyObjectDecoder)">
            <summary>
            Add specified decoder to the group
            </summary>
        </member>
        <member name="M:Python.Runtime.Codecs.DecoderGroup.Clear">
            <summary>
            Remove all decoders from the group
            </summary>
        </member>
        <member name="M:Python.Runtime.Codecs.DecoderGroup.CanDecode(Python.Runtime.PyType,System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Python.Runtime.Codecs.DecoderGroup.TryDecode``1(Python.Runtime.PyObject,``0@)">
            <inheritdoc />
        </member>
        <member name="M:Python.Runtime.Codecs.DecoderGroup.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Python.Runtime.Codecs.DecoderGroupExtensions.GetDecoder(Python.Runtime.IPyObjectDecoder,Python.Runtime.PyType,System.Type)">
            <summary>
            Gets a concrete instance of <see cref="T:Python.Runtime.IPyObjectDecoder"/>
            (potentially selecting one from a collection),
            that can decode from <paramref name="objectType"/> to <paramref name="targetType"/>,
            or <c>null</c> if a matching decoder can not be found.
            </summary>
        </member>
        <member name="T:Python.Runtime.Codecs.EncoderGroup">
            <summary>
            Represents a group of <see cref="T:Python.Runtime.IPyObjectDecoder"/>s. Useful to group them by priority.
            </summary>
        </member>
        <member name="M:Python.Runtime.Codecs.EncoderGroup.Add(Python.Runtime.IPyObjectEncoder)">
            <summary>
            Add specified encoder to the group
            </summary>
        </member>
        <member name="M:Python.Runtime.Codecs.EncoderGroup.Clear">
            <summary>
            Remove all encoders from the group
            </summary>
        </member>
        <member name="M:Python.Runtime.Codecs.EncoderGroup.CanEncode(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Python.Runtime.Codecs.EncoderGroup.TryEncode(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Python.Runtime.Codecs.EncoderGroup.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Python.Runtime.Codecs.EncoderGroupExtensions.GetEncoders(Python.Runtime.IPyObjectEncoder,System.Type)">
            <summary>
            Gets specific instances of <see cref="T:Python.Runtime.IPyObjectEncoder"/>
            (potentially selecting one from a collection),
            that can encode the specified <paramref name="type"/>.
            </summary>
        </member>
        <member name="T:Python.Runtime.Codecs.RawProxyEncoder">
            <summary>
            A .NET object encoder, that returns raw proxies (e.g. no conversion to Python types).
            <para>You must inherit from this class and override <see cref="M:Python.Runtime.Codecs.RawProxyEncoder.CanEncode(System.Type)"/>.</para>
            </summary>
        </member>
        <member name="T:Python.Runtime.IPyObjectDecoder">
            <summary>
            Defines <see cref="T:Python.Runtime.PyObject"/> conversion to CLR types (unmarshalling)
            </summary>
        </member>
        <member name="M:Python.Runtime.IPyObjectDecoder.CanDecode(Python.Runtime.PyType,System.Type)">
            <summary>
            Checks if this decoder can decode from <paramref name="objectType"/> to <paramref name="targetType"/>
            </summary>
        </member>
        <member name="M:Python.Runtime.IPyObjectDecoder.TryDecode``1(Python.Runtime.PyObject,``0@)">
            <summary>
            Attempts do decode <paramref name="pyObj"/> into a variable of specified type
            </summary>
            <typeparam name="T">CLR type to decode into</typeparam>
            <param name="pyObj">Object to decode</param>
            <param name="value">The variable, that will receive decoding result</param>
            <returns></returns>
        </member>
        <member name="T:Python.Runtime.IPyObjectEncoder">
            <summary>
            Defines conversion from CLR objects into Python objects (e.g. <see cref="T:Python.Runtime.PyObject"/>) (marshalling)
            </summary>
        </member>
        <member name="M:Python.Runtime.IPyObjectEncoder.CanEncode(System.Type)">
            <summary>
            Checks if encoder can encode CLR objects of specified type
            </summary>
        </member>
        <member name="M:Python.Runtime.IPyObjectEncoder.TryEncode(System.Object)">
            <summary>
            Attempts to encode CLR object <paramref name="value"/> into Python object
            </summary>
        </member>
        <member name="T:Python.Runtime.PyObjectConversions">
            <summary>
            This class allows to register additional marshalling codecs.
            <para>Python.NET will pick suitable encoder/decoder registered first</para>
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObjectConversions.RegisterEncoder(Python.Runtime.IPyObjectEncoder)">
            <summary>
            Registers specified encoder (marshaller)
            <para>Python.NET will pick suitable encoder/decoder registered first</para>
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObjectConversions.RegisterDecoder(Python.Runtime.IPyObjectDecoder)">
            <summary>
            Registers specified decoder (unmarshaller)
            <para>Python.NET will pick suitable encoder/decoder registered first</para>
            </summary>
        </member>
        <member name="T:Python.Runtime.Converter">
            <summary>
            Performs data conversions between managed types and Python types.
            </summary>
        </member>
        <member name="M:Python.Runtime.Converter.GetTypeByAlias(Python.Runtime.BorrowedReference)">
            <summary>
            Given a builtin Python type, return the corresponding CLR type.
            </summary>
        </member>
        <member name="M:Python.Runtime.Converter.ToPythonImplicit(System.Object)">
            <summary>
            In a few situations, we don't have any advisory type information
            when we want to convert an object to Python.
            </summary>
        </member>
        <member name="M:Python.Runtime.Converter.ToManaged(Python.Runtime.BorrowedReference,System.Type,System.Object@,System.Boolean)">
            <summary>
            Return a managed object for the given Python object, taking funny
            byref types into account.
            </summary>
            <param name="value">A Python object</param>
            <param name="type">The desired managed type</param>
            <param name="result">Receives the managed object</param>
            <param name="setError">If true, call <c>Exceptions.SetError</c> with the reason for failure.</param>
            <returns>True on success</returns>
        </member>
        <member name="M:Python.Runtime.Converter.ToManagedExplicit(Python.Runtime.BorrowedReference,System.Type,System.Object@)">
            <remarks>
            Unlike <see cref="M:Python.Runtime.Converter.ToManaged(Python.Runtime.BorrowedReference,System.Type,System.Object@,System.Boolean)"/>,
            this method does not have a <c>setError</c> parameter, because it should
            only be called after <see cref="M:Python.Runtime.Converter.ToManaged(Python.Runtime.BorrowedReference,System.Type,System.Object@,System.Boolean)"/>.
            </remarks>
        </member>
        <member name="M:Python.Runtime.Converter.ToPrimitive(Python.Runtime.BorrowedReference,System.Type,System.Object@,System.Boolean)">
            <summary>
            Convert a Python value to an instance of a primitive managed type.
            </summary>
        </member>
        <member name="M:Python.Runtime.Converter.ToArray(Python.Runtime.BorrowedReference,System.Type,System.Object@,System.Boolean)">
            <summary>
            Convert a Python value to a correctly typed managed array instance.
            The Python value must support the Python iterator protocol or and the
            items in the sequence must be convertible to the target array type.
            </summary>
        </member>
        <member name="T:Python.Runtime.DefaultBaseTypeProvider">
            <summary>Minimal Python base type provider</summary>
        </member>
        <member name="T:Python.Runtime.DelegateManager">
            <summary>
            The DelegateManager class manages the creation of true managed
            delegate instances that dispatch calls to Python methods.
            </summary>
        </member>
        <member name="M:Python.Runtime.DelegateManager.GetDispatcher(System.Type)">
            <summary>
            GetDispatcher is responsible for creating a class that provides
            an appropriate managed callback method for a given delegate type.
            </summary>
        </member>
        <member name="M:Python.Runtime.DelegateManager.GetDelegate(System.Type,Python.Runtime.PyObject)">
            <summary>
            Given a delegate type and a callable Python object, GetDelegate
            returns an instance of the delegate type. The delegate instance
            returned will dispatch calls to the given Python object.
            </summary>
        </member>
        <member name="T:Python.Runtime.Exceptions">
            <summary>
            Encapsulates the Python exception APIs.
            </summary>
            <remarks>
            Readability of the Exceptions class improvements as we look toward version 2.7 ...
            </remarks>
        </member>
        <member name="M:Python.Runtime.Exceptions.Initialize">
            <summary>
            Initialization performed on startup of the Python runtime.
            </summary>
        </member>
        <member name="M:Python.Runtime.Exceptions.Shutdown">
            <summary>
            Cleanup resources upon shutdown of the Python runtime.
            </summary>
        </member>
        <member name="M:Python.Runtime.Exceptions.SetArgsAndCause(Python.Runtime.BorrowedReference,System.Exception)">
            <summary>
            Set the 'args' slot on a python exception object that wraps
            a CLR exception. This is needed for pickling CLR exceptions as
            BaseException_reduce will only check the slots, bypassing the
            __getattr__ implementation, and thus dereferencing a NULL
            pointer.
            </summary>
        </member>
        <member name="M:Python.Runtime.Exceptions.ErrorCheck(Python.Runtime.BorrowedReference)">
            <summary>
            Shortcut for (pointer == NULL) -&gt; throw PythonException
            </summary>
            <param name="pointer">Pointer to a Python object</param>
        </member>
        <member name="M:Python.Runtime.Exceptions.ErrorOccurredCheck(System.IntPtr)">
            <summary>
            Shortcut for (pointer == NULL or ErrorOccurred()) -&gt; throw PythonException
            </summary>
        </member>
        <member name="M:Python.Runtime.Exceptions.ExceptionMatches(Python.Runtime.BorrowedReference)">
            <summary>
            ExceptionMatches Method
            </summary>
            <remarks>
            Returns true if the current Python exception matches the given
            Python object. This is a wrapper for PyErr_ExceptionMatches.
            </remarks>
        </member>
        <member name="M:Python.Runtime.Exceptions.SetError(Python.Runtime.BorrowedReference,System.String)">
            <summary>
            Sets the current Python exception given a native string.
            This is a wrapper for the Python PyErr_SetString call.
            </summary>
        </member>
        <member name="M:Python.Runtime.Exceptions.SetError(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            SetError Method
            </summary>
            <remarks>
            Sets the current Python exception given a Python object.
            This is a wrapper for the Python PyErr_SetObject call.
            </remarks>
        </member>
        <member name="M:Python.Runtime.Exceptions.SetError(System.Exception)">
            <summary>
            SetError Method
            </summary>
            <remarks>
            Sets the current Python exception given a CLR exception
            object. The CLR exception instance is wrapped as a Python
            object, allowing it to be handled naturally from Python.
            </remarks>
        </member>
        <member name="M:Python.Runtime.Exceptions.SetCause(System.Exception)">
            <summary>
            When called after SetError, sets the cause of the error.
            </summary>
            <param name="cause">The cause of the current error</param>
        </member>
        <member name="M:Python.Runtime.Exceptions.ErrorOccurred">
            <summary>
            ErrorOccurred Method
            </summary>
            <remarks>
            Returns true if an exception occurred in the Python runtime.
            This is a wrapper for the Python PyErr_Occurred call.
            </remarks>
        </member>
        <member name="M:Python.Runtime.Exceptions.Clear">
            <summary>
            Clear Method
            </summary>
            <remarks>
            Clear any exception that has been set in the Python runtime.
            </remarks>
        </member>
        <member name="M:Python.Runtime.Exceptions.warn(System.String,Python.Runtime.BorrowedReference,System.Int32)">
            <summary>
            Alias for Python's warnings.warn() function.
            </summary>
        </member>
        <member name="M:Python.Runtime.Exceptions.RaiseTypeError(System.String)">
            <summary>
            Raises a <see cref="F:Python.Runtime.Exceptions.TypeError"/> and attaches any existing exception as its cause.
            </summary>
            <param name="message">The exception message</param>
            <returns><c>null</c></returns>
        </member>
        <member name="M:Python.Runtime.FinalizationException.GetObject">
             <summary>
             Gets the object, whose finalization failed.
            
             <para>If this function crashes, you can also try <see cref="M:Python.Runtime.FinalizationException.DebugGetObject"/>,
             which does not attempt to increase the object reference count.</para>
             </summary>
        </member>
        <member name="M:Python.Runtime.FinalizationException.DebugGetObject">
            <summary>
            Gets the object, whose finalization failed without incrementing
            its reference count. This should only ever be called during debugging.
            When the result is disposed or finalized, the program will crash.
            </summary>
        </member>
        <member name="T:Python.Runtime.ImportHook">
            <summary>
            Implements the "import hook" used to integrate Python with the CLR.
            </summary>
        </member>
        <member name="M:Python.Runtime.ImportHook.Initialize">
            <summary>
            Initialization performed on startup of the Python runtime.
            </summary>
        </member>
        <member name="M:Python.Runtime.ImportHook.Shutdown">
            <summary>
            Cleanup resources upon shutdown of the Python runtime.
            </summary>
        </member>
        <member name="M:Python.Runtime.ImportHook.SetupNamespaceTracking">
            <summary>
            Sets up the tracking of loaded namespaces. This makes available to 
            Python, as a Python object, the loaded namespaces. The set of loaded
            namespaces is used during the import to verify if we can import a 
            CLR assembly as a module or not. The set is stored on the clr module.
            </summary>
        </member>
        <member name="M:Python.Runtime.ImportHook.TeardownNameSpaceTracking">
            <summary>
            Removes the set of available namespaces from the clr module.
            </summary>
        </member>
        <member name="M:Python.Runtime.ImportHook.UpdateCLRModuleDict">
            <summary>
            Because we use a proxy module for the clr module, we somtimes need
            to force the py_clr_module to sync with the actual clr module's dict.
            </summary>
        </member>
        <member name="M:Python.Runtime.ImportHook.GetCLRModule">
            <summary>
            Return the clr python module (new reference)
            </summary>
        </member>
        <member name="M:Python.Runtime.ImportHook.Import(System.String)">
            <summary>
            The hook to import a CLR module into Python. Returns a new reference
            to the module.
            </summary>
        </member>
        <member name="T:Python.Runtime.IReflectedType">
            <summary>
            xxx
            </summary>
        </member>
        <member name="T:Python.Runtime.DocStringAttribute">
            <summary>
            This file defines objects to support binary interop with the Python
            runtime. Generally, the definitions here need to be kept up to date
            when moving to new Python versions.
            </summary>
        </member>
        <member name="T:Python.Runtime.TypeFlags">
            <summary>
            TypeFlags(): The actual bit values for the Type Flags stored
            in a class.
            Note that the two values reserved for stackless have been put
            to good use as PythonNet specific flags (Managed and Subclass)
            </summary>
        </member>
        <member name="F:Python.Runtime.TypeFlags.HasClrInstance">
            <remarks>PythonNet specific</remarks>
        </member>
        <member name="F:Python.Runtime.TypeFlags.Subclass">
            <remarks>PythonNet specific</remarks>
        </member>
        <member name="P:Python.Runtime.InteropConfiguration.PythonBaseTypeProviders">
            <summary>Enables replacing base types of CLR types as seen from Python</summary>
        </member>
        <member name="M:Python.Runtime.IPythonBaseTypeProvider.GetBaseTypes(System.Type,System.Collections.Generic.IList{Python.Runtime.PyType})">
            <summary>
            Get Python types, that should be presented to Python as the base types
            for the specified .NET type.
            </summary>
        </member>
        <member name="T:Python.Runtime.MethodBinder">
            <summary>
            A MethodBinder encapsulates information about a (possibly overloaded)
            managed method, and is responsible for selecting the right method given
            a set of Python arguments. This is also used as a base class for the
            ConstructorBinder, a minor variation used to invoke constructors.
            </summary>
        </member>
        <member name="F:Python.Runtime.MethodBinder.list">
            <summary>
            The overloads of this method
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinder.MatchSignature(System.Reflection.MethodBase[],System.Type[])">
            <summary>
            Given a sequence of MethodInfo and a sequence of types, return the
            MethodInfo that matches the signature represented by those types.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinder.MatchParameters(System.Reflection.MethodBase[],System.Type[])">
            <summary>
            Given a sequence of MethodInfo and a sequence of type parameters,
            return the MethodInfo(s) that represents the matching closed generic.
            If unsuccessful, returns null and may set a Python error.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinder.MatchSignatureAndParameters(System.Reflection.MethodBase[],System.Type[],System.Type[])">
            <summary>
            Given a sequence of MethodInfo and two sequences of type parameters,
            return the MethodInfo that matches the signature and the closed generic.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinder.GetMethods">
            <summary>
            Return the array of MethodInfo for this method. The result array
            is arranged in order of precedence (done lazily to avoid doing it
            at all for methods that are never called).
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinder.GetPrecedence(System.Reflection.MethodBase)">
            <summary>
            Precedence algorithm largely lifted from Jython - the concerns are
            generally the same so we'll start with this and tweak as necessary.
            </summary>
            <remarks>
            Based from Jython `org.python.core.ReflectedArgs.precedence`
            See: https://github.com/jythontools/jython/blob/master/src/org/python/core/ReflectedArgs.java#L192
            </remarks>
        </member>
        <member name="M:Python.Runtime.MethodBinder.ArgPrecedence(System.Type)">
            <summary>
            Return a precedence value for a particular Type object.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinder.Bind(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Bind the given Python instance and arguments to a particular method
            overload in <see cref="F:Python.Runtime.MethodBinder.list"/> and return a structure that contains the converted Python
            instance, converted arguments and the correct method to call.
            If unsuccessful, may set a Python error.
            </summary>
            <param name="inst">The Python target of the method invocation.</param>
            <param name="args">The Python arguments.</param>
            <param name="kw">The Python keyword arguments.</param>
            <returns>A Binding if successful.  Otherwise null.</returns>
        </member>
        <member name="M:Python.Runtime.MethodBinder.Bind(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,System.Reflection.MethodBase)">
            <summary>
            Bind the given Python instance and arguments to a particular method
            overload in <see cref="F:Python.Runtime.MethodBinder.list"/> and return a structure that contains the converted Python
            instance, converted arguments and the correct method to call.
            If unsuccessful, may set a Python error.
            </summary>
            <param name="inst">The Python target of the method invocation.</param>
            <param name="args">The Python arguments.</param>
            <param name="kw">The Python keyword arguments.</param>
            <param name="info">If not null, only bind to that method.</param>
            <returns>A Binding if successful.  Otherwise null.</returns>
        </member>
        <member name="M:Python.Runtime.MethodBinder.Bind(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,System.Reflection.MethodBase,System.Reflection.MethodBase[])">
            <summary>
            Bind the given Python instance and arguments to a particular method
            overload in <see cref="F:Python.Runtime.MethodBinder.list"/> and return a structure that contains the converted Python
            instance, converted arguments and the correct method to call.
            If unsuccessful, may set a Python error.
            </summary>
            <param name="inst">The Python target of the method invocation.</param>
            <param name="args">The Python arguments.</param>
            <param name="kw">The Python keyword arguments.</param>
            <param name="info">If not null, only bind to that method.</param>
            <param name="methodinfo">If not null, additionally attempt to bind to the generic methods in this array by inferring generic type parameters.</param>
            <returns>A Binding if successful.  Otherwise null.</returns>
        </member>
        <member name="M:Python.Runtime.MethodBinder.TryConvertArguments(System.Reflection.ParameterInfo[],System.Boolean,Python.Runtime.BorrowedReference,System.Int32,System.Collections.Generic.Dictionary{System.String,Python.Runtime.PyObject},System.Collections.ArrayList,System.Int32@)">
            <summary>
            Attempts to convert Python positional argument tuple and keyword argument table
            into an array of managed objects, that can be passed to a method.
            If unsuccessful, returns null and may set a Python error.
            </summary>
            <param name="pi">Information about expected parameters</param>
            <param name="paramsArray"><c>true</c>, if the last parameter is a params array.</param>
            <param name="args">A pointer to the Python argument tuple</param>
            <param name="pyArgCount">Number of arguments, passed by Python</param>
            <param name="kwargDict">Dictionary of keyword argument name to python object pointer</param>
            <param name="defaultArgList">A list of default values for omitted parameters</param>
            <param name="outs">Returns number of output parameters</param>
            <returns>If successful, an array of .NET arguments that can be passed to the method.  Otherwise null.</returns>
        </member>
        <member name="M:Python.Runtime.MethodBinder.TryConvertArgument(Python.Runtime.BorrowedReference,System.Type,System.Object@,System.Boolean@)">
            <summary>
            Try to convert a Python argument object to a managed CLR type.
            If unsuccessful, may set a Python error.
            </summary>
            <param name="op">Pointer to the Python argument object.</param>
            <param name="parameterType">That parameter's managed type.</param>
            <param name="arg">Converted argument.</param>
            <param name="isOut">Whether the CLR type is passed by reference.</param>
            <returns>true on success</returns>
        </member>
        <member name="M:Python.Runtime.MethodBinder.TryComputeClrArgumentType(System.Type,Python.Runtime.BorrowedReference)">
            <summary>
            Determine the managed type that a Python argument object needs to be converted into.
            </summary>
            <param name="parameterType">The parameter's managed type.</param>
            <param name="argument">Pointer to the Python argument object.</param>
            <returns>null if conversion is not possible</returns>
        </member>
        <member name="M:Python.Runtime.MethodBinder.MatchesArgumentCount(System.Int32,System.Reflection.ParameterInfo[],System.Collections.Generic.Dictionary{System.String,Python.Runtime.PyObject},System.Boolean@,System.Collections.ArrayList@,System.Int32@,System.Int32@)">
            <summary>
            Check whether the number of Python and .NET arguments match, and compute additional arg information.
            </summary>
            <param name="positionalArgumentCount">Number of positional args passed from Python.</param>
            <param name="parameters">Parameters of the specified .NET method.</param>
            <param name="kwargDict">Keyword args passed from Python.</param>
            <param name="paramsArray">True if the final param of the .NET method is an array (`params` keyword).</param>
            <param name="defaultArgList">List of default values for arguments.</param>
            <param name="kwargsMatched">Number of kwargs from Python that are also present in the .NET method.</param>
            <param name="defaultsNeeded">Number of non-null defaultsArgs.</param>
            <returns></returns>
        </member>
        <member name="T:Python.Runtime.MethodSorter">
            <summary>
            Utility class to sort method info by parameter type precedence.
            </summary>
        </member>
        <member name="T:Python.Runtime.Binding">
            <summary>
            A Binding is a utility instance that bundles together a MethodInfo
            representing a method to call, a (possibly null) target instance for
            the call, and the arguments for the call (all as managed values).
            </summary>
        </member>
        <member name="T:Python.Runtime.Native.NativeFunc">
            <summary>Catch-all type for native function objects (to be pointed to)</summary>
        </member>
        <member name="T:Python.Runtime.Native.PyGILState">
            <remarks><c>PyGILState_STATE</c></remarks>
        </member>
        <member name="F:Python.Runtime.Native.PyMemberType.Char">
            <summary>1-character string</summary>
        </member>
        <member name="F:Python.Runtime.Native.PyMemberType.Byte">
            <summary>8-bit signed int</summary>
        </member>
        <member name="F:Python.Runtime.Native.PyMemberType.Bool">
            <summary>bools contained in the structure (assumed char)</summary>
        </member>
        <member name="F:Python.Runtime.Native.PyMemberType.ObjectEx">
            <summary>
            Like <see cref="F:Python.Runtime.Native.PyMemberType.Object"/>but raises AttributeError
            when the value is NULL, instead of converting to None
            </summary>
        </member>
        <member name="F:Python.Runtime.Native.PyMethodFlags.Coexist">
            <summary>
            Allows a method to be entered even though a slot has
            already filled the entry.  When defined, the flag allows a separate
            method, "__contains__" for example, to coexist with a defined
            slot like sq_contains.
            </summary>
        </member>
        <member name="F:Python.Runtime.Native.PyMethodFlags.FastCall">
            <remarks>3.10+</remarks>
        </member>
        <member name="F:Python.Runtime.Native.PyMethodFlags.Method">
            <summary>
            The function stores an
            additional reference to the class that defines it;
            both self and class are passed to it.
            It uses PyCMethodObject instead of PyCFunctionObject.
            May not be combined with METH_NOARGS, METH_O, METH_CLASS or METH_STATIC.
            </summary>
            <remarks>3.9+</remarks>
        </member>
        <member name="T:Python.Runtime.BorrowedReference">
            <summary>
            Represents a reference to a Python object, that is being lent, and
            can only be safely used until execution returns to the caller.
            </summary>
        </member>
        <member name="M:Python.Runtime.BorrowedReference.DangerousGetAddress">
            <summary>Gets a raw pointer to the Python object</summary>
        </member>
        <member name="M:Python.Runtime.BorrowedReference.DangerousGetAddressOrNull">
            <summary>Gets a raw pointer to the Python object</summary>
        </member>
        <member name="M:Python.Runtime.BorrowedReference.#ctor(System.IntPtr)">
            <summary>
            Creates new instance of <see cref="T:Python.Runtime.BorrowedReference"/> from raw pointer. Unsafe.
            </summary>
        </member>
        <member name="T:Python.Runtime.MarshalerBase">
            <summary>
            Abstract class defining boiler plate methods that
            Custom Marshalers will use.
            </summary>
        </member>
        <member name="T:Python.Runtime.UcsMarshaler">
            <summary>
            Custom Marshaler to deal with Managed String to Native
            conversion differences on UCS2/UCS4.
            </summary>
        </member>
        <member name="M:Python.Runtime.UcsMarshaler.Py3UnicodePy2StringtoPtr(System.String)">
            <summary>
            Utility function for Marshaling Unicode on PY3 and AnsiStr on PY2.
            Use on functions whose Input signatures changed between PY2/PY3.
            Ex. Py_SetPythonHome
            </summary>
            <param name="s">Managed String</param>
            <returns>
            Ptr to Native String ANSI(PY2)/Unicode(PY3/UCS2)/UTF32(PY3/UCS4.
            </returns>
            <remarks>
            You MUST deallocate the IntPtr of the Return when done with it.
            </remarks>
        </member>
        <member name="M:Python.Runtime.UcsMarshaler.PtrToPy3UnicodePy2String(System.IntPtr)">
            <summary>
            Utility function for Marshaling Unicode IntPtr on PY3 and
            AnsiStr IntPtr on PY2 to Managed Strings. Use on Python functions
            whose return type changed between PY2/PY3.
            Ex. Py_GetPythonHome
            </summary>
            <param name="p">Native Ansi/Unicode/UTF32 String</param>
            <returns>
            Managed String
            </returns>
        </member>
        <member name="T:Python.Runtime.StrArrayMarshaler">
            <summary>
            Custom Marshaler to deal with Managed String Arrays to Native
            conversion differences on UCS2/UCS4.
            </summary>
        </member>
        <member name="T:Python.Runtime.NativeCall">
            <summary>
            Provides support for calling native code indirectly through
            function pointers. Most of the important parts of the Python
            C API can just be wrapped with p/invoke, but there are some
            situations (specifically, calling functions through Python
            type structures) where we need to call functions indirectly.
            </summary>
        </member>
        <member name="T:Python.Runtime.NewReference">
            <summary>
            Represents a reference to a Python object, that is tracked by Python's reference counting.
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReference.#ctor(Python.Runtime.BorrowedReference,System.Boolean)">
            <summary>Creates a <see cref="T:Python.Runtime.NewReference"/> pointing to the same object</summary>
        </member>
        <member name="M:Python.Runtime.NewReference.#ctor(Python.Runtime.NewReference@,System.Boolean)">
            <summary>Creates a <see cref="T:Python.Runtime.NewReference"/> pointing to the same object</summary>
        </member>
        <member name="M:Python.Runtime.NewReference.MoveToPyObject">
            <summary>
            Returns <see cref="T:Python.Runtime.PyObject"/> wrapper around this reference, which now owns
            the pointer. Sets the original reference to <c>null</c>, as it no longer owns it.
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReference.Move">
            <summary>
            Creates new instance of <see cref="T:Python.Runtime.NewReference"/> which now owns the pointer.
            Sets the original reference to <c>null</c>, as it no longer owns the pointer.
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReference.DangerousMoveToPointer">
            <summary>Moves ownership of this instance to unmanged pointer</summary>
        </member>
        <member name="M:Python.Runtime.NewReference.DangerousMoveToPointerOrNull">
            <summary>Moves ownership of this instance to unmanged pointer</summary>
        </member>
        <member name="M:Python.Runtime.NewReference.MoveToPyObjectOrNull">
            <summary>
            Returns <see cref="T:Python.Runtime.PyObject"/> wrapper around this reference, which now owns
            the pointer. Sets the original reference to <c>null</c>, as it no longer owns it.
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReference.StealNullable">
            <summary>
            Call this method to move ownership of this reference to a Python C API function,
            that steals reference passed to it.
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReference.Steal">
            <summary>
            Call this method to move ownership of this reference to a Python C API function,
            that steals reference passed to it.
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReference.Dispose">
            <summary>
            Removes this reference to a Python object, and sets it to <c>null</c>.
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReference.DangerousFromPointer(System.IntPtr)">
            <summary>
            Creates <see cref="T:Python.Runtime.NewReference"/> from a raw pointer
            </summary>
        </member>
        <member name="T:Python.Runtime.NewReferenceExtensions">
            <summary>
            These members can not be directly in <see cref="T:Python.Runtime.NewReference"/> type,
            because <c>this</c> is always passed by value, which we need to avoid.
            (note <code>this in NewReference</code> vs the usual <code>this NewReference</code>)
            </summary>
        </member>
        <member name="M:Python.Runtime.NewReferenceExtensions.DangerousGetAddress(Python.Runtime.NewReference@)">
            <summary>Gets a raw pointer to the Python object</summary>
        </member>
        <member name="F:Python.Runtime.Py_buffer.len">
            <summary>Buffer size in bytes</summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.SIMPLE">
            <summary>
            Simple buffer without shape strides and suboffsets
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.WRITABLE">
            <summary>
            Controls the <see cref="P:Python.Runtime.PyBuffer.ReadOnly"/> field. If set, the exporter MUST provide a writable buffer or else report failure. Otherwise, the exporter MAY provide either a read-only or writable buffer, but the choice MUST be consistent for all consumers.
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.FORMATS">
            <summary>
            Controls the <see cref="P:Python.Runtime.PyBuffer.Format"/> field. If set, this field MUST be filled in correctly. Otherwise, this field MUST be NULL.
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.ND">
            <summary>
            N-Dimensional buffer with shape
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.STRIDES">
            <summary>
            Buffer with strides and shape
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.C_CONTIGUOUS">
            <summary>
            C-Contigous buffer with strides and shape
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.F_CONTIGUOUS">
            <summary>
            F-Contigous buffer with strides and shape
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.ANY_CONTIGUOUS">
            <summary>
            C or Fortran contigous buffer with strides and shape
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.INDIRECT">
            <summary>
            Buffer with suboffsets (if needed)
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.CONTIG">
            <summary>
            Writable C-Contigous buffer with shape
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.CONTIG_RO">
            <summary>
            Readonly C-Contigous buffer with shape
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.STRIDED">
            <summary>
            Writable buffer with shape and strides
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.STRIDED_RO">
            <summary>
            Readonly buffer with shape and strides
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.RECORDS">
            <summary>
            Writable buffer with shape, strides and format
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.RECORDS_RO">
            <summary>
            Readonly buffer with shape, strides and format
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.FULL">
            <summary>
            Writable indirect buffer with shape, strides, format and suboffsets (if needed)
            </summary>
        </member>
        <member name="F:Python.Runtime.PyBUF.FULL_RO">
            <summary>
            Readonly indirect buffer with shape, strides, format and suboffsets (if needed)
            </summary>
        </member>
        <member name="M:Python.Runtime.ReferenceExtensions.IsNone(Python.Runtime.NewReference@)">
            <summary>
            Checks if the reference points to Python object <c>None</c>.
            </summary>
        </member>
        <member name="M:Python.Runtime.ReferenceExtensions.IsNone(Python.Runtime.BorrowedReference)">
            <summary>
            Checks if the reference points to Python object <c>None</c>.
            </summary>
        </member>
        <member name="T:Python.Runtime.StolenReference">
            <summary>
            Should only be used for the arguments of Python C API functions, that steal references,
            and internal <see cref="T:Python.Runtime.PyObject"/> constructors.
            </summary>
        </member>
        <member name="M:Python.Runtime.Py.Import(System.String)">
            <summary>
            Given a module or package name, import the module and return the resulting object.
            </summary>
            <param name="name">Fully-qualified module or package name</param>
        </member>
        <member name="T:Python.Runtime.PyExportAttribute">
            <summary>
            Controls visibility to Python for public .NET type or an entire assembly
            </summary>
        </member>
        <member name="T:Python.Runtime.PythonEngine">
            <summary>
            This class provides the public interface of the Python runtime.
            </summary>
        </member>
        <member name="P:Python.Runtime.PythonEngine.DebugGIL">
            <summary>Set to <c>true</c> to enable GIL debugging assistance.</summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.SetNoSiteFlag">
            <summary>
            Set the NoSiteFlag to disable loading the site module.
            Must be called before Initialize.
            https://docs.python.org/3/c-api/init.html#c.Py_NoSiteFlag
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.Initialize(System.Collections.Generic.IEnumerable{System.String},System.Boolean,System.Boolean)">
            <summary>
            Initialize Method
            </summary>
            <remarks>
            Initialize the Python runtime. It is safe to call this method
            more than once, though initialization will only happen on the
            first call. It is *not* necessary to hold the Python global
            interpreter lock (GIL) to call this method.
            initSigs can be set to 1 to do default python signal configuration. This will override the way signals are handled by the application.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.InitExt">
            <summary>
            A helper to perform initialization from the context of an active
            CPython interpreter process - this bootstraps the managed runtime
            when it is imported by the CLR extension module.
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.Shutdown">
            <summary>
            Shutdown and release resources held by the Python runtime. The
            Python runtime can no longer be used in the current process
            after calling the Shutdown method.
            </summary>
        </member>
        <member name="T:Python.Runtime.PythonEngine.ShutdownHandler">
             <summary>
             Called when the engine is shut down.
            
             Shutdown handlers are run in reverse order they were added, so that
             resources available when running a shutdown handler are the same as
             what was available when it was added.
             </summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.AddShutdownHandler(Python.Runtime.PythonEngine.ShutdownHandler)">
             <summary>
             Add a function to be called when the engine is shut down.
            
             Shutdown handlers are executed in the opposite order they were
             added, so that you can be sure that everything that was initialized
             when you added the handler is still initialized when you need to shut
             down.
            
             If the same shutdown handler is added several times, it will be run
             several times.
            
             Don't add shutdown handlers while running a shutdown handler.
             </summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.RemoveShutdownHandler(Python.Runtime.PythonEngine.ShutdownHandler)">
             <summary>
             Remove a shutdown handler.
            
             If the same shutdown handler is added several times, only the last
             one is removed.
            
             Don't remove shutdown handlers while running a shutdown handler.
             </summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.ExecuteShutdownHandlers">
             <summary>
             Run all the shutdown handlers.
            
             They're run in opposite order they were added.
             </summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.AcquireLock">
            <summary>
            AcquireLock Method
            </summary>
            <remarks>
            Acquire the Python global interpreter lock (GIL). Managed code
            *must* call this method before using any objects or calling any
            methods on objects in the Python.Runtime namespace. The only
            exception is PythonEngine.Initialize, which may be called without
            first calling AcquireLock.
            Each call to AcquireLock must be matched by a corresponding call
            to ReleaseLock, passing the token obtained from AcquireLock.
            For more information, see the "Extending and Embedding" section
            of the Python documentation on www.python.org.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.ReleaseLock(Python.Runtime.Native.PyGILState)">
            <summary>
            ReleaseLock Method
            </summary>
            <remarks>
            Release the Python global interpreter lock using a token obtained
            from a previous call to AcquireLock.
            For more information, see the "Extending and Embedding" section
            of the Python documentation on www.python.org.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.BeginAllowThreads">
            <summary>
            BeginAllowThreads Method
            </summary>
            <remarks>
            Release the Python global interpreter lock to allow other threads
            to run. This is equivalent to the Py_BEGIN_ALLOW_THREADS macro
            provided by the C Python API.
            For more information, see the "Extending and Embedding" section
            of the Python documentation on www.python.org.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.EndAllowThreads(System.IntPtr)">
            <summary>
            EndAllowThreads Method
            </summary>
            <remarks>
            Re-aquire the Python global interpreter lock for the current
            thread. This is equivalent to the Py_END_ALLOW_THREADS macro
            provided by the C Python API.
            For more information, see the "Extending and Embedding" section
            of the Python documentation on www.python.org.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.Eval(System.String,Python.Runtime.PyDict,Python.Runtime.PyObject)">
            <summary>
            Eval Method
            </summary>
            <remarks>
            Evaluate a Python expression and returns the result.
            It's a subset of Python eval function.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.Exec(System.String,Python.Runtime.PyDict,Python.Runtime.PyObject)">
            <summary>
            Exec Method
            </summary>
            <remarks>
            Run a string containing Python code.
            It's a subset of Python exec function.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.Exec(System.String,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Exec Method
            </summary>
            <remarks>
            Run a string containing Python code.
            It's a subset of Python exec function.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonEngine.GetPythonThreadID">
            <summary>
            Gets the Python thread ID.
            </summary>
            <returns>The Python thread ID.</returns>
        </member>
        <member name="M:Python.Runtime.PythonEngine.Interrupt(System.UInt64)">
            <summary>
            Interrupts the execution of a thread.
            </summary>
            <param name="pythonThreadID">The Python thread ID.</param>
            <returns>The number of thread states modified; this is normally one, but will be zero if the thread id is not found.</returns>
        </member>
        <member name="M:Python.Runtime.PythonEngine.RunString(System.String,Python.Runtime.PyDict,Python.Runtime.PyObject)">
            <summary>
            RunString Method. Function has been deprecated and will be removed.
            Use Exec/Eval/RunSimpleString instead.
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonEngine.RunString(System.String,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.RunFlagType)">
            <summary>
            Internal RunString Method.
            </summary>
            <remarks>
            Run a string containing Python code. Returns the result of
            executing the code string as a PyObject instance, or null if
            an exception was raised.
            </remarks>
        </member>
        <member name="T:Python.Runtime.PythonException">
            <summary>
            Provides a managed interface to exceptions thrown by the Python
            runtime.
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonException.ThrowLastAsClrException">
            <summary>
            Rethrows the last Python exception as corresponding CLR exception.
            It is recommended to call this as <code>throw ThrowLastAsClrException()</code>
            to assist control flow checks.
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonException.FromPyErr(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,System.Runtime.ExceptionServices.ExceptionDispatchInfo@)">
            <summary>
            Requires lock to be acquired elsewhere
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonException.Restore">
            <summary>Restores python error.</summary>
        </member>
        <member name="P:Python.Runtime.PythonException.Type">
            <summary>
            Returns the exception type as a Python object.
            </summary>
        </member>
        <member name="P:Python.Runtime.PythonException.Value">
            <summary>
            Returns the exception value as a Python object.
            </summary>
            <seealso cref="M:Python.Runtime.PythonException.Normalize"/>
        </member>
        <member name="P:Python.Runtime.PythonException.Traceback">
            <remarks>
            Returns the TraceBack as a Python object.
            </remarks>
        </member>
        <member name="P:Python.Runtime.PythonException.StackTrace">
            <summary>
            StackTrace Property
            </summary>
            <remarks>
            A string representing the python exception stack trace.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonException.Normalize">
            <summary>
            Replaces Value with an instance of Type, if Value is not already an instance of Type.
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonException.Format">
            <summary>
            Formats this PythonException object into a message as would be printed
            out via the Python console. See traceback.format_exception
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonException.CurrentMatches(Python.Runtime.BorrowedReference)">
            <summary>
            Returns <c>true</c> if the current Python exception
            matches the given exception type.
            </summary>
        </member>
        <member name="P:Python.Runtime.PyBuffer.Shape">
            <summary>
            An array of length <see cref="P:Python.Runtime.PyBuffer.Dimensions"/> indicating the shape of the memory as an n-dimensional array.
            </summary>
        </member>
        <member name="P:Python.Runtime.PyBuffer.Strides">
            <summary>
            An array of length <see cref="P:Python.Runtime.PyBuffer.Dimensions"/> giving the number of bytes to skip to get to a new element in each dimension.
            Will be null except when PyBUF_STRIDES or PyBUF_INDIRECT flags in GetBuffer/>.
            </summary>
        </member>
        <member name="P:Python.Runtime.PyBuffer.SubOffsets">
            <summary>
            An array of Py_ssize_t of length ndim. If suboffsets[n] >= 0,
            the values stored along the nth dimension are pointers and the suboffset value dictates how many bytes to add to each pointer after de-referencing.
            A suboffset value that is negative indicates that no de-referencing should occur (striding in a contiguous memory block).
            </summary>
        </member>
        <member name="M:Python.Runtime.PyBuffer.SizeFromFormat(System.String)">
            <summary>
            Return the implied itemsize from format. On error, raise an exception and return -1.
            New in version 3.9.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyBuffer.IsContiguous(Python.Runtime.BufferOrderStyle)">
            <summary>
            Returns true if the memory defined by the view is C-style (order is 'C') or Fortran-style (order is 'F') contiguous or either one (order is 'A'). Returns false otherwise.
            </summary>
            <param name="order">C-style (order is 'C') or Fortran-style (order is 'F') contiguous or either one (order is 'A')</param>
        </member>
        <member name="M:Python.Runtime.PyBuffer.GetPointer(System.Int64[])">
            <summary>
            Get the memory area pointed to by the indices inside the given view. indices must point to an array of view->ndim indices.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyBuffer.FromContiguous(System.IntPtr,System.Int64,Python.Runtime.BufferOrderStyle)">
            <summary>
            Copy contiguous len bytes from buf to view. fort can be 'C' or 'F' (for C-style or Fortran-style ordering).
            </summary>
        </member>
        <member name="M:Python.Runtime.PyBuffer.ToContiguous(System.IntPtr,Python.Runtime.BufferOrderStyle)">
            <summary>
            Copy len bytes from view to its contiguous representation in buf. order can be 'C' or 'F' or 'A' (for C-style or Fortran-style ordering or either one). 0 is returned on success, -1 on error.
            </summary>
            <param name="order">order can be 'C' or 'F' or 'A' (for C-style or Fortran-style ordering or either one).</param>
            <param name="buf">Buffer to copy to</param>
        </member>
        <member name="M:Python.Runtime.PyBuffer.FillContiguousStrides(System.Int32,System.IntPtr,System.IntPtr,System.Int32,Python.Runtime.BufferOrderStyle)">
            <summary>
            Fill the strides array with byte-strides of a contiguous (C-style if order is 'C' or Fortran-style if order is 'F') array of the given shape with the given number of bytes per element.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyBuffer.FillInfo(Python.Runtime.BorrowedReference,System.IntPtr,System.Int64,System.Boolean,System.Int32)">
            <summary>
            FillInfo Method
            </summary>
            <remarks>
            Handle buffer requests for an exporter that wants to expose buf of size len with writability set according to readonly. buf is interpreted as a sequence of unsigned bytes.
            The flags argument indicates the request type. This function always fills in view as specified by flags, unless buf has been designated as read-only and PyBUF_WRITABLE is set in flags.
            On success, set view->obj to a new reference to exporter and return 0. Otherwise, raise PyExc_BufferError, set view->obj to NULL and return -1;
            If this function is used as part of a getbufferproc, exporter MUST be set to the exporting object and flags must be passed unmodified.Otherwise, exporter MUST be NULL.
            </remarks>
            <returns>On success, set view->obj to a new reference to exporter and return 0. Otherwise, raise PyExc_BufferError, set view->obj to NULL and return -1;</returns>
        </member>
        <member name="M:Python.Runtime.PyBuffer.Write(System.Byte[],System.Int32,System.Int32,System.IntPtr)">
            <summary>
            Writes a managed byte array into the buffer of a python object. This can be used to pass data like images from managed to python.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyBuffer.Read(System.Byte[],System.Int32,System.Int32,System.IntPtr)">
            <summary>
            Reads the buffer of a python object into a managed byte array. This can be used to pass data like images from python to managed.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyBuffer.Dispose">
            <summary>
            Release the buffer view and decrement the reference count for view->obj. This function MUST be called when the buffer is no longer being used, otherwise reference leaks may occur.
            It is an error to call this function on a buffer that was not obtained via <see cref="M:Python.Runtime.PyObject.GetBuffer(Python.Runtime.PyBUF)"/>.
            </summary>
        </member>
        <member name="T:Python.Runtime.PyDict">
            <summary>
            Represents a Python dictionary object. See the documentation at
            PY2: https://docs.python.org/2/c-api/dict.html
            PY3: https://docs.python.org/3/c-api/dict.html
            for details.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyDict.#ctor">
            <summary>
            Creates a new Python dictionary object.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyDict.#ctor(Python.Runtime.PyObject)">
            <summary>
            Wraps existing dictionary object.
            </summary>
            <exception cref="T:System.ArgumentException">
            Thrown if the given object is not a Python dictionary object
            </exception>
        </member>
        <member name="M:Python.Runtime.PyDict.IsDictType(Python.Runtime.PyObject)">
            <summary>
            IsDictType Method
            </summary>
            <remarks>
            Returns true if the given object is a Python dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.HasKey(Python.Runtime.PyObject)">
            <summary>
            HasKey Method
            </summary>
            <remarks>
            Returns true if the object key appears in the dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.HasKey(System.String)">
            <summary>
            HasKey Method
            </summary>
            <remarks>
            Returns true if the string key appears in the dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.Keys">
            <summary>
            Keys Method
            </summary>
            <remarks>
            Returns a sequence containing the keys of the dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.Values">
            <summary>
            Values Method
            </summary>
            <remarks>
            Returns a sequence containing the values of the dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.Items">
            <summary>
            Items Method
            </summary>
            <remarks>
            Returns a sequence containing the items of the dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.Copy">
            <summary>
            Copy Method
            </summary>
            <remarks>
            Returns a copy of the dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.Update(Python.Runtime.PyObject)">
            <summary>
            Update Method
            </summary>
            <remarks>
            Update the dictionary from another dictionary.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyDict.Clear">
            <summary>
            Clear Method
            </summary>
            <remarks>
            Clears the dictionary.
            </remarks>
        </member>
        <member name="T:Python.Runtime.PyFloat">
            <summary>
            Represents a Python float object. See the documentation at
            PY3: https://docs.python.org/3/c-api/float.html
            for details.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyFloat.#ctor(Python.Runtime.PyObject)">
            <summary>
            PyFloat Constructor
            </summary>
            <remarks>
            Copy constructor - obtain a PyFloat from a generic PyObject. An
            ArgumentException will be thrown if the given object is not a
            Python float object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyFloat.#ctor(System.Double)">
            <summary>
            PyFloat Constructor
            </summary>
            <remarks>
            Creates a new Python float from a double value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyFloat.#ctor(System.String)">
            <summary>
            PyFloat Constructor
            </summary>
            <remarks>
            Creates a new Python float from a string value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyFloat.IsFloatType(Python.Runtime.PyObject)">
            <summary>
            IsFloatType Method
            </summary>
            <remarks>
            Returns true if the given object is a Python float.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyFloat.AsFloat(Python.Runtime.PyObject)">
            <summary>
            Convert a Python object to a Python float if possible, raising
            a PythonException if the conversion is not possible. This is
            equivalent to the Python expression "float(object)".
            </summary>
        </member>
        <member name="T:Python.Runtime.PyInt">
            <summary>
            Represents a Python integer object.
            See the documentation at https://docs.python.org/3/c-api/long.html
            </summary>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(Python.Runtime.PyObject)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Copy constructor - obtain a PyInt from a generic PyObject. An
            ArgumentException will be thrown if the given object is not a
            Python int object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.Int32)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from an int32 value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.UInt32)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from a uint32 value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.Int64)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from an int64 value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.UInt64)">
            <summary>
            Creates a new Python int from a <see cref="T:System.UInt64"/> value.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.Int16)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from an int16 value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.UInt16)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from a uint16 value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.Byte)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from a byte value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.SByte)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from an sbyte value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.#ctor(System.String)">
            <summary>
            PyInt Constructor
            </summary>
            <remarks>
            Creates a new Python int from a string value.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.IsIntType(Python.Runtime.PyObject)">
            <summary>
            IsIntType Method
            </summary>
            <remarks>
            Returns true if the given object is a Python int.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.AsInt(Python.Runtime.PyObject)">
            <summary>
            Convert a Python object to a Python int if possible, raising
            a PythonException if the conversion is not possible. This is
            equivalent to the Python expression "int(object)".
            </summary>
        </member>
        <member name="M:Python.Runtime.PyInt.ToInt16">
            <summary>
            ToInt16 Method
            </summary>
            <remarks>
            Return the value of the Python int object as an int16.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyInt.ToInt32">
            <summary>
            Return the value of the Python int object as an <see cref="T:System.Int32"/>.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyInt.ToInt64">
            <summary>
            ToInt64 Method
            </summary>
            <remarks>
            Return the value of the Python int object as an int64.
            </remarks>
        </member>
        <member name="T:Python.Runtime.PyIter">
            <summary>
            Represents a standard Python iterator object. See the documentation at
            PY2: https://docs.python.org/2/c-api/iterator.html
            PY3: https://docs.python.org/3/c-api/iterator.html
            for details.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyIter.#ctor(Python.Runtime.StolenReference@)">
            <summary>
            PyIter Constructor
            </summary>
            <remarks>
            Creates a new PyIter from an existing iterator reference. Note
            that the instance assumes ownership of the object reference.
            The object reference is not checked for type-correctness.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyIter.#ctor(Python.Runtime.PyObject)">
            <summary>
            Creates new <see cref="T:Python.Runtime.PyIter"/> from an untyped reference to Python object.
            The object must support iterator protocol.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyIter.GetIter(Python.Runtime.PyObject)">
             <summary>
             Create a new <see cref="T:Python.Runtime.PyIter"/> from a given iterable.
            
             Like doing "iter(<paramref name="iterable"/>)" in Python.
             </summary>
        </member>
        <member name="M:Python.Runtime.PyIterable.#ctor(Python.Runtime.PyObject)">
            <summary>
            Creates new instance from an existing object.
            </summary>
            <remarks>This constructor does not check if <paramref name="o"/> is actually iterable.</remarks>
        </member>
        <member name="M:Python.Runtime.PyIterable.GetEnumerator">
            <summary>
            Return a new PyIter object for the object. This allows any iterable
            python object to be iterated over in C#. A PythonException will be
            raised if the object is not iterable.
            </summary>
        </member>
        <member name="T:Python.Runtime.PyList">
            <summary>
            Represents a standard Python list object. See the documentation at
            PY2: https://docs.python.org/2/c-api/list.html
            PY3: https://docs.python.org/3/c-api/list.html
            for details.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.#ctor(Python.Runtime.BorrowedReference)">
            <summary>
            Creates new <see cref="T:Python.Runtime.PyList"/> pointing to the same object, as the given reference.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.#ctor(Python.Runtime.PyObject)">
            <summary>
            PyList Constructor
            </summary>
            <remarks>
            Copy constructor - obtain a PyList from a generic PyObject. An
            ArgumentException will be thrown if the given object is not a
            Python list object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyList.#ctor">
            <summary>
            Creates a new empty Python list object.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.#ctor(Python.Runtime.PyObject[])">
            <summary>
            Creates a new Python list object from an array of objects.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.IsListType(Python.Runtime.PyObject)">
            <summary>
            Returns true if the given object is a Python list.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.AsList(Python.Runtime.PyObject)">
            <summary>
            Converts a Python object to a Python list if possible, raising
            a PythonException if the conversion is not possible. This is
            equivalent to the Python expression "list(object)".
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.Append(Python.Runtime.PyObject)">
            <summary>
            Append an item to the list object.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.Insert(System.Int32,Python.Runtime.PyObject)">
            <summary>
            Insert an item in the list object at the given index.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyList.Reverse">
            <summary>
            Reverse Method
            </summary>
            <remarks>
            Reverse the order of the list object in place.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyList.Sort">
            <summary>
            Sort Method
            </summary>
            <remarks>
            Sort the list in place.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.Import(System.String)">
            <summary>
            Given a module or package name, import the module and return the resulting object.
            </summary>
            <param name="name">Fully-qualified module or package name</param>
        </member>
        <member name="M:Python.Runtime.PyModule.Reload">
            <summary>
            Reloads the module, and returns the updated object
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Variables">
            <summary>
            Returns the variables dict of the module.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.NewScope">
            <summary>
            Create a scope, and import all from this scope
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Import(System.String,System.String)">
            <summary>
            Import module by its name.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Import(Python.Runtime.PyModule,System.String)">
            <summary>
            Import module as a variable of given name.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Import(Python.Runtime.PyObject,System.String)">
            <summary>
            The 'import .. as ..' statement in Python.
            Import a module as a variable.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.ImportAll(Python.Runtime.PyModule)">
            <summary>
            Import all variables of the module into this module.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.ImportAll(Python.Runtime.PyObject)">
            <remarks>
            Import all variables of the module into this module.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.ImportAll(Python.Runtime.PyDict)">
            <summary>
            Import all variables in the dictionary into this module.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Execute(Python.Runtime.PyObject,Python.Runtime.PyDict)">
            <summary>
            Execute method
            </summary>
            <remarks>
            Execute a Python ast and return the result as a PyObject.
            The ast can be either an expression or stmts.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.Execute``1(Python.Runtime.PyObject,Python.Runtime.PyDict)">
            <summary>
            Execute a Python ast and return the result as a <see cref="T:Python.Runtime.PyObject"/>,
            and convert the result to a Managed Object of given type.
            The ast can be either an expression or stmts.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Eval(System.String,Python.Runtime.PyDict)">
            <summary>
            Evaluate a Python expression and return the result as a <see cref="T:Python.Runtime.PyObject"/>.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Eval``1(System.String,Python.Runtime.PyDict)">
            <summary>
            Evaluate a Python expression
            </summary>
            <remarks>
            Evaluate a Python expression
            and  convert the result to a Managed Object of given type.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.Exec(System.String,Python.Runtime.PyDict)">
            <summary>
            Exec Method
            </summary>
            <remarks>
            Exec a Python script and save its local variables in the current local variable dict.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.Set(System.String,System.Object)">
            <summary>
            Set Variable Method
            </summary>
            <remarks>
            Add a new variable to the variables dict if it not exist
            or update its value if the variable exists.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.Remove(System.String)">
            <summary>
            Remove Method
            </summary>
            <remarks>
            Remove a variable from the variables dict.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.Contains(System.String)">
            <summary>
            Returns true if the variable exists in the module.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyModule.Get(System.String)">
            <summary>
            Returns the value of the variable with the given name.
            </summary>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">
            Thrown when variable with the given name does not exist.
            </exception>
        </member>
        <member name="M:Python.Runtime.PyModule.TryGet(System.String,Python.Runtime.PyObject@)">
            <summary>
            TryGet Method
            </summary>
            <remarks>
            Returns the value of the variable, local variable first.
            If the variable does not exist, return null.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.Get``1(System.String)">
            <summary>
            Get Method
            </summary>
            <remarks>
            Obtain the value of the variable of given name,
            and convert the result to a Managed Object of given type.
            If the variable does not exist, throw an Exception.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyModule.TryGet``1(System.String,``0@)">
            <summary>
            TryGet Method
            </summary>
            <remarks>
            Obtain the value of the variable of given name,
            and convert the result to a Managed Object of given type.
            If the variable does not exist, return false.
            </remarks>
        </member>
        <member name="T:Python.Runtime.PyNumber">
            <summary>
            Represents a generic Python number. The methods of this class are
            equivalent to the Python "abstract number API". See
            PY3: https://docs.python.org/3/c-api/number.html
            for details.
            </summary>
            <remarks>
            TODO: add all of the PyNumber_XXX methods.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyNumber.IsNumberType(Python.Runtime.PyObject)">
            <summary>
            IsNumberType Method
            </summary>
            <remarks>
            Returns true if the given object is a Python numeric type.
            </remarks>
        </member>
        <member name="T:Python.Runtime.PyObject">
            <summary>
            Represents a generic Python object. The methods of this class are
            generally equivalent to the Python "abstract object API". See
            PY2: https://docs.python.org/2/c-api/object.html
            PY3: https://docs.python.org/3/c-api/object.html
            for details.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObject.#ctor(System.IntPtr)">
            <summary>
            PyObject Constructor
            </summary>
            <remarks>
            Creates a new PyObject from an IntPtr object reference. Note that
            the PyObject instance assumes ownership of the object reference
            and the reference will be DECREFed when the PyObject is garbage
            collected or explicitly disposed.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.#ctor(Python.Runtime.BorrowedReference)">
            <summary>
            Creates new <see cref="T:Python.Runtime.PyObject"/> pointing to the same object as
            the <paramref name="reference"/>. Increments refcount, allowing <see cref="T:Python.Runtime.PyObject"/>
            to have ownership over its own reference.
            </summary>
        </member>
        <member name="P:Python.Runtime.PyObject.Handle">
            <summary>
            Gets the native handle of the underlying Python object. This
            value is generally for internal use by the PythonNet runtime.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObject.FromManagedObject(System.Object)">
            <summary>
            Gets raw Python proxy for this object (bypasses all conversions,
            except <c>null</c> &lt;==&gt; <c>None</c>)
            </summary>
            <remarks>
            Given an arbitrary managed object, return a Python instance that
            reflects the managed object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.FromNullableReference(Python.Runtime.BorrowedReference)">
            <summary>
            Creates new <see cref="T:Python.Runtime.PyObject"/> from a nullable reference.
            When <paramref name="reference"/> is <c>null</c>, <c>null</c> is returned.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObject.AsManagedObject(System.Type)">
            <summary>
            AsManagedObject Method
            </summary>
            <remarks>
            Return a managed object of the given type, based on the
            value of the Python object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.As``1">
            <summary>
            Return a managed object of the given type, based on the
            value of the Python object.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObject.Dispose">
            <summary>
            The Dispose method provides a way to explicitly release the
            Python object represented by a PyObject instance. It is a good
            idea to call Dispose on PyObjects that wrap resources that are
            limited or need strict lifetime control. Otherwise, references
            to Python objects will not be released until a managed garbage
            collection occurs.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObject.GetPythonType">
            <summary>
            GetPythonType Method
            </summary>
            <remarks>
            Returns the Python type of the object. This method is equivalent
            to the Python expression: type(object).
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.TypeCheck(Python.Runtime.PyType)">
            <summary>
            TypeCheck Method
            </summary>
            <remarks>
            Returns true if the object o is of type typeOrClass or a subtype
            of typeOrClass.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.HasAttr(System.String)">
            <summary>
            HasAttr Method
            </summary>
            <remarks>
            Returns true if the object has an attribute with the given name.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.HasAttr(Python.Runtime.PyObject)">
            <summary>
            HasAttr Method
            </summary>
            <remarks>
            Returns true if the object has an attribute with the given name,
            where name is a PyObject wrapping a string or unicode object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetAttr(System.String)">
            <summary>
            GetAttr Method
            </summary>
            <remarks>
            Returns the named attribute of the Python object, or raises a
            PythonException if the attribute access fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetAttr(System.String,Python.Runtime.PyObject)">
             <summary>
             Returns the named attribute of the Python object, or the given
             default object if the attribute access throws AttributeError.
             </summary>
             <remarks>
             This method ignores any AttrubiteError(s), even ones
             not raised due to missing requested attribute.
            
             For example, if attribute getter calls other Python code, and
             that code happens to cause AttributeError elsewhere, it will be ignored
             and <paramref name="_default"/> value will be returned instead.
             </remarks>
             <param name="name">Name of the attribute.</param>
             <param name="_default">The object to return on AttributeError.</param>
        </member>
        <member name="M:Python.Runtime.PyObject.GetAttr(Python.Runtime.PyObject)">
            <summary>
            GetAttr Method
            </summary>
            <remarks>
            Returns the named attribute of the Python object or raises a
            PythonException if the attribute access fails. The name argument
            is a PyObject wrapping a Python string or unicode object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetAttr(Python.Runtime.PyObject,Python.Runtime.PyObject)">
             <summary>
             Returns the named attribute of the Python object, or the given
             default object if the attribute access throws AttributeError.
             </summary>
             <remarks>
             This method ignores any AttrubiteError(s), even ones
             not raised due to missing requested attribute.
            
             For example, if attribute getter calls other Python code, and
             that code happens to cause AttributeError elsewhere, it will be ignored
             and <paramref name="_default"/> value will be returned instead.
             </remarks>
             <param name="name">Name of the attribute. Must be of Python type 'str'.</param>
             <param name="_default">The object to return on AttributeError.</param>
        </member>
        <member name="M:Python.Runtime.PyObject.SetAttr(System.String,Python.Runtime.PyObject)">
            <summary>
            SetAttr Method
            </summary>
            <remarks>
            Set an attribute of the object with the given name and value. This
            method throws a PythonException if the attribute set fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.SetAttr(Python.Runtime.PyObject,Python.Runtime.PyObject)">
            <summary>
            SetAttr Method
            </summary>
            <remarks>
            Set an attribute of the object with the given name and value,
            where the name is a Python string or unicode object. This method
            throws a PythonException if the attribute set fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.DelAttr(System.String)">
            <summary>
            DelAttr Method
            </summary>
            <remarks>
            Delete the named attribute of the Python object. This method
            throws a PythonException if the attribute set fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.DelAttr(Python.Runtime.PyObject)">
            <summary>
            DelAttr Method
            </summary>
            <remarks>
            Delete the named attribute of the Python object, where name is a
            PyObject wrapping a Python string or unicode object. This method
            throws a PythonException if the attribute set fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetItem(Python.Runtime.PyObject)">
            <summary>
            GetItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            return the item at the given object index. This method raises a
            PythonException if the indexing operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetItem(System.String)">
            <summary>
            GetItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            return the item at the given string index. This method raises a
            PythonException if the indexing operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetItem(System.Int32)">
            <summary>
            GetItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            return the item at the given numeric index. This method raises a
            PythonException if the indexing operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.SetItem(Python.Runtime.PyObject,Python.Runtime.PyObject)">
            <summary>
            SetItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            set the item at the given object index to the given value. This
            method raises a PythonException if the set operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.SetItem(System.String,Python.Runtime.PyObject)">
            <summary>
            SetItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            set the item at the given string index to the given value. This
            method raises a PythonException if the set operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.SetItem(System.Int32,Python.Runtime.PyObject)">
            <summary>
            SetItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            set the item at the given numeric index to the given value. This
            method raises a PythonException if the set operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.DelItem(Python.Runtime.PyObject)">
            <summary>
            DelItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            delete the item at the given object index. This method raises a
            PythonException if the delete operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.DelItem(System.String)">
            <summary>
            DelItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            delete the item at the given string index. This method raises a
            PythonException if the delete operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.DelItem(System.Int32)">
            <summary>
            DelItem Method
            </summary>
            <remarks>
            For objects that support the Python sequence or mapping protocols,
            delete the item at the given numeric index. This method raises a
            PythonException if the delete operation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.Length">
            <summary>
            Returns the length for objects that support the Python sequence
            protocol.
            </summary>
        </member>
        <member name="P:Python.Runtime.PyObject.Item(System.String)">
            <summary>
            String Indexer
            </summary>
            <remarks>
            Provides a shorthand for the string versions of the GetItem and
            SetItem methods.
            </remarks>
        </member>
        <member name="P:Python.Runtime.PyObject.Item(Python.Runtime.PyObject)">
            <summary>
            PyObject Indexer
            </summary>
            <remarks>
            Provides a shorthand for the object versions of the GetItem and
            SetItem methods.
            </remarks>
        </member>
        <member name="P:Python.Runtime.PyObject.Item(System.Int32)">
            <summary>
            Numeric Indexer
            </summary>
            <remarks>
            Provides a shorthand for the numeric versions of the GetItem and
            SetItem methods.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetIterator">
            <summary>
            Return a new (Python) iterator for the object. This is equivalent
            to the Python expression "iter(object)".
            </summary>
            <exception cref="T:Python.Runtime.PythonException">Thrown if the object can not be iterated.</exception>
        </member>
        <member name="M:Python.Runtime.PyObject.Invoke(Python.Runtime.PyObject[])">
            <summary>
            Invoke Method
            </summary>
            <remarks>
            Invoke the callable object with the given arguments, passed as a
            PyObject[]. A PythonException is raised if the invocation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.Invoke(Python.Runtime.PyTuple)">
            <summary>
            Invoke Method
            </summary>
            <remarks>
            Invoke the callable object with the given arguments, passed as a
            Python tuple. A PythonException is raised if the invocation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.Invoke(Python.Runtime.PyObject[],Python.Runtime.PyDict)">
            <summary>
            Invoke Method
            </summary>
            <remarks>
            Invoke the callable object with the given positional and keyword
            arguments. A PythonException is raised if the invocation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.Invoke(Python.Runtime.PyTuple,Python.Runtime.PyDict)">
            <summary>
            Invoke Method
            </summary>
            <remarks>
            Invoke the callable object with the given positional and keyword
            arguments. A PythonException is raised if the invocation fails.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.InvokeMethod(System.String,Python.Runtime.PyObject[])">
            <summary>
            InvokeMethod Method
            </summary>
            <remarks>
            Invoke the named method of the object with the given arguments.
            A PythonException is raised if the invocation is unsuccessful.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.InvokeMethod(System.String,Python.Runtime.PyTuple)">
            <summary>
            InvokeMethod Method
            </summary>
            <remarks>
            Invoke the named method of the object with the given arguments.
            A PythonException is raised if the invocation is unsuccessful.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.InvokeMethod(Python.Runtime.PyObject,Python.Runtime.PyObject[])">
            <summary>
            InvokeMethod Method
            </summary>
            <remarks>
            Invoke the named method of the object with the given arguments.
            A PythonException is raised if the invocation is unsuccessful.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.InvokeMethod(Python.Runtime.PyObject,Python.Runtime.PyTuple)">
            <summary>
            InvokeMethod Method
            </summary>
            <remarks>
            Invoke the named method of the object with the given arguments.
            A PythonException is raised if the invocation is unsuccessful.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.InvokeMethod(System.String,Python.Runtime.PyObject[],Python.Runtime.PyDict)">
            <summary>
            InvokeMethod Method
            </summary>
            <remarks>
            Invoke the named method of the object with the given arguments
            and keyword arguments. Keyword args are passed as a PyDict object.
            A PythonException is raised if the invocation is unsuccessful.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.InvokeMethod(System.String,Python.Runtime.PyTuple,Python.Runtime.PyDict)">
            <summary>
            InvokeMethod Method
            </summary>
            <remarks>
            Invoke the named method of the object with the given arguments
            and keyword arguments. Keyword args are passed as a PyDict object.
            A PythonException is raised if the invocation is unsuccessful.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.IsInstance(Python.Runtime.PyObject)">
            <summary>
            IsInstance Method
            </summary>
            <remarks>
            Return true if the object is an instance of the given Python type
            or class. This method always succeeds.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.IsSubclass(Python.Runtime.PyObject)">
            <summary>
            Return <c>true</c> if the object is identical to or derived from the
            given Python type or class. This method always succeeds.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObject.IsCallable">
            <summary>
            IsCallable Method
            </summary>
            <remarks>
            Returns true if the object is a callable object. This method
            always succeeds.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.IsIterable">
            <summary>
            IsIterable Method
            </summary>
            <remarks>
            Returns true if the object is iterable object. This method
            always succeeds.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.IsTrue">
            <summary>
            IsTrue Method
            </summary>
            <remarks>
            Return true if the object is true according to Python semantics.
            This method always succeeds.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.IsNone">
            <summary>
            Return true if the object is None
            </summary>
        </member>
        <member name="M:Python.Runtime.PyObject.Dir">
            <summary>
            Dir Method
            </summary>
            <remarks>
            Return a list of the names of the attributes of the object. This
            is equivalent to the Python expression "dir(object)".
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.Repr">
            <summary>
            Repr Method
            </summary>
            <remarks>
            Return a string representation of the object. This method is
            the managed equivalent of the Python expression "repr(object)".
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.ToString">
            <summary>
            ToString Method
            </summary>
            <remarks>
            Return the string representation of the object. This method is
            the managed equivalent of the Python expression "str(object)".
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.Equals(System.Object)">
            <summary>
            Equals Method
            </summary>
            <remarks>
            Return true if this object is equal to the given object. This
            method is based on Python equality semantics.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetHashCode">
            <summary>
            GetHashCode Method
            </summary>
            <remarks>
            Return a hashcode based on the Python object. This returns the
            hash as computed by Python, equivalent to the Python expression
            "hash(obj)".
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetBuffer(Python.Runtime.PyBUF)">
            <summary>
            GetBuffer Method. This Method only works for objects that have a buffer (like "bytes", "bytearray" or "array.array")
            </summary>
            <remarks>
            Send a request to the PyObject to fill in view as specified by flags. If the PyObject cannot provide a buffer of the exact type, it MUST raise PyExc_BufferError, set view->obj to NULL and return -1.
            On success, fill in view, set view->obj to a new reference to exporter and return 0. In the case of chained buffer providers that redirect requests to a single object, view->obj MAY refer to this object instead of exporter(See Buffer Object Structures).
            Successful calls to <see cref="M:Python.Runtime.PyObject.GetBuffer(Python.Runtime.PyBUF)"/> must be paired with calls to <see cref="M:Python.Runtime.PyBuffer.Dispose"/>, similar to malloc() and free(). Thus, after the consumer is done with the buffer, <see cref="M:Python.Runtime.PyBuffer.Dispose"/> must be called exactly once.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyObject.GetDynamicMemberNames">
            <summary>
            Returns the enumeration of all dynamic member names.
            </summary>
            <remarks>
            This method exists for debugging purposes only.
            </remarks>
            <returns>A sequence that contains dynamic member names.</returns>
        </member>
        <member name="T:Python.Runtime.PySequence">
            <summary>
            Represents a generic Python sequence. The methods of this class are
            equivalent to the Python "abstract sequence API". See
            PY2: https://docs.python.org/2/c-api/sequence.html
            PY3: https://docs.python.org/3/c-api/sequence.html
            for details.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.#ctor(Python.Runtime.PyObject)">
            <summary>
            Creates new instance from an existing object.
            </summary>
            <exception cref="T:System.ArgumentException"><paramref name="o"/> does not provide sequence protocol</exception>
        </member>
        <member name="M:Python.Runtime.PySequence.IsSequenceType(Python.Runtime.PyObject)">
            <summary>
            Returns <c>true</c> if the given object implements the sequence protocol.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.GetSlice(System.Int32,System.Int32)">
            <summary>
            Return the slice of the sequence with the given indices.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.SetSlice(System.Int32,System.Int32,Python.Runtime.PyObject)">
            <summary>
            Sets the slice of the sequence with the given indices.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.DelSlice(System.Int32,System.Int32)">
            <summary>
            DelSlice Method
            </summary>
            <remarks>
            Deletes the slice of the sequence with the given indices.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PySequence.Index(Python.Runtime.PyObject)">
            <summary>
            Return the index of the given item in the sequence, or -1 if
            the item does not appear in the sequence.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.Index32(Python.Runtime.PyObject)">
            <summary>
            Return the index of the given item in the sequence, or -1 if
            the item does not appear in the sequence.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.Index64(Python.Runtime.PyObject)">
            <summary>
            Return the index of the given item in the sequence, or -1 if
            the item does not appear in the sequence.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.Contains(Python.Runtime.PyObject)">
            <summary>
            Return true if the sequence contains the given item. This method
            throws a PythonException if an error occurs during the check.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.Concat(Python.Runtime.PyObject)">
            <summary>
            Return the concatenation of the sequence object with the passed in
            sequence object.
            </summary>
        </member>
        <member name="M:Python.Runtime.PySequence.Repeat(System.Int32)">
            <summary>
            Return the sequence object repeated N times. This is equivalent
            to the Python expression "object * count".
            </summary>
        </member>
        <member name="T:Python.Runtime.PyString">
            <summary>
            Represents a Python (ANSI) string object. See the documentation at
            PY2: https://docs.python.org/2/c-api/string.html
            PY3: No Equivalent
            for details.
            </summary>
            <remarks>
            2011-01-29: ...Then why does the string constructor call PyUnicode_FromUnicode()???
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyString.#ctor(Python.Runtime.PyObject)">
            <summary>
            PyString Constructor
            </summary>
            <remarks>
            Copy constructor - obtain a PyString from a generic PyObject.
            An ArgumentException will be thrown if the given object is not
            a Python string object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyString.#ctor(System.String)">
            <summary>
            PyString Constructor
            </summary>
            <remarks>
            Creates a Python string from a managed string.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyString.IsStringType(Python.Runtime.PyObject)">
            <summary>
            Returns true if the given object is a Python string.
            </summary>
        </member>
        <member name="T:Python.Runtime.PyTuple">
            <summary>
            Represents a Python tuple object. See the documentation at
            PY2: https://docs.python.org/2/c-api/tupleObjects.html
            PY3: https://docs.python.org/3/c-api/tupleObjects.html
            for details.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyTuple.#ctor(Python.Runtime.BorrowedReference)">
            <summary>
            PyTuple Constructor
            </summary>
            <remarks>
            Creates a new PyTuple from an existing object reference.
            The object reference is not checked for type-correctness.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyTuple.#ctor(Python.Runtime.PyObject)">
            <summary>
            PyTuple Constructor
            </summary>
            <remarks>
            Copy constructor - obtain a PyTuple from a generic PyObject. An
            ArgumentException will be thrown if the given object is not a
            Python tuple object.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyTuple.#ctor">
            <summary>
            PyTuple Constructor
            </summary>
            <remarks>
            Creates a new empty PyTuple.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyTuple.#ctor(Python.Runtime.PyObject[])">
            <summary>
            PyTuple Constructor
            </summary>
            <remarks>
            Creates a new PyTuple from an array of PyObject instances.
            <para />
            See caveats about PyTuple_SetItem:
            https://www.coursehero.com/file/p4j2ogg/important-exceptions-to-this-rule-PyTupleSetItem-and-PyListSetItem-These/
            </remarks>
        </member>
        <member name="M:Python.Runtime.PyTuple.IsTupleType(Python.Runtime.PyObject)">
            <summary>
            Returns <c>true</c> if the given object is a Python tuple.
            </summary>
        </member>
        <member name="M:Python.Runtime.PyTuple.AsTuple(Python.Runtime.PyObject)">
            <summary>
            Convert a Python object to a Python tuple if possible. This is
            equivalent to the Python expression "tuple(<paramref name="value"/>)".
            </summary>
            <exception cref="T:Python.Runtime.PythonException">Raised if the object can not be converted to a tuple.</exception>
        </member>
        <member name="M:Python.Runtime.PyType.#ctor(Python.Runtime.TypeSpec,Python.Runtime.PyTuple)">
            <summary>Creates heap type object from the <paramref name="spec"/>.</summary>
        </member>
        <member name="M:Python.Runtime.PyType.#ctor(Python.Runtime.PyObject)">
            <summary>Wraps an existing type object.</summary>
        </member>
        <member name="P:Python.Runtime.PyType.IsReady">
            <summary>Returns <c>true</c> when type is fully initialized</summary>
        </member>
        <member name="M:Python.Runtime.PyType.IsType(Python.Runtime.PyObject)">
            <summary>Checks if specified object is a Python type.</summary>
        </member>
        <member name="M:Python.Runtime.PyType.IsType(Python.Runtime.BorrowedReference)">
            <summary>Checks if specified object is a Python type.</summary>
        </member>
        <member name="M:Python.Runtime.PyType.Get(System.Type)">
            <summary>
            Gets <see cref="T:Python.Runtime.PyType"/>, which represents the specified CLR type.
            </summary>
        </member>
        <member name="F:Python.Runtime.TypeSlotID.tp_finalize">
            <remarks>New in 3.5</remarks>
        </member>
        <member name="T:Python.Runtime.Runtime">
            <summary>
            Encapsulates the low-level Python C API. Note that it is
            the responsibility of the caller to have acquired the GIL
            before calling any of these methods.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.Initialize(System.Boolean)">
            <summary>
            Initialize the runtime...
            </summary>
            <remarks>Always call this method from the Main thread.  After the
            first call to this method, the main thread has acquired the GIL.</remarks>
        </member>
        <member name="M:Python.Runtime.Runtime.TryCollectingGarbage(System.Int32)">
            <summary>
            Alternates .NET and Python GC runs in an attempt to collect all garbage
            </summary>
            <param name="runs">Total number of GC loops to run</param>
            <returns><c>true</c> if a steady state was reached upon the requested number of tries (e.g. on the last try no objects were collected).</returns>
        </member>
        <member name="M:Python.Runtime.Runtime.CheckExceptionOccurred">
            <summary>
            Check if any Python Exceptions occurred.
            If any exist throw new PythonException.
            </summary>
            <remarks>
            Can be used instead of `obj == IntPtr.Zero` for example.
            </remarks>
        </member>
        <member name="M:Python.Runtime.Runtime.XIncref(Python.Runtime.BorrowedReference)">
            <summary>
            Managed exports of the Python C API. Where appropriate, we do
            some optimization to avoid managed &lt;--&gt; unmanaged transitions
            (mostly for heavily used methods).
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.TryUsingDll``1(System.Func{``0})">
            <summary>
            Call specified function, and handle PythonDLL-related failures.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.Py_IncRef(Python.Runtime.BorrowedReference)">
            <summary>
            Export of Macro Py_XIncRef. Use XIncref instead.
            Limit this function usage for Testing and Py_Debug builds
            </summary>
            <param name="ob">PyObject Ptr</param>
        </member>
        <member name="M:Python.Runtime.Runtime.Py_DecRef(Python.Runtime.StolenReference)">
            <summary>
            Export of Macro Py_XDecRef. Use XDecref instead.
            Limit this function usage for Testing and Py_Debug builds
            </summary>
            <param name="ob">PyObject Ptr</param>
        </member>
        <member name="M:Python.Runtime.Runtime.Py_CompileString(System.String,System.String,System.Int32)">
            <summary>
            Return value: New reference.
            This is a simplified interface to Py_CompileStringFlags() below, leaving flags set to NULL.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyObject_TYPE(Python.Runtime.BorrowedReference)">
            <summary>
            A macro-like method to get the type of a Python object. This is
            designed to be lean and mean in IL &amp; avoid managed &lt;-&gt; unmanaged
            transitions. Note that this does not incref the type object.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyObject_IsIterable(Python.Runtime.BorrowedReference)">
            <summary>
            Test whether the Python object is an iterable.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyLong_FromVoidPtr(System.IntPtr)">
            <summary>
            Return value: New reference.
            Create a Python integer from the pointer p. The pointer value can be retrieved from the resulting value using PyLong_AsVoidPtr().
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyLong_AsVoidPtr(Python.Runtime.BorrowedReference)">
            <summary>
            Convert a Python integer pylong to a C void pointer. If pylong cannot be converted, an OverflowError will be raised. This is only assured to produce a usable void pointer for values created with PyLong_FromVoidPtr().
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyUnicode_GetLength(Python.Runtime.BorrowedReference)">
            <summary>Length in code points</summary>
        </member>
        <member name="M:Python.Runtime.Runtime.GetManagedString(Python.Runtime.BorrowedReference@)">
            <summary>
            Function to access the internal PyUnicode/PyString object and
            convert it to a managed string with the correct encoding.
            </summary>
            <remarks>
            We can't easily do this through through the CustomMarshaler's on
            the returns because will have access to the IntPtr but not size.
            <para />
            For PyUnicodeType, we can't convert with Marshal.PtrToStringUni
            since it only works for UCS2.
            </remarks>
            <param name="op">PyStringType or PyUnicodeType object to convert</param>
            <returns>Managed String</returns>
        </member>
        <member name="M:Python.Runtime.Runtime.PyDict_GetItem(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Return NULL if the key is not present, but without setting an exception.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyDict_SetItem(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Return 0 on success or -1 on failure.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyDict_SetItemString(Python.Runtime.BorrowedReference,System.String,Python.Runtime.BorrowedReference)">
            <summary>
             Return 0 on success or -1 on failure.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PySet_Contains(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Return 1 if found, 0 if not found, and -1 if an error is encountered.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyModule_AddObject(Python.Runtime.BorrowedReference,System.String,Python.Runtime.StolenReference)">
            <param name="module">The module to add the object to.</param>
            <param name="name">The key that will refer to the object.</param>
            <param name="value">The object to add to the module.</param>
            <returns>Return -1 on error, 0 on success.</returns>
        </member>
        <member name="M:Python.Runtime.Runtime.PyImport_ImportModule(System.String)">
            <summary>
            Return value: New reference.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PySys_GetObject(System.String)">
            <summary>
            Return value: Borrowed reference.
            Return the object name from the sys module or NULL if it does not exist, without setting an exception.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyType_Ready(Python.Runtime.BorrowedReference)">
            <summary>
            Finalize a type object. This should be called on all type objects to finish their initialization. This function is responsible for adding inherited slots from a type�s base class. Return 0 on success, or return -1 and sets an exception on error.
            </summary>
        </member>
        <member name="M:Python.Runtime.Runtime.PyException_SetCause(Python.Runtime.BorrowedReference,Python.Runtime.StolenReference)">
            <summary>
            Set the cause associated with the exception to cause. Use NULL to clear it. There is no type check to make sure that cause is either an exception instance or None. This steals a reference to cause.
            </summary>
        </member>
        <member name="P:Python.Runtime.RuntimeData.PostStashHook">
            <summary>
            Callback called as a last step in the serialization process
            </summary>
        </member>
        <member name="P:Python.Runtime.RuntimeData.PreRestoreHook">
            <summary>
            Callback called as the first step in the deserialization process
            </summary>
        </member>
        <member name="M:Python.Runtime.RuntimeData.ClearCLRData">
            <summary>
            Clears the old "clr_data" entry if a previous one is present.
            </summary>
        </member>
        <member name="M:Python.Runtime.RuntimeData.FreeSerializationData(System.String)">
            <summary>
            Removes the serialization capsule from the `sys` module object.
            </summary>
            <remarks>
            The serialization data must have been set with <code>StashSerializationData</code>
            </remarks>
            <param name="key">The name given to the capsule on the `sys` module object</param>
        </member>
        <member name="M:Python.Runtime.RuntimeData.StashSerializationData(System.String,System.IO.MemoryStream)">
            <summary>
            Stores the data in the <paramref name="stream"/> argument in a Python capsule and stores
            the capsule on the `sys` module object with the name <paramref name="key"/>.
            </summary>
            <remarks>
            No checks on pre-existing names on the `sys` module object are made.
            </remarks>
            <param name="key">The name given to the capsule on the `sys` module object</param>
            <param name="stream">A MemoryStream that contains the data to be placed in the capsule</param>
        </member>
        <member name="M:Python.Runtime.RuntimeData.GetSerializationData(System.String)">
            <summary>
            Retreives the previously stored data on a Python capsule.
            Throws if the object corresponding to the <paramref name="key"/> parameter
            on the `sys` module object is not a capsule.
            </summary>
            <param name="key">The name given to the capsule on the `sys` module object</param>
            <returns>A MemoryStream containing the previously saved serialization data.
            The stream is empty if no name matches the key.  </returns>
        </member>
        <member name="T:Python.Runtime.TypeManager">
            <summary>
            The TypeManager class is responsible for building binary-compatible
            Python type objects that are implemented in managed code.
            </summary>
        </member>
        <member name="F:Python.Runtime.TypeManager.pythonBaseTypeProvider">
            <remarks>initialized in <see cref="M:Python.Runtime.TypeManager.Initialize"/> rather than in constructor</remarks>
        </member>
        <member name="M:Python.Runtime.TypeManager.GetTypeReference(System.Type)">
            <summary>
            Given a managed Type derived from ExtensionType, get the handle to
            a Python type object that delegates its implementation to the Type
            object. These Python type instances are used to implement internal
            descriptor and utility types like ModuleObject, PropertyObject, etc.
            </summary>
        </member>
        <member name="M:Python.Runtime.TypeManager.CreateType(System.Type)">
            <summary>
            The following CreateType implementations do the necessary work to
            create Python types to represent managed extension types, reflected
            types, subclasses of reflected types and the managed metatype. The
            dance is slightly different for each kind of type due to different
            behavior needed and the desire to have the existing Python runtime
            do as much of the allocation and initialization work as possible.
            </summary>
        </member>
        <member name="M:Python.Runtime.TypeManager.AllocateTypeObject(System.String,Python.Runtime.PyType)">
            <summary>
            Utility method to allocate a type object &amp; do basic initialization.
            </summary>
        </member>
        <member name="M:Python.Runtime.TypeManager.InheritSubstructs(System.IntPtr)">
            <summary>
            Inherit substructs, that are not inherited by default:
            https://docs.python.org/3/c-api/typeobj.html#c.PyTypeObject.tp_as_number
            </summary>
        </member>
        <member name="M:Python.Runtime.TypeManager.InitializeSlots(Python.Runtime.PyType,System.Type,Python.Runtime.SlotsHolder)">
            <summary>
            Given a newly allocated Python type object and a managed Type that
            provides the implementation for the type, connect the type slots of
            the Python object to the managed methods of the implementing Type.
            </summary>
        </member>
        <member name="M:Python.Runtime.TypeManager.CopySlot(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,System.Int32)">
            <summary>
            Utility method to copy slots from a given type to another type.
            </summary>
        </member>
        <member name="M:Python.Runtime.SlotsHolder.#ctor(Python.Runtime.PyType)">
            <summary>
            Create slots holder for holding the delegate of slots and be able  to reset them.
            </summary>
            <param name="type">Steals a reference to target type</param>
        </member>
        <member name="T:Python.Runtime.ArrayObject">
            <summary>
            Implements a Python type for managed arrays. This type is essentially
            the same as a ClassObject, except that it provides sequence semantics
            to support natural array usage (indexing) from Python.
            </summary>
        </member>
        <member name="M:Python.Runtime.ArrayObject.mp_subscript(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __getitem__ for array types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ArrayObject.mp_ass_subscript(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __setitem__ for array types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ArrayObject.sq_contains(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __contains__ for array types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ArrayObject.InitializeSlots(Python.Runtime.PyType,System.Collections.Generic.ISet{System.String},Python.Runtime.SlotsHolder)">
            <summary>
            <see cref="M:Python.Runtime.TypeManager.InitializeSlots(Python.Runtime.PyType,System.Type,Python.Runtime.SlotsHolder)"/>
            </summary>
        </member>
        <member name="T:Python.Runtime.ClassBase">
            <summary>
            Base class for Python types that reflect managed types / classes.
            Concrete subclasses include ClassObject and DelegateObject. This
            class provides common attributes and common machinery for doing
            class initialization (initialization of the class __dict__). The
            concrete subclasses provide slot implementations appropriate for
            each variety of reflected type.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.type_subscript(Python.Runtime.BorrowedReference)">
            <summary>
            Default implementation of [] semantics for reflected types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.tp_richcompare(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,System.Int32)">
            <summary>
            Standard comparison implementation for instances of reflected types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.tp_iter_impl(Python.Runtime.BorrowedReference)">
            <summary>
            Standard iteration support for instances of reflected types. This
            allows natural iteration over objects that either are IEnumerable
            or themselves support IEnumerator directly.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.tp_hash(Python.Runtime.BorrowedReference)">
            <summary>
            Standard __hash__ implementation for instances of reflected types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.tp_str(Python.Runtime.BorrowedReference)">
            <summary>
            Standard __str__ implementation for instances of reflected types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.tp_dealloc(Python.Runtime.NewReference)">
            <summary>
            Standard dealloc implementation for instances of reflected types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.mp_subscript_impl(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __getitem__ for reflected classes and value types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassBase.mp_ass_subscript_impl(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __setitem__ for reflected classes and value types.
            </summary>
        </member>
        <member name="T:Python.Runtime.IPythonDerivedType">
            <summary>
            Managed class that provides the implementation for reflected types.
            Managed classes and value types are represented in Python by actual
            Python type objects. Each of those type objects is associated with
            an instance of ClassObject, which provides its implementation.
            </summary>
            <remarks>
            interface used to identify which C# types were dynamically created as python subclasses
            </remarks>
        </member>
        <member name="M:Python.Runtime.ClassDerivedObject.tp_clear(Python.Runtime.BorrowedReference)">
            <summary>
            No-op clear. Real cleanup happens in <seealso cref="M:Python.Runtime.PythonDerivedType.Finalize(System.IntPtr)"/>
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassDerivedObject.ToPython(Python.Runtime.IPythonDerivedType)">
            <summary>
            Called from Converter.ToPython for types that are python subclasses of managed types.
            The referenced python object is returned instead of a new wrapper.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassDerivedObject.CreateDerivedType(System.String,System.Type,System.Collections.Generic.IList{System.Type},Python.Runtime.BorrowedReference,System.String,System.String,System.String)">
            <summary>
            Creates a new managed type derived from a base type with any virtual
            methods overridden to call out to python if the associated python
            object has overridden the method.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassDerivedObject.AddConstructor(System.Reflection.ConstructorInfo,System.Type,System.Reflection.Emit.TypeBuilder)">
            <summary>
            Add a constructor override that calls the python ctor after calling the base type constructor.
            </summary>
            <param name="ctor">constructor to be called before calling the python ctor</param>
            <param name="baseType">Python callable object</param>
            <param name="typeBuilder">TypeBuilder for the new type the ctor is to be added to</param>
        </member>
        <member name="M:Python.Runtime.ClassDerivedObject.AddVirtualMethod(System.Reflection.MethodInfo,System.Type,System.Reflection.Emit.TypeBuilder)">
            <summary>
            Add a virtual method override that checks for an override on the python instance
            and calls it, otherwise fall back to the base class method.
            </summary>
            <param name="method">virtual method to be overridden</param>
            <param name="baseType">Python callable object</param>
            <param name="typeBuilder">TypeBuilder for the new type the method is to be added to</param>
        </member>
        <member name="M:Python.Runtime.ClassDerivedObject.AddPythonMethod(System.String,Python.Runtime.PyObject,System.Reflection.Emit.TypeBuilder)">
            <summary>
            Python method may have the following function attributes set to control how they're exposed:
            - _clr_return_type_    - method return type (required)
            - _clr_arg_types_      - list of method argument types (required)
            - _clr_method_name_    - method name, if different from the python method name (optional)
            </summary>
            <param name="methodName">Method name to add to the type</param>
            <param name="func">Python callable object</param>
            <param name="typeBuilder">TypeBuilder for the new type the method/property is to be added to</param>
        </member>
        <member name="M:Python.Runtime.ClassDerivedObject.AddPythonProperty(System.String,Python.Runtime.PyObject,System.Reflection.Emit.TypeBuilder)">
            <summary>
            Python properties may have the following function attributes set to control how they're exposed:
            - _clr_property_type_     - property type (required)
            </summary>
            <param name="propertyName">Property name to add to the type</param>
            <param name="func">Python property object</param>
            <param name="typeBuilder">TypeBuilder for the new type the method/property is to be added to</param>
        </member>
        <member name="T:Python.Runtime.PythonDerivedType">
            <summary>
            PythonDerivedType contains static methods used by the dynamically created
            derived type that allow it to call back into python from overridden virtual
            methods, and also handle the construction and destruction of the python
            object.
            </summary>
            <remarks>
            This has to be public as it's called from methods on dynamically built classes
            potentially in other assemblies.
            </remarks>
        </member>
        <member name="M:Python.Runtime.PythonDerivedType.InvokeMethod``1(Python.Runtime.IPythonDerivedType,System.String,System.String,System.Object[],System.RuntimeMethodHandle,System.RuntimeTypeHandle)">
            <summary>
            This is the implementation of the overridden methods in the derived
            type. It looks for a python method with the same name as the method
            on the managed base class and if it exists and isn't the managed
            method binding (i.e. it has been overridden in the derived python
            class) it calls it, otherwise it calls the base method.
            </summary>
        </member>
        <member name="M:Python.Runtime.PythonDerivedType.MarshalByRefsBack(System.Object[],System.Reflection.MethodBase,Python.Runtime.PyObject,System.Int32)">
            <summary>
            If the method has byref arguments, reinterprets Python return value
            as a tuple of new values for those arguments, and updates corresponding
            elements of <paramref name="args"/> array.
            </summary>
        </member>
        <member name="T:Python.Runtime.ClassObject">
            <summary>
            Managed class that provides the implementation for reflected types.
            Managed classes and value types are represented in Python by actual
            Python type objects. Each of those type objects is associated with
            an instance of ClassObject, which provides its implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassObject.GetDocString">
            <summary>
            Helper to get docstring from reflected constructor info.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassObject.GetEnumReprString(System.Enum)">
            <summary>
            given an enum, write a __repr__ string formatted in the same
            way as a python repr string. Something like:
              '&lt;Color.GREEN: 2&gt;';
            with a binary value for [Flags] enums
            </summary>
            <param name="inst">Instace of the enum object</param>
            <returns></returns>
        </member>
        <member name="M:Python.Runtime.ClassObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            ClassObject __repr__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassObject.tp_new_impl(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __new__ for reflected classes and value types.
            </summary>
        </member>
        <member name="M:Python.Runtime.ClassObject.NewString(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
             <summary>
             Construct a new .NET String object from Python args
            
             This manual implementation of all individual relevant constructors
             is required because System.String can't be allocated uninitialized.
            
             Additionally, it implements `String(pythonStr)`
             </summary>
        </member>
        <member name="M:Python.Runtime.ClassObject.NewPrimitive(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,System.Type)">
             <summary>
             Create a new Python object for a primitive type
            
             The primitive types are Boolean, Byte, SByte, Int16, UInt16,
             Int32, UInt32, Int64, UInt64, IntPtr, UIntPtr, Char, Double,
             and Single.
            
             All numeric types and Boolean can be handled by a simple
             conversion, (U)IntPtr has to be handled separately as we
             do not want to convert them automically to/from integers.
             </summary>
             <param name="type">.NET type to construct</param>
             <param name="tp">Corresponding Python type</param>
             <param name="args">Constructor arguments</param>
        </member>
        <member name="M:Python.Runtime.ClassObject.type_subscript(Python.Runtime.BorrowedReference)">
            <summary>
            Implementation of [] semantics for reflected types. This exists
            both to implement the Array[int] syntax for creating arrays and
            to support generic name overload resolution using [].
            </summary>
        </member>
        <member name="T:Python.Runtime.CLRModule">
            <summary>
            The CLR module is the root handler used by the magic import hook
            to import assemblies. It has a fixed module name "clr" and doesn't
            provide a namespace.
            </summary>
        </member>
        <member name="M:Python.Runtime.CLRModule.InitializePreload">
            <summary>
            The initializing of the preload hook has to happen as late as
            possible since sys.ps1 is created after the CLR module is
            created.
            </summary>
        </member>
        <member name="M:Python.Runtime.CLRModule.GetClrType(System.Type)">
             <summary>
             Get a Type instance for a class object.
             clr.GetClrType(IComparable) gives you the Type for IComparable,
             that you can e.g. perform reflection on. Similar to typeof(IComparable) in C#
             or clr.GetClrType(IComparable) in IronPython.
            
             </summary>
             <param name="type"></param>
             <returns>The Type object</returns>
        </member>
        <member name="M:Python.Runtime.CLRModule._load_clr_module(Python.Runtime.PyObject)">
            <summary>
            Note: This should *not* be called directly.
            The function that get/import a CLR assembly as a python module.
            This function should only be called by the import machinery as seen
            in importhook.cs
            </summary>
            <param name="spec">A ModuleSpec Python object</param>
            <returns>A new reference to the imported module, as a PyObject.</returns>
        </member>
        <member name="T:Python.Runtime.DelegateObject">
            <summary>
            Managed class that provides the implementation for reflected delegate
            types. Delegates are represented in Python by generated type objects.
            Each of those type objects is associated an instance of this class,
            which provides its implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.DelegateObject.GetTrueDelegate(Python.Runtime.BorrowedReference)">
            <summary>
            Given a PyObject pointer to an instance of a delegate type, return
            the true managed delegate the Python object represents (or null).
            </summary>
        </member>
        <member name="M:Python.Runtime.DelegateObject.tp_new(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            DelegateObject __new__ implementation. The result of this is a new
            PyObject whose type is DelegateObject and whose ob_data is a handle
            to an actual delegate instance. The method wrapped by the actual
            delegate instance belongs to an object generated to relay the call
            to the Python callable passed in.
            </summary>
        </member>
        <member name="M:Python.Runtime.DelegateObject.tp_call(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __call__ for reflected delegate types.
            </summary>
        </member>
        <member name="M:Python.Runtime.DelegateObject.tp_richcompare(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,System.Int32)">
            <summary>
            Implements __cmp__ for reflected delegate types.
            </summary>
        </member>
        <member name="T:Python.Runtime.EventBinding">
            <summary>
            Implements a Python event binding type, similar to a method binding.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventBinding.nb_inplace_add(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            EventBinding += operator implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventBinding.nb_inplace_subtract(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            EventBinding -= operator implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventBinding.tp_hash(Python.Runtime.BorrowedReference)">
            <summary>
            EventBinding  __hash__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventBinding.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            EventBinding __repr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.EventObject">
            <summary>
            Implements a Python descriptor type that provides access to CLR events.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventObject.tp_descr_get(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __get__ implementation. A getattr on an event returns
            a "bound" event that keeps a reference to the object instance.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventObject.tp_descr_set(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __set__ implementation. This actually never allows you
            to set anything; it exists solely to support the '+=' spelling of
            event handler registration. The reason is that given code like:
            'ob.SomeEvent += method', Python will attempt to set the attribute
            SomeEvent on ob to the result of the '+=' operation.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __repr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.ExceptionClassObject">
            <summary>
            Base class for Python types that reflect managed exceptions based on
            System.Exception
            </summary>
        </member>
        <member name="M:Python.Runtime.ExceptionClassObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            Exception __repr__ implementation
            </summary>
        </member>
        <member name="M:Python.Runtime.ExceptionClassObject.tp_str(Python.Runtime.BorrowedReference)">
            <summary>
            Exception __str__ implementation
            </summary>
        </member>
        <member name="T:Python.Runtime.ExtensionType">
            <summary>
            Base class for extensions whose instances *share* a single Python
            type object, such as the types that represent CLR methods, fields,
            etc. Instances implemented by this class do not support sub-typing.
            </summary>
        </member>
        <member name="M:Python.Runtime.ExtensionType.tp_setattro(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Type __setattr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.FieldObject">
            <summary>
            Implements a Python descriptor type that provides access to CLR fields.
            </summary>
        </member>
        <member name="M:Python.Runtime.FieldObject.tp_descr_get(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __get__ implementation. This method returns the
            value of the field on the given object. The returned value
            is converted to an appropriately typed Python object.
            </summary>
        </member>
        <member name="M:Python.Runtime.FieldObject.tp_descr_set(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __set__ implementation. This method sets the value of
            a field based on the given Python value. The Python value must be
            convertible to the type of the field.
            </summary>
        </member>
        <member name="M:Python.Runtime.FieldObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __repr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.GenericType">
            <summary>
            Implements reflected generic types. Note that the Python behavior
            is the same for both generic type definitions and constructed open
            generic types. Both are essentially factories for creating closed
            types based on the required generic type parameters.
            </summary>
        </member>
        <member name="M:Python.Runtime.GenericType.tp_new(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __new__ for reflected generic types.
            </summary>
        </member>
        <member name="M:Python.Runtime.GenericType.tp_call(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __call__ for reflected generic types.
            </summary>
        </member>
        <member name="T:Python.Runtime.Indexer">
            <summary>
            Bundles the information required to support an indexer property.
            </summary>
        </member>
        <member name="M:Python.Runtime.Indexer.GetDefaultArgs(Python.Runtime.BorrowedReference)">
            <summary>
            This will return default arguments a new instance of a tuple. The size
            of the tuple will indicate the number of default arguments.
            </summary>
            <param name="args">This is pointing to the tuple args passed in</param>
            <returns>a new instance of the tuple containing the default args</returns>
        </member>
        <member name="T:Python.Runtime.InterfaceObject">
            <summary>
            Provides the implementation for reflected interface types. Managed
            interfaces are represented in Python by actual Python type objects.
            Each of those type objects is associated with an instance of this
            class, which provides the implementation for the Python type.
            </summary>
        </member>
        <member name="M:Python.Runtime.InterfaceObject.tp_new(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implements __new__ for reflected interface types.
            </summary>
        </member>
        <member name="M:Python.Runtime.InterfaceObject.TryWrapObject(System.Object)">
            <summary>
            Wrap the given object in an interface object, so that only methods
            of the interface are available.
            </summary>
        </member>
        <member name="M:Python.Runtime.InterfaceObject.tp_getattro(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Expose the wrapped implementation through attributes in both
            converted/encoded (__implementation__) and raw (__raw_implementation__) form.
            </summary>
        </member>
        <member name="T:Python.Runtime.Iterator">
            <summary>
            Implements a generic Python iterator for IEnumerable objects and
            managed array objects. This supports 'for i in object:' in Python.
            </summary>
        </member>
        <member name="M:Python.Runtime.Iterator.tp_iternext(Python.Runtime.BorrowedReference)">
            <summary>
            Implements support for the Python iteration protocol.
            </summary>
        </member>
        <member name="T:Python.Runtime.ManagedType">
            <summary>
            Common base class for all objects that are implemented in managed
            code. It defines the common fields that associate CLR and Python
            objects and common utilities to convert between those identities.
            </summary>
        </member>
        <member name="M:Python.Runtime.ManagedType.GetManagedObject(Python.Runtime.BorrowedReference)">
            <summary>
            Given a Python object, return the associated managed object or null.
            </summary>
        </member>
        <member name="M:Python.Runtime.ManagedType.CallTypeClear(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Wrapper for calling tp_clear
            </summary>
        </member>
        <member name="M:Python.Runtime.ManagedType.Init(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Initializes given object, or returns <c>false</c> and sets Python error on failure
            </summary>
        </member>
        <member name="T:Python.Runtime.MetaType">
            <summary>
            The managed metatype. This object implements the type of all reflected
            types. It also provides support for single-inheritance from reflected
            managed types.
            </summary>
        </member>
        <member name="M:Python.Runtime.MetaType.Initialize">
            <summary>
            Metatype initialization. This bootstraps the CLR metatype to life.
            </summary>
        </member>
        <member name="M:Python.Runtime.MetaType.tp_new(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Metatype __new__ implementation. This is called to create a new
            class / type when a reflected class is subclassed.
            </summary>
        </member>
        <member name="M:Python.Runtime.MetaType.tp_call(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Metatype __call__ implementation. This is needed to ensure correct
            initialization (__init__ support), because the tp_call we inherit
            from PyType_Type won't call __init__ for metatypes it doesn't know.
            </summary>
        </member>
        <member name="M:Python.Runtime.MetaType.tp_setattro(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Type __setattr__ implementation for reflected types. Note that this
            is slightly different than the standard setattr implementation for
            the normal Python metatype (PyTypeType). We need to look first in
            the type object of a reflected type for a descriptor in order to
            support the right setattr behavior for static fields and properties.
            </summary>
        </member>
        <member name="M:Python.Runtime.MetaType.mp_subscript(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            The metatype has to implement [] semantics for generic types, so
            here we just delegate to the generic type def implementation. Its
            own mp_subscript
            </summary>
        </member>
        <member name="M:Python.Runtime.MetaType.tp_dealloc(Python.Runtime.NewReference)">
            <summary>
            Dealloc implementation. This is called when a Python type generated
            by this metatype is no longer referenced from the Python runtime.
            </summary>
        </member>
        <member name="T:Python.Runtime.MethodBinding">
            <summary>
            Implements a Python binding type for CLR methods. These work much like
            standard Python method bindings, but the same type is used to bind
            both static and instance methods.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinding.mp_subscript(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implement binding of generic methods using the subscript syntax [].
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinding.tp_getattro(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            MethodBinding __getattribute__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinding.tp_call(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            MethodBinding  __call__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinding.tp_hash(Python.Runtime.BorrowedReference)">
            <summary>
            MethodBinding  __hash__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodBinding.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            MethodBinding  __repr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.MethodObject">
            <summary>
            Implements a Python type that represents a CLR method. Method objects
            support a subscript syntax [] to allow explicit overload selection.
            </summary>
            <remarks>
            TODO: ForbidPythonThreadsAttribute per method info
            </remarks>
        </member>
        <member name="M:Python.Runtime.MethodObject.GetDocString">
            <summary>
            Helper to get docstrings from reflected method / param info.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodObject.IsStatic">
            <summary>
            This is a little tricky: a class can actually have a static method
            and instance methods all with the same name. That makes it tough
            to support calling a method 'unbound' (passing the instance as the
            first argument), because in this case we can't know whether to call
            the instance method unbound or call the static method.
            </summary>
            <remarks>
            The rule we is that if there are both instance and static methods
            with the same name, then we always call the static method. So this
            method returns true if any of the methods that are represented by
            the descriptor are static methods (called by MethodBinding).
            </remarks>
        </member>
        <member name="M:Python.Runtime.MethodObject.tp_getattro(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __getattribute__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodObject.tp_descr_get(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __get__ implementation. Accessing a CLR method returns
            a "bound" method similar to a Python bound method.
            </summary>
        </member>
        <member name="M:Python.Runtime.MethodObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __repr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.ModuleFunctionObject">
            <summary>
            Module level functions
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleFunctionObject.tp_call(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            __call__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleFunctionObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            __repr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.ModuleObject">
            <summary>
            Implements a Python type that provides access to CLR namespaces. The
            type behaves like a Python module, and can contain other sub-modules.
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleObject.#ctor(System.String)">
            <remarks><seealso cref="F:Python.Runtime.ModuleObject.dict"/> is initialized in <seealso cref="M:Python.Runtime.ModuleObject.Create(System.String)"/></remarks>
        </member>
        <member name="M:Python.Runtime.ModuleObject.GetAttribute(System.String,System.Boolean)">
            <summary>
            Returns a ClassBase object representing a type that appears in
            this module's namespace or a ModuleObject representing a child
            namespace (or null if the name is not found). This method does
            not increment the Python refcount of the returned object.
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleObject.StoreAttribute(System.String,Python.Runtime.BorrowedReference)">
            <summary>
            Stores an attribute in the instance dict for future lookups.
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleObject.LoadNames">
            <summary>
            Preloads all currently-known names for the module namespace. This
            can be called multiple times, to add names from assemblies that
            may have been loaded since the last call to the method.
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleObject.InitializeModuleMembers">
            <summary>
            Initialize module level functions and attributes
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleObject.tp_getattro(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            ModuleObject __getattribute__ implementation. Module attributes
            are always either classes or sub-modules representing subordinate
            namespaces. CLR modules implement a lazy pattern - the sub-modules
            and classes are created when accessed and cached for future use.
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            ModuleObject __repr__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.ModuleObject.tp_setattro(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Override the setattr implementation.
            This is needed because the import mechanics need
            to set a few attributes
            </summary>
        </member>
        <member name="T:Python.Runtime.ModulePropertyObject">
            <summary>
            Module level properties (attributes)
            </summary>
        </member>
        <member name="M:Python.Runtime.Slots.MpLengthSlot.impl(Python.Runtime.BorrowedReference)">
            <summary>
            Implements __len__ for classes that implement ICollection
            (this includes any IList implementer or Array subclass)
            </summary>
        </member>
        <member name="P:Python.Runtime.OperatorMethod.OpMethodMap">
            <summary>
            Maps the compiled method name in .NET CIL (e.g. op_Addition) to
            the equivalent Python operator (e.g. __add__) as well as the offset
            that identifies that operator's slot (e.g. nb_add) in heap space.
            </summary>
        </member>
        <member name="M:Python.Runtime.OperatorMethod.FixupSlots(Python.Runtime.BorrowedReference,System.Type)">
            <summary>
            For the operator methods of a CLR type, set the special slots of the
            corresponding Python type's operator methods.
            </summary>
        </member>
        <member name="M:Python.Runtime.OperatorMethod.HaveReverse(System.Reflection.MethodBase)">
            <summary>
            Check if the method should have a reversed operation.
            </summary>
            <param name="method">The operator method.</param>
            <returns></returns>
        </member>
        <member name="T:Python.Runtime.OverloadMapper">
            <summary>
            Implements the __overloads__ attribute of method objects. This object
            supports the [] syntax to explicitly select an overload by signature.
            </summary>
        </member>
        <member name="M:Python.Runtime.OverloadMapper.mp_subscript(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Implement explicit overload selection using subscript syntax ([]).
            </summary>
        </member>
        <member name="M:Python.Runtime.OverloadMapper.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            OverloadMapper  __repr__ implementation.
            </summary>
        </member>
        <member name="T:Python.Runtime.PropertyObject">
            <summary>
            Implements a Python descriptor type that manages CLR properties.
            </summary>
        </member>
        <member name="M:Python.Runtime.PropertyObject.tp_descr_get(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __get__ implementation. This method returns the
            value of the property on the given object. The returned value
            is converted to an appropriately typed Python object.
            </summary>
        </member>
        <member name="M:Python.Runtime.PropertyObject.tp_descr_set(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __set__ implementation. This method sets the value of
            a property based on the given Python value. The Python value must
            be convertible to the type of the property.
            </summary>
        </member>
        <member name="M:Python.Runtime.PropertyObject.tp_repr(Python.Runtime.BorrowedReference)">
            <summary>
            Descriptor __repr__ implementation.
            </summary>
        </member>
        <member name="M:Python.Runtime.ReflectedClrType.GetOrCreate(System.Type)">
            <summary>
            Get the Python type that reflects the given CLR type.
            </summary>
            <remarks>
            Returned <see cref="T:Python.Runtime.ReflectedClrType"/> might be partially initialized.
            </remarks>
        </member>
        <member name="T:Python.Runtime.CodeGenerator">
            <summary>
            Several places in the runtime generate code on the fly to support
            dynamic functionality. The CodeGenerator class manages the dynamic
            assembly used for code generation and provides utility methods for
            certain repetitive tasks.
            </summary>
        </member>
        <member name="M:Python.Runtime.CodeGenerator.DefineType(System.String)">
            <summary>
            DefineType is a shortcut utility to get a new TypeBuilder.
            </summary>
        </member>
        <member name="M:Python.Runtime.CodeGenerator.DefineType(System.String,System.Type)">
            <summary>
            DefineType is a shortcut utility to get a new TypeBuilder.
            </summary>
        </member>
        <member name="M:Python.Runtime.CodeGenerator.GenerateMarshalByRefsBack(System.Reflection.Emit.ILGenerator,System.Collections.Generic.IReadOnlyList{System.Type})">
            <summary>
            Generates code, that copies potentially modified objects in args array
            back to the corresponding byref arguments
            </summary>
        </member>
        <member name="T:Python.Runtime.DebugUtil">
            <summary>
            Debugging helper utilities.
            The methods are only executed when the DEBUG flag is set. Otherwise
            they are automagically hidden by the compiler and silently suppressed.
            </summary>
        </member>
        <member name="M:Python.Runtime.DebugUtil.PrintHexBytes(System.Byte[])">
            <summary>
            Helper function to inspect/compare managed to native conversions.
            Especially useful when debugging CustomMarshaler.
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="M:Python.Runtime.EventHandlerCollection.AddEventHandler(Python.Runtime.BorrowedReference,Python.Runtime.PyObject)">
            <summary>
            Register a new Python object event handler with the event.
            </summary>
        </member>
        <member name="M:Python.Runtime.EventHandlerCollection.RemoveEventHandler(Python.Runtime.BorrowedReference,Python.Runtime.BorrowedReference)">
            <summary>
            Remove the given Python object event handler.
            </summary>
        </member>
        <member name="T:Python.Runtime.GenericUtil">
            <summary>
            This class is responsible for efficiently maintaining the bits
            of information we need to support aliases with 'nice names'.
            </summary>
        </member>
        <member name="F:Python.Runtime.GenericUtil.mapping">
            <summary>
            Maps namespace -> generic base name -> list of generic type names
            </summary>
        </member>
        <member name="M:Python.Runtime.GenericUtil.Register(System.Type)">
            <summary>
            Register a generic type that appears in a given namespace.
            </summary>
            <param name="t">A generic type definition (<c>t.IsGenericTypeDefinition</c> must be true)</param>
        </member>
        <member name="M:Python.Runtime.GenericUtil.GetGenericBaseNames(System.String)">
            <summary>
            xxx
            </summary>
        </member>
        <member name="M:Python.Runtime.GenericUtil.GenericForType(System.Type,System.Int32)">
            <summary>
            Finds a generic type with the given number of generic parameters and the same name and namespace as <paramref name="t"/>.
            </summary>
        </member>
        <member name="M:Python.Runtime.GenericUtil.GenericByName(System.String,System.String,System.Int32)">
            <summary>
            Finds a generic type in the given namespace with the given name and number of generic parameters.
            </summary>
        </member>
        <member name="M:Python.Runtime.GenericUtil.GenericNameForBaseName(System.String,System.String)">
            <summary>
            xxx
            </summary>
        </member>
        <member name="T:Python.Runtime.NullOnly">
            <summary>
            An utility class, that can only have one value: <c>null</c>.
            <para>Useful for overloading operators on structs,
            that have meaningful concept of <c>null</c> value (e.g. pointers and references).</para>
            </summary>
        </member>
        <member name="T:Python.Runtime.PythonReferenceComparer">
            <summary>
            Compares Python object wrappers by Python object references.
            <para>Similar to <see cref="M:System.Object.ReferenceEquals(System.Object,System.Object)"/> but for Python objects</para>
            </summary>
        </member>
        <member name="M:Python.Runtime.Util.AfterLast(System.String,System.Char)">
            <summary>
            Gets substring after last occurrence of <paramref name="symbol"/>
            </summary>
        </member>
    </members>
</doc>
