#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板图片捕获工具
用于快速截取微信界面元素作为OpenCV模板1
"""

import pyautogui
import cv2
import numpy as np
import os
import time

def capture_template():
    """交互式截图工具"""
    print("🎯 模板图片捕获工具")
    print("=" * 50)
    
    # 确保templates文件夹存在
    if not os.path.exists("templates"):
        os.makedirs("templates")
        print("✅ 创建templates文件夹")
    
    templates = {
        "1": ("user_not_found", "无法找到用户弹窗"),
        "2": ("confirm_button", "确定按钮"),
        "3": ("add_friend_button", "添加到通讯录按钮"),
        "4": ("network_search_button", "网络查找微信号按钮"),
        "5": ("dialog_background", "弹窗背景")
    }
    
    while True:
        print("\n请选择要截取的模板:")
        for key, (filename, desc) in templates.items():
            print(f"{key}. {desc} ({filename}.png)")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 退出截图工具")
            break
        
        if choice in templates:
            filename, desc = templates[choice]
            print(f"\n📸 准备截取: {desc}")
            print("请按以下步骤操作:")
            print("1. 打开微信并显示目标界面")
            print("2. 5秒后将自动截图")
            print("3. 请确保目标元素清晰可见")
            
            # 倒计时
            for i in range(5, 0, -1):
                print(f"⏰ {i}秒后截图...")
                time.sleep(1)
            
            try:
                # 截取全屏
                screenshot = pyautogui.screenshot()
                
                # 保存原始截图供参考
                ref_path = f"templates/reference_{filename}.png"
                screenshot.save(ref_path)
                print(f"📷 参考截图已保存: {ref_path}")
                
                # 转换为OpenCV格式进行分析
                screenshot_np = np.array(screenshot)
                screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
                
                # 根据类型进行智能检测
                if "button" in filename:
                    detect_and_save_button(screenshot_cv, filename, desc)
                elif "dialog" in filename or "user_not_found" in filename:
                    detect_and_save_dialog(screenshot_cv, filename, desc)
                else:
                    # 保存全屏截图
                    full_path = f"templates/{filename}.png"
                    screenshot.save(full_path)
                    print(f"✅ 模板已保存: {full_path}")
                
            except Exception as e:
                print(f"❌ 截图失败: {e}")
        else:
            print("❌ 无效选择，请重新输入")

def detect_and_save_button(screenshot_cv, filename, desc):
    """检测并保存按钮"""
    try:
        # 检测绿色按钮
        hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)
        lower_green = np.array([40, 50, 50])
        upper_green = np.array([80, 255, 255])
        green_mask = cv2.inRange(hsv, lower_green, upper_green)
        
        contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        buttons_found = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            if 500 < area < 10000:  # 按钮大小范围
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                if 1.0 < aspect_ratio < 5.0:  # 按钮宽高比
                    # 提取按钮区域
                    button_roi = screenshot_cv[y:y+h, x:x+w]
                    
                    # 保存按钮
                    button_path = f"templates/{filename}_{buttons_found}.png"
                    cv2.imwrite(button_path, button_roi)
                    print(f"✅ 检测到按钮: {button_path} (位置: {x},{y},{w},{h})")
                    buttons_found += 1
        
        if buttons_found == 0:
            print(f"⚠️ 未检测到{desc}，请手动截图")
        else:
            print(f"🎉 共检测到 {buttons_found} 个按钮")
            
    except Exception as e:
        print(f"❌ 按钮检测失败: {e}")

def detect_and_save_dialog(screenshot_cv, filename, desc):
    """检测并保存弹窗"""
    try:
        gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
        
        # 检测白色区域
        white_mask = cv2.inRange(gray, 240, 255)
        contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        dialogs_found = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            if 10000 < area < 200000:  # 弹窗大小范围
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                if 0.5 < aspect_ratio < 3.0:  # 弹窗宽高比
                    # 提取弹窗区域
                    dialog_roi = screenshot_cv[y:y+h, x:x+w]
                    
                    # 保存弹窗
                    dialog_path = f"templates/{filename}_{dialogs_found}.png"
                    cv2.imwrite(dialog_path, dialog_roi)
                    print(f"✅ 检测到弹窗: {dialog_path} (位置: {x},{y},{w},{h})")
                    dialogs_found += 1
        
        if dialogs_found == 0:
            print(f"⚠️ 未检测到{desc}，请手动截图")
        else:
            print(f"🎉 共检测到 {dialogs_found} 个弹窗")
            
    except Exception as e:
        print(f"❌ 弹窗检测失败: {e}")

if __name__ == "__main__":
    capture_template()
