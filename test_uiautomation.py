#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试uiautomation是否能正常工作
"""

import uiautomation as auto

def test_uiautomation():
    """测试uiautomation基本功能"""
    print("="*50)
    print("测试uiautomation功能")
    print("="*50)
    
    try:
        print("✅ uiautomation导入成功")
        print(f"📦 版本: {getattr(auto, '__version__', '2.0.28')}")
        
        # 测试查找微信窗口
        print("\n🔍 正在查找微信窗口...")
        
        # 尝试多种方式查找微信窗口
        wechat_methods = [
            ("ClassName", "WeChatMainWndForPC"),
            ("Name", "微信"),
            ("ClassName + Name", "WeChatMainWndForPC", "微信")
        ]
        
        wechat_window = None
        for method in wechat_methods:
            try:
                if len(method) == 2:
                    key, value = method
                    if key == "ClassName":
                        wechat_window = auto.WindowControl(searchDepth=1, ClassName=value)
                    elif key == "Name":
                        wechat_window = auto.WindowControl(searchDepth=1, Name=value)
                else:
                    wechat_window = auto.WindowControl(searchDepth=1, ClassName=method[1], Name=method[2])
                
                if wechat_window.Exists(0, 0):
                    print(f"✅ 找到微信窗口 (方法: {method[0]})")
                    print(f"   窗口名称: {wechat_window.Name}")
                    print(f"   窗口类名: {wechat_window.ClassName}")
                    break
                else:
                    print(f"❌ 未找到微信窗口 (方法: {method[0]})")
                    
            except Exception as e:
                print(f"❌ 查找失败 (方法: {method[0]}): {e}")
        
        if wechat_window and wechat_window.Exists(0, 0):
            print("\n🎯 微信窗口详细信息:")
            try:
                print(f"   位置: {wechat_window.BoundingRectangle}")
                print(f"   可见: {wechat_window.IsVisible}")
                print(f"   激活: {wechat_window.IsActive}")
                
                # 尝试获取子控件
                print("\n🔍 查找微信子控件...")
                children = wechat_window.GetChildren()
                print(f"   子控件数量: {len(children)}")
                
                # 查找搜索框
                search_controls = wechat_window.GetChildren(lambda c: c.ControlType == auto.ControlType.EditControl)
                print(f"   编辑框数量: {len(search_controls)}")
                
                if search_controls:
                    for i, edit in enumerate(search_controls[:3]):  # 只显示前3个
                        print(f"   编辑框{i+1}: {edit.Name} - {edit.AutomationId}")
                
            except Exception as e:
                print(f"   获取详细信息失败: {e}")
        else:
            print("\n❌ 未找到微信窗口")
            print("请确保:")
            print("1. 微信PC版已打开")
            print("2. 微信已登录")
            print("3. 微信窗口可见")
        
        # 测试其他窗口
        print("\n🔍 查找其他窗口...")
        try:
            all_windows = auto.GetRootControl().GetChildren(lambda c: c.ControlType == auto.ControlType.WindowControl)
            print(f"   系统窗口总数: {len(all_windows)}")
            
            # 显示前5个窗口
            for i, window in enumerate(all_windows[:5]):
                if window.Name:
                    print(f"   窗口{i+1}: {window.Name} ({window.ClassName})")
                    
        except Exception as e:
            print(f"   获取系统窗口失败: {e}")
        
        print("\n" + "="*50)
        print("测试完成！")
        
        if wechat_window and wechat_window.Exists(0, 0):
            print("✅ uiautomation可以正常使用")
            print("💡 建议运行: python wechat_uiautomation.py")
        else:
            print("⚠️ 请先打开微信后再测试")
            
        print("="*50)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_uiautomation()
