#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于pyautogui的微信自动化替代方案
使用图像识别和鼠标键盘操作
"""

import time
import logging
from typing import Tuple, Optional

try:
    import pyautogui
    import cv2
    import numpy as np
    from PIL import Image
except ImportError:
    print("请安装依赖: pip install pyautogui opencv-python pillow")
    exit(1)

# 设置pyautogui
pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
pyautogui.PAUSE = 0.5      # 每次操作间隔

class WeChatPyAutoGUI:
    """基于pyautogui的微信自动化类"""
    
    def __init__(self):
        """初始化"""
        self.setup_logging()
        self.confidence = 0.8  # 图像识别置信度
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler('wechat_pyautogui.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def find_image_on_screen(self, image_path: str, confidence: float = None) -> Optional[Tuple[int, int]]:
        """在屏幕上查找图像"""
        try:
            if confidence is None:
                confidence = self.confidence
                
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                center = pyautogui.center(location)
                self.logger.info(f"找到图像 {image_path} 位置: {center}")
                return center
            else:
                self.logger.warning(f"未找到图像: {image_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"查找图像失败: {e}")
            return None
    
    def click_image(self, image_path: str, confidence: float = None) -> bool:
        """点击屏幕上的图像"""
        location = self.find_image_on_screen(image_path, confidence)
        if location:
            pyautogui.click(location)
            self.logger.info(f"点击图像: {image_path}")
            return True
        return False
    
    def activate_wechat(self) -> bool:
        """激活微信窗口"""
        try:
            # 尝试点击微信图标或任务栏
            wechat_locations = [
                "images/wechat_icon.png",
                "images/wechat_taskbar.png"
            ]
            
            for image_path in wechat_locations:
                if self.click_image(image_path, confidence=0.7):
                    time.sleep(1)
                    return True
            
            # 如果找不到图标，尝试Alt+Tab切换
            pyautogui.hotkey('alt', 'tab')
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.logger.error(f"激活微信失败: {e}")
            return False
    
    def search_contact(self, contact_name: str) -> bool:
        """搜索联系人"""
        try:
            # 点击搜索框
            if self.click_image("images/search_box.png"):
                time.sleep(0.5)
                
                # 清空并输入联系人名称
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.2)
                pyautogui.write(contact_name)
                time.sleep(1)
                
                self.logger.info(f"搜索联系人: {contact_name}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"搜索联系人失败: {e}")
            return False
    
    def send_message(self, contact_name: str, message: str) -> bool:
        """发送消息"""
        try:
            # 搜索联系人
            if not self.search_contact(contact_name):
                return False
            
            # 按回车打开聊天
            pyautogui.press('enter')
            time.sleep(1)
            
            # 点击消息输入框
            if self.click_image("images/message_input.png"):
                time.sleep(0.5)
                pyautogui.write(message)
                time.sleep(0.5)
                pyautogui.press('enter')
                
                self.logger.info(f"向 {contact_name} 发送消息: {message}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def add_friend(self, search_keyword: str, verify_msg: str = "") -> bool:
        """添加好友"""
        try:
            # 搜索用户
            if not self.search_contact(search_keyword):
                return False
            
            time.sleep(1)
            
            # 点击添加到通讯录按钮
            if self.click_image("images/add_contact_button.png"):
                time.sleep(1)
                
                # 如果有验证消息输入框
                if verify_msg and self.click_image("images/verify_input.png"):
                    pyautogui.write(verify_msg)
                    time.sleep(0.5)
                
                # 点击发送按钮
                if self.click_image("images/send_button.png"):
                    self.logger.info(f"已发送好友申请给: {search_keyword}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"添加好友失败: {e}")
            return False
    
    def take_screenshot(self, filename: str = None) -> str:
        """截取屏幕截图"""
        if filename is None:
            filename = f"screenshot_{int(time.time())}.png"
        
        screenshot = pyautogui.screenshot()
        screenshot.save(filename)
        self.logger.info(f"截图保存为: {filename}")
        return filename
    
    def create_template_images(self):
        """创建模板图像指导"""
        print("="*50)
        print("图像模板创建指导")
        print("="*50)
        print("为了使用图像识别功能，您需要创建以下模板图像:")
        print("")
        print("1. images/search_box.png - 微信搜索框截图")
        print("2. images/message_input.png - 消息输入框截图")
        print("3. images/add_contact_button.png - 添加到通讯录按钮截图")
        print("4. images/send_button.png - 发送按钮截图")
        print("5. images/verify_input.png - 验证消息输入框截图")
        print("")
        print("创建步骤:")
        print("1. 打开微信")
        print("2. 使用截图工具截取上述界面元素")
        print("3. 保存到images/目录下")
        print("4. 确保图像清晰，大小适中")
        print("")
        print("提示: 可以使用take_screenshot()方法先截取全屏，")
        print("然后裁剪出需要的部分")
        print("="*50)

def main():
    """主函数示例"""
    wechat = WeChatPyAutoGUI()
    
    print("基于PyAutoGUI的微信自动化工具")
    print("1. 发送消息")
    print("2. 添加好友")
    print("3. 截取屏幕")
    print("4. 创建模板图像指导")
    
    choice = input("请选择功能 (1-4): ")
    
    if choice == "1":
        contact = input("请输入联系人名称: ")
        message = input("请输入消息内容: ")
        wechat.send_message(contact, message)
        
    elif choice == "2":
        keyword = input("请输入搜索关键词: ")
        verify_msg = input("请输入验证消息: ")
        wechat.add_friend(keyword, verify_msg)
        
    elif choice == "3":
        filename = input("请输入截图文件名 (留空使用默认): ").strip()
        if not filename:
            filename = None
        wechat.take_screenshot(filename)
        
    elif choice == "4":
        wechat.create_template_images()

if __name__ == "__main__":
    main()
