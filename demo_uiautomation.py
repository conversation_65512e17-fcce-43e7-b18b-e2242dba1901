#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
uiautomation微信自动化演示脚本
展示核心功能
"""

from wechat_uiautomation import WeChatUIAutomation
import time

def demo_basic_functions():
    """演示基本功能"""
    print("="*60)
    print("🚀 uiautomation微信自动化演示")
    print("="*60)
    
    try:
        # 创建实例
        wechat = WeChatUIAutomation()
        print("✅ 创建WeChatUIAutomation实例成功")
        
        # 查找微信窗口
        if wechat.find_wechat_window():
            print("✅ 找到微信窗口")
            
            # 激活微信窗口
            wechat.activate_wechat()
            print("✅ 激活微信窗口")
            
            print("\n" + "="*60)
            print("🎯 可用功能演示")
            print("="*60)
            
            # 演示搜索功能
            print("\n1. 🔍 搜索功能演示")
            print("   可以搜索任何联系人或群聊")
            print("   示例: wechat.search_contact('文件传输助手')")
            
            # 演示发送消息功能
            print("\n2. 💬 发送消息功能演示")
            print("   可以向任何联系人发送消息")
            print("   示例: wechat.send_message('文件传输助手', '测试消息')")
            
            # 演示添加好友功能
            print("\n3. 👥 添加好友功能演示")
            print("   可以自动搜索并添加好友")
            print("   示例: wechat.add_friend('用户名', '验证消息')")
            
            # 演示批量操作
            print("\n4. 📋 批量操作功能演示")
            print("   可以批量添加多个好友")
            print("   示例: wechat.batch_add_friends(friends_list)")
            
            print("\n" + "="*60)
            print("🎉 uiautomation功能演示完成！")
            print("="*60)
            print("💡 优势:")
            print("   ✅ 完全免费")
            print("   ✅ 功能强大")
            print("   ✅ 真正的自动化")
            print("   ✅ 不依赖第三方API")
            print("   ✅ 稳定可靠")
            
            print("\n🚀 立即开始使用:")
            print("   python test_wechat_ui.py  # 完整功能测试")
            print("   python wechat_enhanced.py  # 增强版工具")
            
        else:
            print("❌ 未找到微信窗口")
            print("请确保:")
            print("1. 微信PC版已打开")
            print("2. 微信已登录")
            print("3. 微信窗口可见")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def interactive_demo():
    """交互式演示"""
    print("="*60)
    print("🎮 交互式功能演示")
    print("="*60)
    
    try:
        wechat = WeChatUIAutomation()
        
        if not wechat.find_wechat_window():
            print("❌ 请先打开微信")
            return
        
        print("✅ 微信连接成功！")
        print("\n选择演示功能:")
        print("1. 测试搜索联系人")
        print("2. 发送测试消息")
        print("3. 查看联系人列表")
        print("4. 退出演示")
        
        while True:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == "1":
                contact = input("请输入要搜索的联系人名称: ").strip()
                if contact:
                    print(f"正在搜索: {contact}")
                    result = wechat.search_contact(contact)
                    if result:
                        print("✅ 搜索成功")
                    else:
                        print("❌ 搜索失败")
                        
            elif choice == "2":
                contact = input("请输入联系人名称: ").strip()
                message = input("请输入消息内容: ").strip()
                if contact and message:
                    confirm = input(f"确认向 {contact} 发送 '{message}'? (y/n): ")
                    if confirm.lower() == 'y':
                        result = wechat.send_message(contact, message)
                        if result:
                            print("✅ 消息发送成功")
                        else:
                            print("❌ 消息发送失败")
                            
            elif choice == "3":
                print("正在获取联系人列表...")
                contacts = wechat.get_contact_list()
                if contacts:
                    print(f"✅ 找到 {len(contacts)} 个联系人")
                    print("前5个联系人:")
                    for i, contact in enumerate(contacts[:5], 1):
                        print(f"  {i}. {contact}")
                else:
                    print("❌ 获取联系人列表失败")
                    
            elif choice == "4":
                print("演示结束")
                break
                
            else:
                print("❌ 无效选择")
                
    except Exception as e:
        print(f"❌ 交互式演示失败: {e}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_demo()
    else:
        demo_basic_functions()

if __name__ == "__main__":
    main()
