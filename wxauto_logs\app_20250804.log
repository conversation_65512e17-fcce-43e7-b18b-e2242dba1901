2025-08-04 01:46:04 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:46:31 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:46:31 [__main__] [INFO] [wechat.py:99]  微信客户端初始化成功
2025-08-04 01:46:31 [__main__] [INFO] [wechat.py:293]  自动回复模块微信客户端初始化成功
2025-08-04 01:46:31 [__main__] [WARNING] [wechat.py:125]  当前wxauto版本功能有限
2025-08-04 01:46:43 [__main__] [ERROR] [wechat.py:549]  获取会话信息失败: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:46:48 [__main__] [ERROR] [wechat.py:549]  获取会话信息失败: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:00 [__main__] [INFO] [wechat.py:463]  自动回复功能已启动
2025-08-04 01:47:00 [__main__] [INFO] [wechat.py:305]  成功加载自定义回复规则
2025-08-04 01:47:00 [__main__] [INFO] [wechat.py:389]  开始监控新消息...
2025-08-04 01:47:00 [__main__] [ERROR] [wechat.py:443]  监控消息时出错: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:05 [__main__] [ERROR] [wechat.py:443]  监控消息时出错: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:10 [__main__] [ERROR] [wechat.py:443]  监控消息时出错: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:13 [__main__] [INFO] [wechat.py:611]  用户中断程序
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:446]  消息监控已停止
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:470]  自动回复功能已停止
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:631]  
⚠️ 重要提示:
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:632]  1. 微信对频繁添加好友有严格限制
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:633]  2. 建议每日添加好友不超过10次
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:634]  3. 如遇到限制，请24小时后再试
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:635]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:618]  程序执行完成！
2025-08-04 01:51:52 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:99]  微信客户端初始化成功
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:305]  自动回复模块微信客户端初始化成功
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:112]  微信窗口检测成功
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:118]  当前微信账号: 又是一年冬
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:133]  微信状态正常（基础模式）
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:689]  准备批量添加好友 (自动模式)...
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:150]  成功加载 3 个好友数据
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:230]  开始批量处理 3 个好友...
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:237]  使用自动检测模式
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:240]  
[1/3] 处理: user001
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:176]  开始添加好友: user001
2025-08-04 01:52:04 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:198]    搜索关键词: user001
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:199]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:201]    建议备注: 客户001
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:203]    建议标签: 客户, 潜在合作
2025-08-04 01:52:04 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user001, False, False, 0.5
2025-08-04 01:52:10 [__main__] [INFO] [wechat.py:208]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-04 01:52:10 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 10.5 秒...
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:240]  
[2/3] 处理: user002
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:176]  开始添加好友: user002
2025-08-04 01:52:20 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:198]    搜索关键词: user002
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:199]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:201]    建议备注: 行业联系人002
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:203]    建议标签: 行业, 交流
2025-08-04 01:52:20 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user002, False, False, 0.5
2025-08-04 01:52:26 [__main__] [INFO] [wechat.py:208]  ✅ user002 可能已经是好友或可以直接聊天
2025-08-04 01:52:26 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 8.5 秒...
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:240]  
[3/3] 处理: user003
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:176]  开始添加好友: user003
2025-08-04 01:52:35 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:198]    搜索关键词: user003
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:199]    验证消息: 看到您在XX群的专业分享，希望能向您请教
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:201]    建议备注: 专业人士003
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:203]    建议标签: 专业, 学习
2025-08-04 01:52:35 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user003, False, False, 0.5
2025-08-04 01:52:41 [__main__] [INFO] [wechat.py:208]  ✅ user003 可能已经是好友或可以直接聊天
2025-08-04 01:52:41 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 16.9 秒...
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:730]  
==============================
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:731]  批量处理好友完成！
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:732]  ✅ 成功: 3 人
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:733]  ❌ 失败: 0 人
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:734]  ⏭️ 跳过: 0 人
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:735]  ==============================
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:689]  准备批量添加好友 (自动模式)...
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:150]  成功加载 3 个好友数据
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:230]  开始批量处理 3 个好友...
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:237]  使用自动检测模式
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:240]  
[1/3] 处理: user001
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:176]  开始添加好友: user001
2025-08-04 01:53:47 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:198]    搜索关键词: user001
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:199]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:201]    建议备注: 客户001
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:203]    建议标签: 客户, 潜在合作
2025-08-04 01:53:47 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user001, False, False, 0.5
2025-08-04 01:53:53 [__main__] [INFO] [wechat.py:208]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-04 01:53:53 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 6.3 秒...
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:240]  
[2/3] 处理: user002
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:176]  开始添加好友: user002
2025-08-04 01:53:59 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:198]    搜索关键词: user002
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:199]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:201]    建议备注: 行业联系人002
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:203]    建议标签: 行业, 交流
2025-08-04 01:53:59 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user002, False, False, 0.5
2025-08-04 01:54:05 [__main__] [INFO] [wechat.py:208]  ✅ user002 可能已经是好友或可以直接聊天
2025-08-04 01:54:05 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 14.9 秒...
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:719]  用户中断程序
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:483]  自动回复功能已停止
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:739]  
⚠️ 重要提示:
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:740]  1. 微信对频繁添加好友有严格限制
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:741]  2. 建议每日添加好友不超过10次
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:742]  3. 如遇到限制，请24小时后再试
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:743]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:726]  程序执行完成！
2025-08-04 01:54:28 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:100]  微信客户端初始化成功
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:306]  自动回复模块微信客户端初始化成功
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:113]  微信窗口检测成功
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:119]  当前微信账号: 又是一年冬
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:134]  微信状态正常（基础模式）
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:690]  准备批量添加好友 (自动模式)...
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:151]  成功加载 3 个好友数据
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:231]  开始批量处理 3 个好友...
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:238]  使用自动检测模式
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:241]  
[1/3] 处理: user001
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:177]  开始添加好友: user001
2025-08-04 01:54:33 [__main__] [WARNING] [wechat.py:196]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:197]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:198]  需要添加的好友信息:
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:199]    搜索关键词: user001
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:200]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:202]    建议备注: 客户001
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:204]    建议标签: 客户, 潜在合作
2025-08-04 01:54:33 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user001, False, False, 0.5
2025-08-04 01:54:40 [__main__] [INFO] [wechat.py:209]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-04 01:54:40 [__main__] [INFO] [wechat.py:257]  ⏱️ 等待 5.5 秒...
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:241]  
[2/3] 处理: user002
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:177]  开始添加好友: user002
2025-08-04 01:54:45 [__main__] [WARNING] [wechat.py:196]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:197]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:198]  需要添加的好友信息:
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:199]    搜索关键词: user002
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:200]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:202]    建议备注: 行业联系人002
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:204]    建议标签: 行业, 交流
2025-08-04 01:54:45 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user002, False, False, 0.5
2025-08-04 01:54:51 [__main__] [INFO] [wechat.py:209]  ✅ user002 可能已经是好友或可以直接聊天
2025-08-04 01:54:51 [__main__] [INFO] [wechat.py:257]  ⏱️ 等待 16.6 秒...
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:720]  用户中断程序
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:484]  自动回复功能已停止
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:740]  
⚠️ 重要提示:
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:741]  1. 微信对频繁添加好友有严格限制
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:742]  2. 建议每日添加好友不超过10次
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:743]  3. 如遇到限制，请24小时后再试
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:744]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:727]  程序执行完成！
