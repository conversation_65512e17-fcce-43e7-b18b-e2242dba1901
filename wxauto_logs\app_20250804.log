2025-08-04 01:46:04 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:46:31 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:46:31 [__main__] [INFO] [wechat.py:99]  微信客户端初始化成功
2025-08-04 01:46:31 [__main__] [INFO] [wechat.py:293]  自动回复模块微信客户端初始化成功
2025-08-04 01:46:31 [__main__] [WARNING] [wechat.py:125]  当前wxauto版本功能有限
2025-08-04 01:46:43 [__main__] [ERROR] [wechat.py:549]  获取会话信息失败: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:46:48 [__main__] [ERROR] [wechat.py:549]  获取会话信息失败: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:00 [__main__] [INFO] [wechat.py:463]  自动回复功能已启动
2025-08-04 01:47:00 [__main__] [INFO] [wechat.py:305]  成功加载自定义回复规则
2025-08-04 01:47:00 [__main__] [INFO] [wechat.py:389]  开始监控新消息...
2025-08-04 01:47:00 [__main__] [ERROR] [wechat.py:443]  监控消息时出错: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:05 [__main__] [ERROR] [wechat.py:443]  监控消息时出错: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:10 [__main__] [ERROR] [wechat.py:443]  监控消息时出错: 'WeChat' object has no attribute 'GetSessionList'
2025-08-04 01:47:13 [__main__] [INFO] [wechat.py:611]  用户中断程序
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:446]  消息监控已停止
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:470]  自动回复功能已停止
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:631]  
⚠️ 重要提示:
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:632]  1. 微信对频繁添加好友有严格限制
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:633]  2. 建议每日添加好友不超过10次
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:634]  3. 如遇到限制，请24小时后再试
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:635]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 01:47:15 [__main__] [INFO] [wechat.py:618]  程序执行完成！
2025-08-04 01:51:52 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:99]  微信客户端初始化成功
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:305]  自动回复模块微信客户端初始化成功
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:112]  微信窗口检测成功
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:118]  当前微信账号: 又是一年冬
2025-08-04 01:51:53 [__main__] [INFO] [wechat.py:133]  微信状态正常（基础模式）
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:689]  准备批量添加好友 (自动模式)...
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:150]  成功加载 3 个好友数据
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:230]  开始批量处理 3 个好友...
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:237]  使用自动检测模式
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:240]  
[1/3] 处理: user001
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:176]  开始添加好友: user001
2025-08-04 01:52:04 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:198]    搜索关键词: user001
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:199]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:201]    建议备注: 客户001
2025-08-04 01:52:04 [__main__] [INFO] [wechat.py:203]    建议标签: 客户, 潜在合作
2025-08-04 01:52:04 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user001, False, False, 0.5
2025-08-04 01:52:10 [__main__] [INFO] [wechat.py:208]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-04 01:52:10 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 10.5 秒...
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:240]  
[2/3] 处理: user002
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:176]  开始添加好友: user002
2025-08-04 01:52:20 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:198]    搜索关键词: user002
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:199]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:201]    建议备注: 行业联系人002
2025-08-04 01:52:20 [__main__] [INFO] [wechat.py:203]    建议标签: 行业, 交流
2025-08-04 01:52:20 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user002, False, False, 0.5
2025-08-04 01:52:26 [__main__] [INFO] [wechat.py:208]  ✅ user002 可能已经是好友或可以直接聊天
2025-08-04 01:52:26 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 8.5 秒...
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:240]  
[3/3] 处理: user003
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:176]  开始添加好友: user003
2025-08-04 01:52:35 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:198]    搜索关键词: user003
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:199]    验证消息: 看到您在XX群的专业分享，希望能向您请教
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:201]    建议备注: 专业人士003
2025-08-04 01:52:35 [__main__] [INFO] [wechat.py:203]    建议标签: 专业, 学习
2025-08-04 01:52:35 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user003, False, False, 0.5
2025-08-04 01:52:41 [__main__] [INFO] [wechat.py:208]  ✅ user003 可能已经是好友或可以直接聊天
2025-08-04 01:52:41 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 16.9 秒...
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:730]  
==============================
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:731]  批量处理好友完成！
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:732]  ✅ 成功: 3 人
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:733]  ❌ 失败: 0 人
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:734]  ⏭️ 跳过: 0 人
2025-08-04 01:52:58 [__main__] [INFO] [wechat.py:735]  ==============================
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:689]  准备批量添加好友 (自动模式)...
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:150]  成功加载 3 个好友数据
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:230]  开始批量处理 3 个好友...
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:237]  使用自动检测模式
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:240]  
[1/3] 处理: user001
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:176]  开始添加好友: user001
2025-08-04 01:53:47 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:198]    搜索关键词: user001
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:199]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:201]    建议备注: 客户001
2025-08-04 01:53:47 [__main__] [INFO] [wechat.py:203]    建议标签: 客户, 潜在合作
2025-08-04 01:53:47 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user001, False, False, 0.5
2025-08-04 01:53:53 [__main__] [INFO] [wechat.py:208]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-04 01:53:53 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 6.3 秒...
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:240]  
[2/3] 处理: user002
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:176]  开始添加好友: user002
2025-08-04 01:53:59 [__main__] [WARNING] [wechat.py:195]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:196]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:197]  需要添加的好友信息:
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:198]    搜索关键词: user002
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:199]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:201]    建议备注: 行业联系人002
2025-08-04 01:53:59 [__main__] [INFO] [wechat.py:203]    建议标签: 行业, 交流
2025-08-04 01:53:59 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user002, False, False, 0.5
2025-08-04 01:54:05 [__main__] [INFO] [wechat.py:208]  ✅ user002 可能已经是好友或可以直接聊天
2025-08-04 01:54:05 [__main__] [INFO] [wechat.py:256]  ⏱️ 等待 14.9 秒...
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:719]  用户中断程序
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:483]  自动回复功能已停止
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:739]  
⚠️ 重要提示:
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:740]  1. 微信对频繁添加好友有严格限制
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:741]  2. 建议每日添加好友不超过10次
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:742]  3. 如遇到限制，请24小时后再试
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:743]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 01:54:15 [__main__] [INFO] [wechat.py:726]  程序执行完成！
2025-08-04 01:54:28 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:100]  微信客户端初始化成功
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:306]  自动回复模块微信客户端初始化成功
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:113]  微信窗口检测成功
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:119]  当前微信账号: 又是一年冬
2025-08-04 01:54:28 [__main__] [INFO] [wechat.py:134]  微信状态正常（基础模式）
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:690]  准备批量添加好友 (自动模式)...
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:151]  成功加载 3 个好友数据
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:231]  开始批量处理 3 个好友...
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:238]  使用自动检测模式
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:241]  
[1/3] 处理: user001
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:177]  开始添加好友: user001
2025-08-04 01:54:33 [__main__] [WARNING] [wechat.py:196]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:197]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:198]  需要添加的好友信息:
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:199]    搜索关键词: user001
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:200]    验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:202]    建议备注: 客户001
2025-08-04 01:54:33 [__main__] [INFO] [wechat.py:204]    建议标签: 客户, 潜在合作
2025-08-04 01:54:33 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user001, False, False, 0.5
2025-08-04 01:54:40 [__main__] [INFO] [wechat.py:209]  ✅ user001 可能已经是好友或可以直接聊天
2025-08-04 01:54:40 [__main__] [INFO] [wechat.py:257]  ⏱️ 等待 5.5 秒...
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:241]  
[2/3] 处理: user002
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:177]  开始添加好友: user002
2025-08-04 01:54:45 [__main__] [WARNING] [wechat.py:196]  当前wxauto版本不支持自动添加好友的高级功能
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:197]  建议手动操作或升级到支持AddNewFriend方法的wxauto版本
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:198]  需要添加的好友信息:
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:199]    搜索关键词: user002
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:200]    验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:202]    建议备注: 行业联系人002
2025-08-04 01:54:45 [__main__] [INFO] [wechat.py:204]    建议标签: 行业, 交流
2025-08-04 01:54:45 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user002, False, False, 0.5
2025-08-04 01:54:51 [__main__] [INFO] [wechat.py:209]  ✅ user002 可能已经是好友或可以直接聊天
2025-08-04 01:54:51 [__main__] [INFO] [wechat.py:257]  ⏱️ 等待 16.6 秒...
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:720]  用户中断程序
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:484]  自动回复功能已停止
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:740]  
⚠️ 重要提示:
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:741]  1. 微信对频繁添加好友有严格限制
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:742]  2. 建议每日添加好友不超过10次
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:743]  3. 如遇到限制，请24小时后再试
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:744]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 01:54:58 [__main__] [INFO] [wechat.py:727]  程序执行完成！
2025-08-04 02:00:25 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:03:21 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:697]  ==================================================
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:698]  微信自动化工具启动
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:699]  当前版本: 开源版本 (wxauto)
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:700]  ==================================================
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:703]  ⚠️ 请确保:
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:704]  1. 微信PC版已登录且窗口打开
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:705]  2. 网络连接正常
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:706]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-04 02:03:21 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:100]  微信客户端初始化成功
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:322]  自动回复模块微信客户端初始化成功
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:113]  微信窗口检测成功
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:119]  当前微信账号: 又是一年冬
2025-08-04 02:03:21 [__main__] [INFO] [wechat.py:134]  微信状态正常（基础模式）
2025-08-04 02:03:30 [__main__] [INFO] [wechat.py:492]  自动回复功能已启动
2025-08-04 02:03:30 [__main__] [INFO] [wechat.py:493]  注意：当前版本的自动回复功能有限，建议使用手动回复模式
2025-08-04 02:03:30 [__main__] [INFO] [wechat.py:334]  成功加载自定义回复规则
2025-08-04 02:03:30 [__main__] [INFO] [wechat.py:418]  开始监控新消息...
2025-08-04 02:03:30 [__main__] [INFO] [wechat.py:419]  注意：当前版本使用简化的消息监控模式
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:744]  准备批量添加好友 (基础模式)...
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:151]  成功加载 3 个好友数据
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:247]  开始批量处理 3 个好友...
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:254]  使用自动检测模式
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:257]  
[1/3] 处理: user001
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:177]  开始添加好友: user001
2025-08-04 02:04:27 [__main__] [WARNING] [wechat.py:206]  当前使用开源版本wxauto，不支持自动添加好友功能
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:207]  💡 建议升级到Plus版本(wxautox)以获得完整的自动添加好友功能
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:208]     安装命令: pip install wxautox
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:209]  
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:210]  📋 需要添加的好友信息:
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:211]     🔍 搜索关键词: user001
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:212]     💬 验证消息: 您好！我是XX公司的客服，方便通过好友申请交流下项目吗？
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:214]     🏷️ 建议备注: 客户001
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:216]     🔖 建议标签: 客户, 潜在合作
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:218]  
2025-08-04 02:04:27 [__main__] [INFO] [wechat.py:219]  🔧 当前版本处理方式:
2025-08-04 02:04:27 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user001, False, False, 0.5
2025-08-04 02:04:33 [__main__] [INFO] [wechat.py:224]  ✅ user001 已经是好友，可以直接聊天
2025-08-04 02:04:33 [__main__] [INFO] [wechat.py:273]  ⏱️ 等待 18.5 秒...
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:257]  
[2/3] 处理: user002
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:177]  开始添加好友: user002
2025-08-04 02:04:52 [__main__] [WARNING] [wechat.py:206]  当前使用开源版本wxauto，不支持自动添加好友功能
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:207]  💡 建议升级到Plus版本(wxautox)以获得完整的自动添加好友功能
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:208]     安装命令: pip install wxautox
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:209]  
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:210]  📋 需要添加的好友信息:
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:211]     🔍 搜索关键词: user002
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:212]     💬 验证消息: 朋友介绍认识，想和您沟通下行业动态，麻烦通过下~
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:214]     🏷️ 建议备注: 行业联系人002
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:216]     🔖 建议标签: 行业, 交流
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:218]  
2025-08-04 02:04:52 [__main__] [INFO] [wechat.py:219]  🔧 当前版本处理方式:
2025-08-04 02:04:52 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user002, False, False, 0.5
2025-08-04 02:04:58 [__main__] [INFO] [wechat.py:224]  ✅ user002 已经是好友，可以直接聊天
2025-08-04 02:04:58 [__main__] [INFO] [wechat.py:273]  ⏱️ 等待 13.0 秒...
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:257]  
[3/3] 处理: user003
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:177]  开始添加好友: user003
2025-08-04 02:05:11 [__main__] [WARNING] [wechat.py:206]  当前使用开源版本wxauto，不支持自动添加好友功能
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:207]  💡 建议升级到Plus版本(wxautox)以获得完整的自动添加好友功能
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:208]     安装命令: pip install wxautox
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:209]  
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:210]  📋 需要添加的好友信息:
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:211]     🔍 搜索关键词: user003
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:212]     💬 验证消息: 看到您在XX群的专业分享，希望能向您请教
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:214]     🏷️ 建议备注: 专业人士003
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:216]     🔖 建议标签: 专业, 学习
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:218]  
2025-08-04 02:05:11 [__main__] [INFO] [wechat.py:219]  🔧 当前版本处理方式:
2025-08-04 02:05:11 [wxauto] [DEBUG] [sessionbox.py:68]  切换聊天窗口: user003, False, False, 0.5
2025-08-04 02:05:17 [__main__] [INFO] [wechat.py:224]  ✅ user003 已经是好友，可以直接聊天
2025-08-04 02:05:17 [__main__] [INFO] [wechat.py:273]  ⏱️ 等待 14.8 秒...
2025-08-04 02:05:31 [__main__] [INFO] [wechat.py:788]  
==============================
2025-08-04 02:05:31 [__main__] [INFO] [wechat.py:789]  批量处理好友完成！
2025-08-04 02:05:31 [__main__] [INFO] [wechat.py:790]  ✅ 成功: 3 人
2025-08-04 02:05:31 [__main__] [INFO] [wechat.py:791]  ❌ 失败: 0 人
2025-08-04 02:05:31 [__main__] [INFO] [wechat.py:792]  ⏭️ 跳过: 0 人
2025-08-04 02:05:31 [__main__] [INFO] [wechat.py:793]  ==============================
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:777]  用户中断程序
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:475]  消息监控已停止
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:500]  自动回复功能已停止
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:829]  
⚠️ 重要提示:
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:830]  1. 微信对频繁添加好友有严格限制
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:831]  2. 建议每日添加好友不超过10次
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:832]  3. 如遇到限制，请24小时后再试
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:833]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 02:08:46 [__main__] [INFO] [wechat.py:784]  程序执行完成！
2025-08-04 02:14:11 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:14:11 [wechat_enhanced] [INFO] [wechat_enhanced.py:99]  ✅ wxauto客户端初始化成功
2025-08-04 02:14:11 [wechat_enhanced] [INFO] [wechat_enhanced.py:110]  ✅ uiautomation客户端初始化成功
2025-08-04 02:21:30 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:21:30 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:100]  微信客户端初始化成功
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:322]  自动回复模块微信客户端初始化成功
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:113]  微信窗口检测成功
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:119]  当前微信账号: 又是一年冬
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:134]  微信状态正常（基础模式）
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:650]  正在获取微信状态信息...
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:656]  当前微信账号: 又是一年冬
2025-08-04 02:21:30 [wechat] [WARNING] [wechat.py:679]  当前wxauto版本不支持GetSessionList方法
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:680]  微信客户端状态: 已连接，但无法获取详细会话信息
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:334]  成功加载自定义回复规则
2025-08-04 02:21:30 [wechat] [INFO] [wechat.py:151]  成功加载 3 个好友数据
2025-08-04 02:21:39 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:21:39 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:100]  微信客户端初始化成功
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:322]  自动回复模块微信客户端初始化成功
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:113]  微信窗口检测成功
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:119]  当前微信账号: 又是一年冬
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:134]  微信状态正常（基础模式）
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:650]  正在获取微信状态信息...
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:656]  当前微信账号: 又是一年冬
2025-08-04 02:21:39 [wechat] [WARNING] [wechat.py:679]  当前wxauto版本不支持GetSessionList方法
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:680]  微信客户端状态: 已连接，但无法获取详细会话信息
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:334]  成功加载自定义回复规则
2025-08-04 02:21:39 [wechat] [INFO] [wechat.py:151]  成功加载 3 个好友数据
2025-08-04 02:24:59 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:697]  ==================================================
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:698]  微信自动化工具启动
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:699]  当前版本: 开源版本 (wxauto)
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:700]  ==================================================
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:703]  ⚠️ 请确保:
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:704]  1. 微信PC版已登录且窗口打开
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:705]  2. 网络连接正常
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:706]  3. 不要操作鼠标/键盘直到脚本完成
2025-08-04 02:24:59 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:100]  微信客户端初始化成功
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:322]  自动回复模块微信客户端初始化成功
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:113]  微信窗口检测成功
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:119]  当前微信账号: 又是一年冬
2025-08-04 02:24:59 [__main__] [INFO] [wechat.py:134]  微信状态正常（基础模式）
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:769]  程序退出
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:500]  自动回复功能已停止
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:829]  
⚠️ 重要提示:
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:830]  1. 微信对频繁添加好友有严格限制
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:831]  2. 建议每日添加好友不超过10次
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:832]  3. 如遇到限制，请24小时后再试
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:833]  4. 请遵守微信使用规范，避免账号风险
2025-08-04 02:25:14 [__main__] [INFO] [wechat.py:784]  程序执行完成！
2025-08-04 21:34:39 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 21:34:39 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:916]  📱 使用wxauto进行快速搜索...
2025-08-04 21:34:39 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:931]  wxauto搜索失败，回退到uiauto: 'WeChat' object has no attribute 'Search'
2025-08-04 21:34:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:92]  微信窗口已激活
2025-08-04 21:34:40 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:132]  找到搜索框: 搜索
2025-08-04 21:34:45 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:151]  搜索联系人 (标准方法): Ybycy19940805
2025-08-04 21:34:48 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:815]  检查是否出现'无法找到该用户'弹窗...
2025-08-04 21:37:24 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:834]  ✅ 发现'无法找到用户'弹窗: 用户不存在
2025-08-04 21:37:24 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:867]  尝试关闭'无法找到用户'弹窗...
2025-08-04 21:39:31 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:879]  ✅ 点击了确定按钮: 关闭
2025-08-04 21:51:09 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 21:51:09 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:917]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-04 21:51:10 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:92]  微信窗口已激活
2025-08-04 21:51:11 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:928]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-04 21:51:12 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:92]  微信窗口已激活
2025-08-04 21:51:12 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:132]  找到搜索框: 搜索
2025-08-04 21:51:17 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:151]  搜索联系人 (标准方法): Ybycy19940805
2025-08-04 21:51:19 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:939]  🎯 步骤2: 使用uiauto点击'网络查找微信号'按钮...
2025-08-04 21:51:59 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:68]  找到微信窗口
2025-08-04 21:51:59 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:910]  🔄 使用混合模式添加好友: 183000320
2025-08-04 21:51:59 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 21:51:59 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:917]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-04 21:52:00 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:92]  微信窗口已激活
2025-08-04 21:52:01 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:928]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-04 21:52:02 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:92]  微信窗口已激活
2025-08-04 21:52:02 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:132]  找到搜索框: 搜索
2025-08-04 21:52:07 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:151]  搜索联系人 (标准方法): 183000320
2025-08-04 21:52:09 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:939]  🎯 步骤2: 使用uiauto点击'网络查找微信号'按钮...
2025-08-04 21:52:49 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:68]  找到微信窗口
2025-08-04 21:52:49 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:910]  🔄 使用混合模式添加好友: Baby66ovo
2025-08-04 21:52:49 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-08-04 21:52:49 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:917]  📱 步骤1: 使用wxauto快速输入微信号...
2025-08-04 21:52:50 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:92]  微信窗口已激活
2025-08-04 21:52:51 [wechat_uiautomation] [WARNING] [wechat_uiautomation.py:928]  wxauto输入失败，回退到uiauto输入: 'WeChat' object has no attribute 'Search'
2025-08-04 21:52:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:92]  微信窗口已激活
2025-08-04 21:52:52 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:132]  找到搜索框: 搜索
2025-08-04 21:52:57 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:151]  搜索联系人 (标准方法): Baby66ovo
2025-08-04 21:52:59 [wechat_uiautomation] [INFO] [wechat_uiautomation.py:939]  🎯 步骤2: 使用uiauto点击'网络查找微信号'按钮...
