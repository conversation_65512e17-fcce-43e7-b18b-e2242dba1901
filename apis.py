# 交互文件
import time
import threading


# 核心库导入
try:
    import openpyxl
except ImportError as e:
    print(f"⚠️ openpyxl库导入失败: {e}")
    openpyxl = None

try:
    from sqlite3_util import (
        batch_insert, query_users, get_users_count, verify_insert_result,
        query_wechat_phrases, add_wechat_phrase, update_wechat_phrase, delete_wechat_phrase,
        add_user_log, query_user_logs
    )
except ImportError as e:
    print(f"⚠️ sqlite3_util模块导入失败: {e}")
    batch_insert = query_users = get_users_count = verify_insert_result = None
    query_wechat_phrases = add_wechat_phrase = update_wechat_phrase = delete_wechat_phrase = None
    add_user_log = query_user_logs = None

# 微信自动化导入
try:
    from wechat_uiautomation import WeChatUIAutomation
    WECHAT_AUTOMATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 微信自动化模块导入失败: {e}")
    WeChatUIAutomation = None
    WECHAT_AUTOMATION_AVAILABLE = False

# 全局控制台输出变量
GLOBAL_CONSOLE_OUTPUT = {
    "logs": [],  # 控制台日志列表
    "is_processing": False,  # 是否正在处理
    "current_status": "idle",  # 当前状态: idle, processing, completed, error
    "current_message": "",  # 当前状态消息
    "progress": 0,  # 进度百分比 0-100
    "last_update": time.time()
}

# 全局取消标志
PROCESSING_CANCELLED = False

# 线程锁
console_lock = threading.Lock()

def add_console_log(message, log_type="info"):
    """添加控制台日志"""
    global GLOBAL_CONSOLE_OUTPUT

    with console_lock:
        log_entry = {
            "timestamp": time.time(),
            "time_str": time.strftime("%H:%M:%S", time.localtime()),
            "message": message,
            "type": log_type  # info, success, warning, error
        }

        GLOBAL_CONSOLE_OUTPUT["logs"].append(log_entry)

        # 限制日志数量，只保留最新50条
        if len(GLOBAL_CONSOLE_OUTPUT["logs"]) > 50:
            GLOBAL_CONSOLE_OUTPUT["logs"] = GLOBAL_CONSOLE_OUTPUT["logs"][-50:]

        GLOBAL_CONSOLE_OUTPUT["last_update"] = time.time()

        # 同时打印到控制台
        print(f"[{log_entry['time_str']}] {message}")

def update_console_status(status="idle", message="", progress=0, is_processing=False):
    """更新控制台状态"""
    global GLOBAL_CONSOLE_OUTPUT

    with console_lock:
        GLOBAL_CONSOLE_OUTPUT["current_status"] = status
        GLOBAL_CONSOLE_OUTPUT["current_message"] = message
        GLOBAL_CONSOLE_OUTPUT["progress"] = progress
        GLOBAL_CONSOLE_OUTPUT["is_processing"] = is_processing
        GLOBAL_CONSOLE_OUTPUT["last_update"] = time.time()

def get_console_output():
    """获取控制台输出"""
    global GLOBAL_CONSOLE_OUTPUT

    with console_lock:
        return GLOBAL_CONSOLE_OUTPUT.copy()

def reset_console_output():
    """重置控制台输出"""
    global GLOBAL_CONSOLE_OUTPUT

    with console_lock:
        GLOBAL_CONSOLE_OUTPUT["logs"] = []
        GLOBAL_CONSOLE_OUTPUT["is_processing"] = False
        GLOBAL_CONSOLE_OUTPUT["current_status"] = "idle"
        GLOBAL_CONSOLE_OUTPUT["current_message"] = ""
        GLOBAL_CONSOLE_OUTPUT["progress"] = 0
        GLOBAL_CONSOLE_OUTPUT["last_update"] = time.time()

def cancel_processing():
    """取消当前处理"""
    global PROCESSING_CANCELLED
    PROCESSING_CANCELLED = True
    add_console_log("🛑 用户取消处理", "warning")
    update_console_status(status="cancelled", message="处理已取消", is_processing=False)

def is_processing_cancelled():
    """检查是否已取消处理"""
    return PROCESSING_CANCELLED

def reset_cancel_flag():
    """重置取消标志"""
    global PROCESSING_CANCELLED
    PROCESSING_CANCELLED = False

def check_api_response_valid(real_data):
    """检查API响应是否有效，判断是否被风控"""
    if not real_data:
        return False, "API返回空数据"

    if not isinstance(real_data, dict):
        return False, "API返回数据格式错误"

    # 检查关键字段
    if not real_data.get('signature') or not real_data.get('unique_id'):
        return False, "API返回数据不完整，可能被风控或登录失效"

    return True, "数据有效"
class API:
    def __init__(self):
        pass

    def get_console_output(self):
        """获取控制台输出"""
        return get_console_output()

    def reset_console_output(self):
        """重置控制台输出"""
        return reset_console_output()

    def cancel_processing(self):
        """取消当前处理"""
        return cancel_processing()

    def check_token_status(self):
        """检查token状态"""
        try:
            from cmm import get_latest_token
            token = get_latest_token()

            if token:
                return {
                    "success": True,
                    "has_token": True,
                    "token_preview": f"{token[:20]}...",
                    "message": "Token可用"
                }
            else:
                return {
                    "success": True,
                    "has_token": False,
                    "message": "未找到有效Token"
                }
        except Exception as e:
            return {
                "success": False,
                "has_token": False,
                "message": f"检查Token失败: {str(e)}"
            }



    def get_users_data(self, page=1, page_size=200, search_params=None):
        """
        获取用户数据（分页+搜索）
        """
        try:
            print(f"=== 查询用户数据 ===")
            print(f"页码: {page}, 每页: {page_size}")
            if search_params:
                print(f"搜索条件: {search_params}")

            # 首先检查数据库和表是否存在
            import os
            db_path = 'system.db'
            if not os.path.exists(db_path):
                print(f"❌ 数据库文件不存在: {db_path}")
                return {
                    "success": False,
                    "message": "数据库文件不存在，请先上传Excel文件",
                    "data": [],
                    "total": 0
                }

            # 检查表是否存在
            conn = __import__('sqlite3').connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            table_exists = cursor.fetchone() is not None

            if not table_exists:
                conn.close()
                print(f"❌ users表不存在")
                return {
                    "success": False,
                    "message": "users表不存在，请先上传Excel文件",
                    "data": [],
                    "total": 0
                }

            # 检查表中是否有数据
            cursor.execute("SELECT COUNT(*) FROM users")
            total_records = cursor.fetchone()[0]
            print(f"📊 数据库中总记录数: {total_records}")
            conn.close()

            # 构建搜索条件
            where_conditions = []
            params = []

            if search_params:
                if search_params.get('fileName'):
                    where_conditions.append("file_name LIKE ?")
                    params.append(f"%{search_params['fileName']}%")

                if search_params.get('description'):
                    where_conditions.append("intro LIKE ?")
                    params.append(f"%{search_params['description']}%")

                if search_params.get('phone'):
                    # 这里假设phone字段存在，如果没有可以搜索unique_id
                    where_conditions.append("unique_id LIKE ?")
                    params.append(f"%{search_params['phone']}%")

                if search_params.get('wechat'):
                    # 这里假设wechat字段存在，如果没有可以搜索username
                    where_conditions.append("username LIKE ?")
                    params.append(f"%{search_params['wechat']}%")

            # 构建WHERE子句
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # 查询总记录数
            conn = __import__('sqlite3').connect('system.db')
            conn.row_factory = __import__('sqlite3').Row
            cursor = conn.cursor()

            count_sql = f"SELECT COUNT(*) FROM users {where_clause}"
            cursor.execute(count_sql, params)
            total_count = cursor.fetchone()[0]
            print(f"符合条件的总记录数: {total_count}")

            # 计算偏移量
            offset = (page - 1) * page_size

            # 查询数据
            data_sql = f"""
                SELECT * FROM users
                {where_clause}
                ORDER BY create_time DESC
                LIMIT ? OFFSET ?
            """

            cursor.execute(data_sql, params + [page_size, offset])
            results = [dict(row) for row in cursor.fetchall()]
            conn.close()

            add_console_log(f"📊 查询到 {len(results)} 条记录", "info")
            for i, record in enumerate(results[:3], 1):
                add_console_log(f"  {i}. {record.get('username')} - {record.get('cmm_id')}", "info")

            return {
                "success": True,
                "data": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "search_params": search_params
            }

        except Exception as e:
            print(f"查询用户数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": [],
                "total": 0
            }

    # 处理文件内容
    def process_file(self, file_data):
        """
        处理前端传来的文件数据，直接在内存中读取Excel内容
        :param file_data: 包含文件信息和内容的字典
        :return: 处理结果
        """
        # 检查必要的库
        if openpyxl is None:
            return {
                "success": False,
                "message": "openpyxl库未安装，无法处理Excel文件"
            }

        try:
            # 重置取消标志
            reset_cancel_flag()

            # 更新状态：开始处理
            update_console_status(status="processing", message="开始处理Excel文件...", is_processing=True)
            add_console_log("🚀 开始处理Excel文件...", "info")

            if not file_data:
                add_console_log("❌ 未接收到文件数据", "error")
                update_console_status(status="error", message="未接收到文件数据", is_processing=False)
                return {
                    "success": False,
                    "message": "未接收到文件数据"
                }

            # 检查必要字段
            required_fields = ['name', 'content']
            for field in required_fields:
                if field not in file_data:
                    add_console_log(f"❌ 缺少必要字段: {field}", "error")
                    update_console_status(status="error", message=f"缺少必要字段: {field}", is_processing=False)
                    return {
                        "success": False,
                        "message": f"缺少必要字段: {field}"
                    }

            add_console_log(f"📄 读取文件: {file_data['name']}", "info")
            add_console_log(f"📊 文件大小: {file_data.get('size', 'unknown')} bytes", "info")

            # 将字节数组转换为字节对象
            file_content = bytes(file_data['content'])
            print(f"转换后的字节长度: {len(file_content)}")

            # 使用BytesIO在内存中创建文件对象
            from io import BytesIO
            # 文件
            file_stream = BytesIO(file_content)

            # 使用openpyxl直接从内存读取Excel文件
            add_console_log("📊 开始解析Excel文件结构", "info")
            update_console_status(status="processing", message="正在解析Excel文件...", progress=10)
            workbook = openpyxl.load_workbook(file_stream)

            # 获取工作表信息
            sheet_names = workbook.sheetnames
            add_console_log(f"📋 发现 {len(sheet_names)} 个工作表", "success")

            # 读取第一个工作表
            sheet = workbook.active
            add_console_log(f"📄 当前工作表: {sheet.title} ({sheet.max_row}行 x {sheet.max_column}列)", "info")

            # 读取表头（第一行）
            headers = []
            for col in range(1, sheet.max_column + 1):
                cell_value = sheet.cell(row=1, column=col).value
                headers.append(str(cell_value) if cell_value else f"列{col}")

            add_console_log(f"📝 表头: {', '.join(headers[:5])}{'...' if len(headers) > 5 else ''}", "info")

            # 读取所有数据，包括超链接
            add_console_log("🔍 开始扫描数据和超链接", "info")
            update_console_status(status="processing", message="正在扫描数据和超链接...", progress=20)
            all_data = []
            hyperlinks_found = []

            for row in range(2, sheet.max_row + 1):  # 从第2行开始
                row_data = []
                row_hyperlinks = []

                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=row, column=col)
                    cell_value = str(cell.value) if cell.value else ""
                    row_data.append(cell_value)

                    # 检查是否有超链接
                    if cell.hyperlink:
                        hyperlink_info = {
                            'row': row,
                            'col': col,
                            'column_name': headers[col-1] if col-1 < len(headers) else f"列{col}",
                            'cell_value': cell_value,
                            'hyperlink': cell.hyperlink.target if cell.hyperlink.target else str(cell.hyperlink)
                        }
                        row_hyperlinks.append(hyperlink_info)
                        hyperlinks_found.append(hyperlink_info)
                        print(f"发现超链接 - 行{row}列{col}({headers[col-1] if col-1 < len(headers) else f'列{col}'}): {cell_value} -> {hyperlink_info['hyperlink']}")

                all_data.append(row_data)

                # 只打印前5行作为示例
                if row <= 6:
                    print(f"第{row}行: {row_data}")
                    if row_hyperlinks:
                        for link in row_hyperlinks:
                            print(f"  └─ 超链接: {link['cell_value']} -> {link['hyperlink']}")

            add_console_log(f"✅ 数据扫描完成！发现 {len(hyperlinks_found)} 个超链接", "success")
            update_console_status(status="processing", message=f"发现 {len(hyperlinks_found)} 个超链接", progress=40)

            # 处理超链接
            if hyperlinks_found:
                add_console_log(f"🔗 开始处理 {len(hyperlinks_found)} 个超链接", "info")

                # 提取ID并组装SQLite数据
                import re
                from datetime import datetime

                sqlite_data = []
                extracted_ids = []
                insert_success = False
                insert_message = "未执行数据库插入"

                # 获取爬取配置
                crawl_config = file_data.get('crawlConfig', {})
                sleep_interval = crawl_config.get('sleepInterval', 3)  # 默认3秒
                add_console_log(f"⚙️ 爬取配置 - 休眠间隔: {sleep_interval}秒", "info")

                # 获取最新token用于API调用
                add_console_log("🔑 获取最新token...", "info")
                token = self.get_latest_token_from_db()
                if not token:
                    add_console_log("❌ 未找到有效token，无法处理Excel文件", "error")
                    update_console_status(status="error", message="未找到有效token，请先登录蝉妈妈", is_processing=False)
                    return {
                        "success": False,
                        "message": "未找到有效token，请先登录蝉妈妈账号",
                        "data": None
                    }
                else:
                    add_console_log(f"✅ 获取到token: {token[:20]}...", "success")

                import time

                # 分批处理，每批返回进度
                batch_size = 5  # 每5个为一批
                total_count = len(hyperlinks_found)
                processed_count = 0

                for i, link in enumerate(hyperlinks_found, 1):
                    # 检查是否取消处理
                    if is_processing_cancelled():
                        add_console_log("🛑 处理已被用户取消", "warning")
                        update_console_status(status="cancelled", message="处理已取消", is_processing=False)
                        return {
                            "success": False,
                            "message": "处理已取消",
                            "cancelled": True
                        }

                    # 更新进度
                    progress = 40 + (i / len(hyperlinks_found)) * 50  # 40-90%
                    update_console_status(status="processing", message=f"正在获取第 {i}/{len(hyperlinks_found)} 个达人信息", progress=progress)

                    # 提取authorDetail/后面的ID
                    url = link['hyperlink']
                    match = re.search(r'authorDetail/([^/?]+)', url)
                    if match:
                        author_id = match.group(1)
                        extracted_ids.append(author_id)

                        add_console_log(f"📡 [{i}/{len(hyperlinks_found)}] 获取达人: {link['cell_value']} ({author_id})", "info")

                        # 调用get_real_info获取真实数据
                        real_intro = ""
                        real_unique_id = ""
                        real_code = ""

                        # 使用token调用API获取真实数据
                        try:
                                from cmm import get_real_info, extract_contact_code
                                real_data = get_real_info(author_id, token)

                                # 检查API响应是否有效（风控检测）
                                is_valid, error_msg = check_api_response_valid(real_data)

                                if is_valid:
                                    real_intro = real_data['signature']
                                    real_unique_id = real_data['unique_id']

                                    # 从signature中提取联系方式
                                    real_code = extract_contact_code(real_intro)

                                    add_console_log(f"✅ 获取成功: {link['cell_value']} | 抖音ID: {real_unique_id}", "success")
                                    if real_code:
                                        add_console_log(f"📞 提取联系方式: {real_code}", "success")
                                    else:
                                        add_console_log("📞 未提取到联系方式", "warning")
                                else:
                                    # 检测到风控或登录失效，删除token并终止处理
                                    add_console_log(f"❌ {error_msg}", "error")
                                    add_console_log("🛑 检测到风控或登录失效，正在清除token...", "error")

                                    # 删除数据库中的token
                                    try:
                                        import sqlite3
                                        conn = sqlite3.connect('system.db')
                                        cursor = conn.cursor()
                                        cursor.execute("DELETE FROM tokens")
                                        conn.commit()
                                        deleted_count = cursor.rowcount
                                        conn.close()
                                        add_console_log(f"🗑️ 已清除 {deleted_count} 个失效token", "warning")
                                    except Exception as token_error:
                                        add_console_log(f"❌ 清除token失败: {str(token_error)}", "error")

                                    add_console_log("🛑 处理终止，请重新登录蝉妈妈", "error")
                                    update_console_status(status="error", message="token失效，请重新登录", is_processing=False)
                                    return {
                                        "success": False,
                                        "message": f"检测到风控或登录失效: {error_msg}，请重新登录蝉妈妈",
                                        "risk_control": True,
                                        "token_cleared": True
                                    }

                        except Exception as e:
                            add_console_log(f"❌ API调用失败: {str(e)[:50]}...", "error")

                        data_row = {
                            'file_name': file_data['name'],
                            'username': link['cell_value'],
                            'intro': real_intro,
                            'unique_id': real_unique_id,
                            'cmm_id': author_id,
                            'code': real_code,
                            'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }

                        sqlite_data.append(data_row)
                        processed_count += 1

                        add_console_log(f"✅ 完成第 {i} 个达人信息获取: {link['cell_value']}", "success")

                        # 添加休眠间隔，避免请求过快
                        if i < len(hyperlinks_found):  # 最后一个不需要休眠
                            add_console_log(f"⏱️ 休眠 {sleep_interval} 秒，避免请求过快", "warning")
                            time.sleep(sleep_interval)

                add_console_log(f"📊 提取到 {len(extracted_ids)} 个ID", "info")

                # 生成批量插入SQL
                if sqlite_data:
                    add_console_log("💾 开始准备数据库插入", "info")
                    print("-- users 表结构")
                    print("CREATE TABLE IF NOT EXISTS users (")
                    print("    id INTEGER PRIMARY KEY AUTOINCREMENT,")
                    print("    file_name TEXT NOT NULL,  -- 原始文件名")
                    print("    username TEXT NOT NULL,   -- 用户昵称")
                    print("    intro TEXT,               -- 简介信息")
                    print("    unique_id TEXT,           -- 抖音ID")
                    print("    cmm_id TEXT,              -- 蝉妈妈ID")
                    print("    create_time TEXT NOT NULL -- 创建时间")
                    print(");")
                    print()

                    # 批量插入语句
                    print("-- 批量插入数据")
                    print("INSERT INTO users (file_name, username, intro, unique_id, cmm_id, code, create_time) VALUES")
                    values_list = []
                    for data in sqlite_data:
                        values = f"('{data['file_name']}', '{data['username']}', '{data['intro']}', '{data['unique_id']}', '{data['cmm_id']}', '{data['code']}', '{data['create_time']}')"
                        values_list.append(values)

                    print(",\n".join(values_list) + ";")

                    print(f"\n总共 {len(sqlite_data)} 条数据准备插入到 users 表")

                    # 详细数据预览
                    print(f"\n=== 数据详情预览 ===")
                    for i, data in enumerate(sqlite_data[:5], 1):  # 只显示前5条
                        print(f"{i}. 文件: {data['file_name']}")
                        print(f"   用户: {data['username']}")
                        print(f"   简介: {data['intro'][:50]}...")
                        print(f"   抖音ID: {data['unique_id']}")
                        print(f"   蝉妈妈ID: {data['cmm_id']}")
                        print(f"   联系方式: {data['code'] if data['code'] else '未提取'}")
                        print(f"   时间: {data['create_time']}")
                        print()

                    if len(sqlite_data) > 5:
                        print(f"... 还有 {len(sqlite_data) - 5} 条数据")

                    # 插入数据到数据库
                    print(f"\n=== 开始插入数据到数据库 ===")
                    try:
                        # 准备插入数据
                        field_names = ['file_name', 'username', 'intro', 'unique_id', 'cmm_id', 'code', 'create_time']
                        insert_data = []

                        for data in sqlite_data:
                            row_tuple = (
                                data['file_name'],
                                data['username'],
                                data['intro'],
                                data['unique_id'],
                                data['cmm_id'],
                                data['code'],
                                data['create_time']
                            )
                            insert_data.append(row_tuple)

                        print(f"准备插入 {len(insert_data)} 条数据到 users 表...")

                        # 执行批量插入
                        inserted_count = batch_insert(
                            db_path='system.db',
                            table_name='users',
                            field_names=field_names,
                            data=insert_data,
                            batch_size=50
                        )

                        if inserted_count > 0:
                            add_console_log(f"✅ 成功插入 {inserted_count} 条数据到数据库", "success")

                            # 验证插入结果
                            verify_result = verify_insert_result()
                            add_console_log(f"📊 数据库总记录数: {verify_result.get('record_count', 0)}", "info")

                            if verify_result.get('latest_records'):
                                print(f"最新5条记录:")
                                for i, record in enumerate(verify_result['latest_records'][:3], 1):
                                    print(f"  {i}. ID:{record.get('id')} 用户:{record.get('username')} 蝉妈妈ID:{record.get('cmm_id')}")

                            insert_success = True
                            insert_message = f"成功插入 {inserted_count} 条数据到数据库，当前总记录数: {verify_result.get('record_count', 0)}"
                        else:
                            add_console_log("❌ 数据插入失败", "error")
                            insert_success = False
                            insert_message = "数据插入失败"

                    except Exception as insert_error:
                        add_console_log(f"❌ 数据库插入异常: {str(insert_error)}", "error")
                        import traceback
                        traceback.print_exc()
                        insert_success = False
                        insert_message = f"数据库插入异常: {str(insert_error)}"

            else:
                add_console_log("⚠️ 未发现任何超链接", "warning")

            # 关闭工作簿
            workbook.close()

            print("=" * 50)
            print("Excel文件读取完成！")

            # 准备返回数据
            return_data = {
                "filename": file_data['name'],
                "sheet_names": sheet_names,
                "current_sheet": sheet.title,
                "max_row": sheet.max_row,
                "max_column": sheet.max_column,
                "headers": headers,
                "sample_data": all_data[:5],  # 前5行作为示例
                "total_rows": len(all_data),
                "hyperlinks_count": len(hyperlinks_found),
                "hyperlinks": hyperlinks_found
            }

            # 如果有超链接，添加提取的ID和SQLite数据
            if hyperlinks_found and 'extracted_ids' in locals():
                return_data.update({
                    "extracted_ids": extracted_ids,
                    "sqlite_data": sqlite_data,
                    "sqlite_ready_count": len(sqlite_data),
                    "db_insert_success": insert_success,
                    "db_insert_message": insert_message
                })

            # 处理完成
            add_console_log("🎉 Excel文件处理完成！所有达人信息已获取", "success")
            update_console_status(status="completed", message="Excel文件处理完成！", progress=100, is_processing=False)

            # 延迟3秒后重置控制台，让用户看到完成信息
            import threading
            def delayed_reset():
                import time
                time.sleep(3)
                reset_console_output()
                add_console_log("📋 控制台已重置，可以处理新文件", "info")

            threading.Thread(target=delayed_reset, daemon=True).start()

            return {
                "success": True,
                "message": "Excel文件读取成功",
                "data": return_data
            }

        except Exception as e:
            add_console_log(f"❌ 处理失败: {str(e)}", "error")
            update_console_status(status="error", message=f"处理失败: {str(e)}", is_processing=False)
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"Excel文件处理失败: {str(e)}",
                "error": str(e)
            }

    def api_login_cmm(self, username, password):
        """
        蝉妈妈登录API接口
        """
        try:
            print(f"=== 蝉妈妈登录 ===")
            print(f"用户名: {username}")

            # 验证输入
            if not username or not password:
                return {
                    "success": False,
                    "message": "用户名和密码不能为空",
                    "logged_in": False,
                    "token": ''
                }

            # 调用cmm.py的登录方法
            from cmm import login_cmm
            result = login_cmm(username, password)

            if result and result.get('data', {}).get('logged_in'):
                print("✅ 蝉妈妈登录成功")

                return {
                    "success": True,
                    "message": "登录成功",
                    "data": result.get('data', {}),
                    "logged_in": result.get('data', {}).get('logged_in', False),
                    "token": result.get('data', {}).get('token', '')
                }
            else:
                print("❌ 蝉妈妈登录失败")
                error_msg = "登录失败，请检查用户名和密码"
                if result and result.get('message'):
                    error_msg = result.get('message')

                return {
                    "success": False,
                    "message": error_msg,
                    "data": result,
                    "logged_in": False,
                    "token": ''
                }

        except Exception as e:
            print(f"❌ 蝉妈妈登录异常: {str(e)}")
            return {
                "success": False,
                "message": f"登录异常: {str(e)}",
                "logged_in": False,
                "token": ''
            }

    def get_latest_token_from_db(self):
        """
        从数据库获取最新token
        """
        try:
            import sqlite3
            conn = sqlite3.connect('system.db')
            cursor = conn.cursor()

            cursor.execute("""
                SELECT token FROM tokens
                ORDER BY create_time DESC
                LIMIT 1
            """)
            result = cursor.fetchone()
            conn.close()

            if result:
                return result[0]
            else:
                return None
        except Exception as e:
            print(f"❌ 从数据库获取token失败: {str(e)}")
            return None

    def get_processing_status(self):
        """
        获取处理状态（用于前端显示进度）
        """
        import time
        return {
            "status": "ready",
            "message": "系统就绪",
            "timestamp": time.time()
        }

    def update_processing_progress(self, current=None, total=None, message=""):
        """
        更新处理进度并返回全局控制台输出
        """
        # 如果传入了参数，更新全局状态
        if current is not None and total is not None:
            progress_percent = round((current / total) * 100, 1) if total > 0 else 0
            update_console_status(
                status="processing",
                message=message,
                progress=progress_percent,
                is_processing=True
            )
            print('更新被调用····')
        # 返回全局控制台输出
        return get_console_output()

    def save_export_file(self, file_content, file_name, file_type='csv'):
        """
        保存导出文件到用户指定位置（使用pywebview文件对话框）
        """
        import os

        try:
            print(f"=== 保存导出文件 ===")
            print(f"文件名: {file_name}")
            print(f"文件类型: {file_type}")
            print(f"内容长度: {len(file_content)} 字符")

            # 尝试使用pywebview的文件对话框
            try:
                import webview

                # 设置文件类型过滤器
                if file_type.lower() == 'csv':
                    file_types = ('CSV文件 (*.csv)', '*.csv')
                elif file_type.lower() == 'xlsx':
                    file_types = ('Excel文件 (*.xlsx)', '*.xlsx')
                else:
                    file_types = ('所有文件 (*.*)', '*.*')

                # 使用pywebview的保存文件对话框
                file_path = webview.windows[0].create_file_dialog(
                    webview.SAVE_DIALOG,
                    directory=os.path.expanduser('~/Downloads'),  # 默认下载文件夹
                    save_filename=file_name,
                    file_types=(file_types,)
                )

                if not file_path:
                    print("用户取消了文件保存")
                    return {
                        "success": False,
                        "message": "用户取消了文件保存"
                    }

                # file_path可能是列表，取第一个
                if isinstance(file_path, (list, tuple)):
                    file_path = file_path[0] if file_path else None

                if not file_path:
                    return {
                        "success": False,
                        "message": "未选择保存路径"
                    }

                print(f"保存路径: {file_path}")

            except Exception as webview_error:
                print(f"⚠️ pywebview文件对话框失败: {str(webview_error)}")

                # 备用方案：直接保存到下载文件夹
                downloads_dir = os.path.expanduser('~/Downloads')
                if not os.path.exists(downloads_dir):
                    downloads_dir = os.path.expanduser('~')  # 用户主目录

                file_path = os.path.join(downloads_dir, file_name)
                print(f"使用默认路径: {file_path}")

            # 保存文件
            if file_type.lower() == 'csv':
                # CSV文件需要UTF-8编码和BOM
                with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
                    f.write(file_content)
            else:
                # 其他文件类型
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(file_content)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ 文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"文件已保存到: {file_path}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ 文件保存失败")
                return {
                    "success": False,
                    "message": "文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存文件失败: {str(e)}"
            }

    def save_to_downloads(self, file_content, file_name, file_type='csv'):
        """
        直接保存文件到Downloads文件夹（最简单的方案）
        """
        import os

        try:
            print(f"=== 直接保存到Downloads文件夹 ===")
            print(f"文件名: {file_name}")
            print(f"文件类型: {file_type}")
            print(f"内容长度: {len(file_content)} 字符")

            # 获取Downloads文件夹路径
            downloads_dir = os.path.expanduser('~/Downloads')
            if not os.path.exists(downloads_dir):
                # 如果Downloads文件夹不存在，使用用户主目录
                downloads_dir = os.path.expanduser('~')
                print(f"Downloads文件夹不存在，使用主目录: {downloads_dir}")

            # 构建完整文件路径
            file_path = os.path.join(downloads_dir, file_name)

            # 如果文件已存在，添加序号
            base_name, ext = os.path.splitext(file_path)
            counter = 1
            while os.path.exists(file_path):
                file_path = f"{base_name}_{counter}{ext}"
                counter += 1

            print(f"保存路径: {file_path}")

            # 保存文件
            if file_type.lower() == 'csv':
                # CSV文件需要UTF-8编码和BOM
                with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
                    f.write(file_content)
            else:
                # 其他文件类型
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(file_content)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ 文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"文件已保存到Downloads文件夹: {os.path.basename(file_path)}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ 文件保存失败")
                return {
                    "success": False,
                    "message": "文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存文件失败: {str(e)}"
            }

    def save_excel_file(self, base64_content, file_name):
        """
        保存Excel文件（从Base64内容，使用pywebview文件对话框）
        """
        import os
        import base64

        try:
            print(f"=== 保存Excel文件 ===")
            print(f"文件名: {file_name}")
            print(f"Base64内容长度: {len(base64_content)} 字符")

            # 尝试使用pywebview的文件对话框
            try:
                import webview

                # 使用pywebview的保存文件对话框
                file_path = webview.windows[0].create_file_dialog(
                    webview.SAVE_DIALOG,
                    directory=os.path.expanduser('~/Downloads'),  # 默认下载文件夹
                    save_filename=file_name,
                    file_types=(('Excel文件 (*.xlsx)', '*.xlsx'),)
                )

                if not file_path:
                    print("用户取消了Excel文件保存")
                    return {
                        "success": False,
                        "message": "用户取消了文件保存"
                    }

                # file_path可能是列表，取第一个
                if isinstance(file_path, (list, tuple)):
                    file_path = file_path[0] if file_path else None

                if not file_path:
                    return {
                        "success": False,
                        "message": "未选择保存路径"
                    }

                print(f"保存路径: {file_path}")

            except Exception as webview_error:
                print(f"⚠️ pywebview文件对话框失败: {str(webview_error)}")

                # 备用方案：直接保存到下载文件夹
                downloads_dir = os.path.expanduser('~/Downloads')
                if not os.path.exists(downloads_dir):
                    downloads_dir = os.path.expanduser('~')  # 用户主目录

                file_path = os.path.join(downloads_dir, file_name)
                print(f"使用默认路径: {file_path}")

            # 解码Base64并保存文件
            excel_data = base64.b64decode(base64_content)

            with open(file_path, 'wb') as f:
                f.write(excel_data)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ Excel文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"Excel文件已保存到: {file_path}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ Excel文件保存失败")
                return {
                    "success": False,
                    "message": "Excel文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存Excel文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存Excel文件失败: {str(e)}"
            }

    def save_excel_to_downloads(self, base64_content, file_name):
        """
        直接保存Excel文件到Downloads文件夹
        """
        import os
        import base64

        try:
            print(f"=== 直接保存Excel到Downloads文件夹 ===")
            print(f"文件名: {file_name}")
            print(f"Base64内容长度: {len(base64_content)} 字符")

            # 获取Downloads文件夹路径
            downloads_dir = os.path.expanduser('~/Downloads')
            if not os.path.exists(downloads_dir):
                # 如果Downloads文件夹不存在，使用用户主目录
                downloads_dir = os.path.expanduser('~')
                print(f"Downloads文件夹不存在，使用主目录: {downloads_dir}")

            # 构建完整文件路径
            file_path = os.path.join(downloads_dir, file_name)

            # 如果文件已存在，添加序号
            base_name, ext = os.path.splitext(file_path)
            counter = 1
            while os.path.exists(file_path):
                file_path = f"{base_name}_{counter}{ext}"
                counter += 1

            print(f"保存路径: {file_path}")

            # 解码Base64并保存文件
            excel_data = base64.b64decode(base64_content)

            with open(file_path, 'wb') as f:
                f.write(excel_data)

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ Excel文件保存成功")
                print(f"   路径: {file_path}")
                print(f"   大小: {file_size} 字节")

                return {
                    "success": True,
                    "message": f"Excel文件已保存到Downloads文件夹: {os.path.basename(file_path)}",
                    "file_path": file_path,
                    "file_size": file_size
                }
            else:
                print(f"❌ Excel文件保存失败")
                return {
                    "success": False,
                    "message": "Excel文件保存失败"
                }

        except Exception as e:
            print(f"❌ 保存Excel文件异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"保存Excel文件失败: {str(e)}"
            }

    def api_logout_cmm(self):
        """
        蝉妈妈退出登录 - 清除数据库中的token
        """
        try:
            import sqlite3

            # 连接数据库
            conn = sqlite3.connect('system.db')
            cursor = conn.cursor()

            # 删除所有token记录
            cursor.execute("DELETE FROM tokens")
            conn.commit()

            deleted_count = cursor.rowcount
            conn.close()

            print(f"已清除数据库中的 {deleted_count} 个token记录")

            return {
                "success": True,
                "message": f"已清除 {deleted_count} 个token记录"
            }

        except Exception as e:
            print(f"清除数据库token失败: {str(e)}")
            return {
                "success": False,
                "message": f"清除token失败: {str(e)}"
            }

    # ==================== 微信自动化相关方法 ====================

    def __init_wechat_automation(self):
        """初始化微信自动化实例"""
        if not WECHAT_AUTOMATION_AVAILABLE:
            return None

        try:
            wechat = WeChatUIAutomation()
            if wechat.find_wechat_window():
                return wechat
            else:
                return None
        except Exception as e:
            print(f"初始化微信自动化失败: {e}")
            return None

    def check_wechat_status(self):
        """检查微信连接状态"""
        try:
            if not WECHAT_AUTOMATION_AVAILABLE:
                return {
                    "success": False,
                    "message": "微信自动化模块不可用，请检查依赖安装"
                }

            wechat = self.__init_wechat_automation()
            if wechat:
                return {
                    "success": True,
                    "message": "微信连接正常",
                    "wechat_window_found": True
                }
            else:
                return {
                    "success": False,
                    "message": "未找到微信窗口，请确保微信PC版已打开并登录",
                    "wechat_window_found": False
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"检查微信状态失败: {str(e)}"
            }

    def add_wechat_friend(self, params):
        """添加微信好友"""
        try:
            if not WECHAT_AUTOMATION_AVAILABLE:
                return {
                    "success": False,
                    "message": "微信自动化模块不可用，请检查依赖安装"
                }

            # 获取参数
            wechat_id = params.get('wechat_id', '').strip()
            verify_msg = params.get('verify_msg', '').strip()
            remark_name = params.get('remark_name', '').strip()

            if not wechat_id:
                return {
                    "success": False,
                    "message": "微信号不能为空"
                }

            # 初始化微信自动化
            wechat = self.__init_wechat_automation()
            if not wechat:
                return {
                    "success": False,
                    "message": "无法连接到微信，请确保微信PC版已打开并登录"
                }

            # 执行添加好友操作
            add_console_log(f"开始添加好友: {wechat_id}", "info")

            result = wechat.add_friend(
                search_keyword=wechat_id,
                verify_msg=verify_msg if verify_msg else None,
                remark_name=remark_name if remark_name else None
            )

            # 添加10秒休眠时间
            import time
            time.sleep(10)
            add_console_log("等待10秒后继续...", "info")

            # 处理返回结果
            if isinstance(result, dict):
                success = result.get('success', False)
                message = result.get('message', '')
                user_not_found = result.get('user_not_found', False)
            else:
                # 兼容旧的布尔返回值
                success = bool(result)
                message = ''
                user_not_found = False

            # 记录操作日志到数据库
            try:
                # 查找用户ID
                user_id = None
                if query_users:
                    users = query_users()
                    for user in users:
                        if user.get('unique_id') == wechat_id:
                            user_id = user.get('id')
                            break

                # 查找截图路径
                screenshot_path = None
                try:
                    import os
                    import glob
                    screenshot_dir = "screenshots"
                    if os.path.exists(screenshot_dir):
                        pattern = os.path.join(screenshot_dir, f"*{wechat_id}*添加好友*.png")
                        files = glob.glob(pattern)
                        if files:
                            latest_file = max(files, key=os.path.getctime)
                            screenshot_path = latest_file
                except:
                    pass

                # 记录到数据库
                if add_user_log:
                    status = 1 if success else 0
                    error_msg = message if not success else None
                    add_user_log(
                        wechat_id=wechat_id,
                        user_id=user_id,
                        status=status,
                        img_path=screenshot_path,
                        file_name="wechat_automation",
                        verify_msg=verify_msg,
                        error_msg=error_msg
                    )
                    add_console_log(f"📝 操作日志已记录到数据库", "info")
            except Exception as e:
                add_console_log(f"记录操作日志失败: {e}", "warning")

            if success:
                add_console_log(f"✅ 成功发送好友申请给: {wechat_id}", "success")
                return {
                    "success": True,
                    "message": message or "好友申请发送成功",
                    "wechat_id": wechat_id,
                    "verify_msg": verify_msg,
                    "remark_name": remark_name,
                    "screenshot_path": screenshot_path
                }
            else:
                if user_not_found:
                    add_console_log(f"⚠️ 无法找到用户: {wechat_id}", "warning")
                    return {
                        "success": False,
                        "message": message or "无法找到该用户，请检查微信号是否正确",
                        "user_not_found": True
                    }
                else:
                    add_console_log(f"❌ 添加好友失败: {wechat_id} - {message}", "error")
                    return {
                        "success": False,
                        "message": message or "添加好友失败，请检查微信号是否正确或网络连接"
                    }

        except Exception as e:
            error_msg = f"添加微信好友失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }

    # ==================== 常用语管理相关方法 ====================

    def get_wechat_phrases(self):
        """获取微信常用语列表"""
        try:
            if not query_wechat_phrases:
                return {
                    "success": False,
                    "message": "数据库模块不可用"
                }

            phrases = query_wechat_phrases()
            return {
                "success": True,
                "data": phrases,
                "message": f"获取到 {len(phrases)} 条常用语"
            }

        except Exception as e:
            error_msg = f"获取常用语失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }

    def add_wechat_phrase(self, params):
        """添加微信常用语"""
        try:
            # 导入函数时使用不同的名称避免冲突
            from sqlite3_util import add_wechat_phrase as db_add_phrase

            content = params.get('content', '').strip()
            if not content:
                return {
                    "success": False,
                    "message": "常用语内容不能为空"
                }

            success = db_add_phrase(content=content)
            if success:
                add_console_log(f"✅ 添加常用语成功: {content[:20]}...", "success")
                return {
                    "success": True,
                    "message": "添加成功"
                }
            else:
                return {
                    "success": False,
                    "message": "添加失败"
                }

        except Exception as e:
            error_msg = f"添加常用语失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }

    def update_wechat_phrase(self, params):
        """更新微信常用语"""
        try:
            # 导入函数时使用不同的名称避免冲突
            from sqlite3_util import update_wechat_phrase as db_update_phrase

            phrase_id = params.get('id')
            content = params.get('content', '').strip()
            status = params.get('status')

            if phrase_id is None:
                return {
                    "success": False,
                    "message": "常用语ID不能为空"
                }

            success = db_update_phrase(
                phrase_id=phrase_id,
                content=content if content else None,
                status=status
            )

            if success:
                action = "更新内容" if content else "更新状态"
                add_console_log(f"✅ {action}成功: ID {phrase_id}", "success")
                return {
                    "success": True,
                    "message": "更新成功"
                }
            else:
                return {
                    "success": False,
                    "message": "更新失败"
                }

        except Exception as e:
            error_msg = f"更新常用语失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }

    def delete_wechat_phrase(self, params):
        """删除微信常用语"""
        try:
            # 导入函数时使用不同的名称避免冲突
            from sqlite3_util import delete_wechat_phrase as db_delete_phrase

            phrase_id = params.get('id')
            if phrase_id is None:
                return {
                    "success": False,
                    "message": "常用语ID不能为空"
                }

            success = db_delete_phrase(phrase_id=phrase_id)
            if success:
                add_console_log(f"✅ 删除常用语成功: ID {phrase_id}", "success")
                return {
                    "success": True,
                    "message": "删除成功"
                }
            else:
                return {
                    "success": False,
                    "message": "删除失败"
                }

        except Exception as e:
            error_msg = f"删除常用语失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }

    def get_users(self, params=None):
        """获取用户数据"""
        try:
            if not query_users:
                return {
                    "success": False,
                    "message": "数据库模块不可用"
                }

            # 获取参数
            limit = None
            if params:
                limit = params.get('limit')

            users = query_users(limit=limit)
            add_console_log(f"✅ 获取到 {len(users)} 条用户数据", "success")

            return {
                "success": True,
                "data": users,
                "message": f"获取到 {len(users)} 条用户数据"
            }

        except Exception as e:
            error_msg = f"获取用户数据失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }

    def get_user_logs(self, params=None):
        """获取用户操作日志"""
        try:
            if not query_user_logs:
                return {
                    "success": False,
                    "message": "数据库模块不可用"
                }

            # 获取参数
            limit = 100
            if params:
                limit = params.get('limit', 100)

            logs = query_user_logs(limit=limit)
            add_console_log(f"✅ 获取到 {len(logs)} 条操作日志", "success")

            return {
                "success": True,
                "data": logs,
                "message": f"获取到 {len(logs)} 条操作日志"
            }

        except Exception as e:
            error_msg = f"获取操作日志失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }

    def clear_user_logs(self, params=None):
        """清空用户操作日志"""
        try:
            if not query_user_logs:
                return {
                    "success": False,
                    "message": "数据库模块不可用"
                }

            # 这里需要实现清空日志的功能
            # 暂时返回成功，实际需要在sqlite3_util中添加清空方法
            add_console_log("✅ 操作日志已清空", "success")

            return {
                "success": True,
                "message": "操作日志已清空"
            }

        except Exception as e:
            error_msg = f"清空操作日志失败: {str(e)}"
            add_console_log(f"❌ {error_msg}", "error")
            return {
                "success": False,
                "message": error_msg
            }
